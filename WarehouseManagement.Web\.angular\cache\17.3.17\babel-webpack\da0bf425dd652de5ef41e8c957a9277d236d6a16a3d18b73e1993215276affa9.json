{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputGroupAddon displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nconst _c0 = [\"*\"];\nlet InputGroupAddon = /*#__PURE__*/(() => {\n  class InputGroupAddon {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    static ɵfac = function InputGroupAddon_Factory(t) {\n      return new (t || InputGroupAddon)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: InputGroupAddon,\n      selectors: [[\"p-inputGroupAddon\"]],\n      hostAttrs: [1, \"p-element\", \"p-inputgroup-addon\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\"\n      },\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 3,\n      consts: [[3, \"ngClass\", \"ngStyle\"]],\n      template: function InputGroupAddon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.styleClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"inputgroupaddon\");\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      encapsulation: 2\n    });\n  }\n  return InputGroupAddon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputGroupAddonModule = /*#__PURE__*/(() => {\n  class InputGroupAddonModule {\n    static ɵfac = function InputGroupAddonModule_Factory(t) {\n      return new (t || InputGroupAddonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputGroupAddonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return InputGroupAddonModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroupAddon, InputGroupAddonModule };\n//# sourceMappingURL=primeng-inputgroupaddon.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}