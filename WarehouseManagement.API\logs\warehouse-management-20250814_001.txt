2025-08-14 15:24:02.605 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 15:24:03.474 +03:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 15:24:03.521 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 15:24:03.528 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 15:24:03.533 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 15:24:03.572 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 15:24:03.596 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 15:24:03.599 +03:00 [INF] Database migrations applied successfully
2025-08-14 15:24:03.606 +03:00 [INF] Starting authentication data seeding...
2025-08-14 15:24:04.129 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:24:04.146 +03:00 [INF] Permissions already exist, skipping seeding
2025-08-14 15:24:04.156 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:24:04.159 +03:00 [INF] Roles already exist, skipping seeding
2025-08-14 15:24:04.190 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:24:04.192 +03:00 [INF] Admin user already exists, skipping seeding
2025-08-14 15:24:04.202 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:24:04.204 +03:00 [INF] Role permissions already exist, skipping seeding
2025-08-14 15:24:04.211 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserRoles] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:24:04.213 +03:00 [INF] User roles already exist, skipping seeding
2025-08-14 15:24:04.214 +03:00 [INF] Authentication data seeding completed successfully
2025-08-14 15:24:04.215 +03:00 [INF] Database seeding completed successfully
2025-08-14 15:24:04.219 +03:00 [INF] Starting Warehouse Management API
2025-08-14 15:24:04.241 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 15:24:04.466 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 15:24:04.468 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 15:24:04.542 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 15:24:04.544 +03:00 [INF] Hosting environment: Development
2025-08-14 15:24:04.545 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 15:30:55.351 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 15:30:55.977 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 15:30:56.019 +03:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 15:30:56.026 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 15:30:56.030 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 15:30:56.053 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 15:30:56.068 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 15:30:56.070 +03:00 [INF] Database migrations applied successfully
2025-08-14 15:30:56.074 +03:00 [INF] Starting authentication data seeding...
2025-08-14 15:30:56.337 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:30:56.352 +03:00 [INF] Permissions already exist, skipping seeding
2025-08-14 15:30:56.359 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:30:56.362 +03:00 [INF] Roles already exist, skipping seeding
2025-08-14 15:30:56.385 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:30:56.388 +03:00 [INF] Admin user already exists, skipping seeding
2025-08-14 15:30:56.396 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:30:56.399 +03:00 [INF] Role permissions already exist, skipping seeding
2025-08-14 15:30:56.405 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserRoles] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 15:30:56.407 +03:00 [INF] User roles already exist, skipping seeding
2025-08-14 15:30:56.409 +03:00 [INF] Authentication data seeding completed successfully
2025-08-14 15:30:56.410 +03:00 [INF] Database seeding completed successfully
2025-08-14 15:30:56.414 +03:00 [INF] Starting Warehouse Management API
2025-08-14 15:30:56.440 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 15:30:56.656 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 15:30:56.658 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 15:30:56.722 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 15:30:56.724 +03:00 [INF] Hosting environment: Development
2025-08-14 15:30:56.725 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 15:30:57.374 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 15:30:57.591 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 221.9611ms
2025-08-14 15:30:57.615 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - null null
2025-08-14 15:30:57.625 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_vs/browserLink - null null
2025-08-14 15:30:57.626 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - 200 16531 application/javascript; charset=utf-8 10.3498ms
2025-08-14 15:30:58.002 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_vs/browserLink - 200 null text/javascript; charset=UTF-8 376.22ms
2025-08-14 15:30:58.050 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 15:30:58.078 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 27.9575ms
2025-08-14 15:31:44.321 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/Auth/login - application/json 74
2025-08-14 15:31:44.330 +03:00 [INF] CORS policy execution failed.
2025-08-14 15:31:44.332 +03:00 [INF] Request origin https://localhost:53878 does not have permission to access the resource.
2025-08-14 15:31:44.408 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 15:31:44.430 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 15:31:57.472 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 15:32:29.391 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 15:32:29.403 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 44965.1001ms
2025-08-14 15:32:29.405 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 15:32:29.411 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/Auth/login - 400 null application/json; charset=utf-8 45089.5052ms
2025-08-14 16:07:07.311 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 16:07:07.872 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 16:07:07.897 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 16:07:07.903 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 16:07:07.906 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 16:07:07.922 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 16:07:07.938 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 16:07:07.940 +03:00 [INF] Database migrations applied successfully
2025-08-14 16:07:07.945 +03:00 [INF] Starting authentication data seeding...
2025-08-14 16:07:08.178 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 16:07:08.190 +03:00 [INF] Permissions already exist, skipping seeding
2025-08-14 16:07:08.198 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 16:07:08.201 +03:00 [INF] Roles already exist, skipping seeding
2025-08-14 16:07:08.223 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 16:07:08.226 +03:00 [INF] Admin user already exists, skipping seeding
2025-08-14 16:07:08.234 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 16:07:08.237 +03:00 [INF] Role permissions already exist, skipping seeding
2025-08-14 16:07:08.243 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserRoles] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 16:07:08.247 +03:00 [INF] User roles already exist, skipping seeding
2025-08-14 16:07:08.248 +03:00 [INF] Authentication data seeding completed successfully
2025-08-14 16:07:08.249 +03:00 [INF] Database seeding completed successfully
2025-08-14 16:07:08.253 +03:00 [INF] Starting Warehouse Management API
2025-08-14 16:07:08.264 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 16:07:08.360 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 16:07:08.362 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 16:07:08.363 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 16:07:08.365 +03:00 [INF] Hosting environment: Development
2025-08-14 16:07:08.365 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 16:07:11.969 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 16:07:12.085 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 117.1238ms
2025-08-14 16:07:12.085 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 16:07:12.100 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 14.9741ms
2025-08-14 16:07:12.331 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 16:07:12.500 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 169.2993ms
2025-08-14 16:07:12.502 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 16:07:12.525 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 22.9968ms
2025-08-14 16:07:38.089 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/api/Auth/get-hash?password=Admin%40123 - null null
2025-08-14 16:07:38.124 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.GeytHash (WarehouseManagement.API)'
2025-08-14 16:07:38.143 +03:00 [INF] Route matched with {action = "GeytHash", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GeytHash(System.String, System.String) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 16:07:38.185 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-08-14 16:07:38.206 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.GeytHash (WarehouseManagement.API) in 57.2532ms
2025-08-14 16:07:38.208 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.GeytHash (WarehouseManagement.API)'
2025-08-14 16:07:38.211 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/api/Auth/get-hash?password=Admin%40123 - 400 null application/problem+json; charset=utf-8 122.5632ms
2025-08-14 16:10:20.116 +03:00 [INF] Application is shutting down...
