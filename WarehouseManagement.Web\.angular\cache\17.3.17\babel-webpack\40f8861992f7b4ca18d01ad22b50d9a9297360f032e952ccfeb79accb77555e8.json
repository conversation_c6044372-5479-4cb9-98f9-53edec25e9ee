{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.isRefreshing = false;\n      this.refreshTokenSubject = new BehaviorSubject(null);\n    }\n    intercept(request, next) {\n      // Skip authentication for certain endpoints\n      if (this.shouldSkipAuth(request)) {\n        return next.handle(request);\n      }\n      // Add credentials for cookie-based authentication\n      const authRequest = request.clone({\n        setHeaders: {\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        },\n        withCredentials: true\n      });\n      return next.handle(authRequest).pipe(tap(event => {\n        // Handle successful responses\n        if (event instanceof HttpResponse) {\n          this.handleSuccessfulResponse(event);\n        }\n      }), catchError(error => {\n        return this.handleErrorResponse(error, authRequest, next);\n      }));\n    }\n    shouldSkipAuth(request) {\n      const skipAuthUrls = ['/auth/login', '/auth/refresh', '/auth/logout', '/public/', '/assets/', '.json'];\n      return skipAuthUrls.some(url => request.url.includes(url));\n    }\n    handleSuccessfulResponse(response) {\n      // Handle rate limiting headers\n      if (response.headers.has('X-RateLimit-Remaining')) {\n        const remaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0', 10);\n        const limit = parseInt(response.headers.get('X-RateLimit-Limit') || '0', 10);\n        const resetTime = response.headers.get('X-RateLimit-Reset');\n        if (remaining < limit * 0.1) {\n          // Less than 10% remaining\n          console.warn(`Rate limit warning: ${remaining}/${limit} requests remaining`);\n        }\n      }\n      // Handle CSRF token updates\n      if (response.headers.has('X-CSRF-Token')) {\n        const csrfToken = response.headers.get('X-CSRF-Token');\n        if (csrfToken) {\n          // Store CSRF token for future requests\n          sessionStorage.setItem('csrf-token', csrfToken);\n        }\n      }\n    }\n    handleErrorResponse(error, request, next) {\n      // Handle different error status codes\n      switch (error.status) {\n        case 401:\n          return this.handle401Error(request, next);\n        case 403:\n          return this.handle403Error(error);\n        case 429:\n          return this.handle429Error(error);\n        case 419:\n          // CSRF token mismatch\n          return this.handle419Error(error);\n        default:\n          return this.handleGenericError(error);\n      }\n    }\n    handle401Error(request, next) {\n      if (!this.isRefreshing) {\n        this.isRefreshing = true;\n        this.refreshTokenSubject.next(null);\n        return this.authService.refreshToken().pipe(switchMap(() => {\n          this.isRefreshing = false;\n          this.refreshTokenSubject.next(true);\n          // Retry the original request\n          return next.handle(request);\n        }), catchError(refreshError => {\n          this.isRefreshing = false;\n          this.refreshTokenSubject.next(null);\n          // Refresh failed, redirect to login\n          this.authService.logout().subscribe();\n          this.router.navigate(['/login']);\n          return throwError(() => refreshError);\n        }));\n      } else {\n        // Wait for refresh to complete\n        return this.refreshTokenSubject.pipe(filter(result => result !== null), take(1), switchMap(() => next.handle(request)));\n      }\n    }\n    handle403Error(error) {\n      // Forbidden - user doesn't have permission\n      console.error('Access forbidden:', error.message);\n      // Don't redirect automatically, let the component handle it\n      // The route guards should prevent most 403 errors\n      return throwError(() => error);\n    }\n    handle429Error(error) {\n      // Rate limit exceeded\n      const retryAfter = error.headers.get('Retry-After');\n      const retryAfterMs = retryAfter ? parseInt(retryAfter, 10) * 1000 : 5000;\n      console.warn(`Rate limit exceeded. Retry after ${retryAfterMs}ms`);\n      // You could implement automatic retry with exponential backoff here\n      // For now, just pass the error through\n      return throwError(() => error);\n    }\n    handle419Error(error) {\n      // CSRF token mismatch - clear stored token and let user retry\n      sessionStorage.removeItem('csrf-token');\n      console.error('CSRF token mismatch:', error.message);\n      return throwError(() => error);\n    }\n    handleGenericError(error) {\n      // Log error for debugging\n      console.error('HTTP Error:', {\n        status: error.status,\n        message: error.message,\n        url: error.url,\n        error: error.error\n      });\n      // Handle network errors\n      if (error.status === 0) {\n        console.error('Network error - check your internet connection');\n      }\n      // Handle server errors\n      if (error.status >= 500) {\n        console.error('Server error - please try again later');\n      }\n      return throwError(() => error);\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();\nexport let CsrfInterceptor = /*#__PURE__*/(() => {\n  class CsrfInterceptor {\n    intercept(request, next) {\n      // Only add CSRF token to state-changing requests\n      if (this.shouldAddCsrfToken(request)) {\n        const csrfToken = sessionStorage.getItem('csrf-token');\n        if (csrfToken) {\n          const csrfRequest = request.clone({\n            setHeaders: {\n              'X-CSRF-Token': csrfToken\n            }\n          });\n          return next.handle(csrfRequest);\n        }\n      }\n      return next.handle(request);\n    }\n    shouldAddCsrfToken(request) {\n      // Add CSRF token to POST, PUT, PATCH, DELETE requests\n      const stateMutatingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];\n      return stateMutatingMethods.includes(request.method.toUpperCase());\n    }\n    static {\n      this.ɵfac = function CsrfInterceptor_Factory(t) {\n        return new (t || CsrfInterceptor)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CsrfInterceptor,\n        factory: CsrfInterceptor.ɵfac\n      });\n    }\n  }\n  return CsrfInterceptor;\n})();\nexport let SecurityHeadersInterceptor = /*#__PURE__*/(() => {\n  class SecurityHeadersInterceptor {\n    intercept(request, next) {\n      // Add security headers to all requests\n      const secureRequest = request.clone({\n        setHeaders: {\n          'X-Content-Type-Options': 'nosniff',\n          'X-Frame-Options': 'DENY',\n          'X-XSS-Protection': '1; mode=block',\n          'Referrer-Policy': 'strict-origin-when-cross-origin'\n        }\n      });\n      return next.handle(secureRequest);\n    }\n    static {\n      this.ɵfac = function SecurityHeadersInterceptor_Factory(t) {\n        return new (t || SecurityHeadersInterceptor)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SecurityHeadersInterceptor,\n        factory: SecurityHeadersInterceptor.ɵfac\n      });\n    }\n  }\n  return SecurityHeadersInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}