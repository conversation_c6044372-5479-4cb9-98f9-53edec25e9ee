{"ast": null, "code": "import { BehaviorSubject, throwError, of } from 'rxjs';\nimport { map, catchError, tap, finalize } from 'rxjs/operators';\nimport { SecurityEventType, SecurityEventSeverity } from '../models/auth.models';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.API_URL = environment.apiUrl;\n      this.TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiration\n      this.authStateSubject = new BehaviorSubject({\n        isAuthenticated: false,\n        user: null,\n        loading: false,\n        error: null\n      });\n      this.authState$ = this.authStateSubject.asObservable();\n      this.initializeAuth();\n    }\n    /**\n     * Initialize authentication state on service creation\n     */\n    initializeAuth() {\n      this.updateAuthState({\n        loading: true\n      });\n      // Check if user is already authenticated\n      this.validateCurrentSession().subscribe({\n        next: isValid => {\n          if (isValid) {\n            this.getCurrentUser().subscribe({\n              next: user => {\n                this.updateAuthState({\n                  isAuthenticated: true,\n                  user,\n                  loading: false,\n                  error: null\n                });\n                this.scheduleTokenRefresh();\n              },\n              error: () => {\n                this.clearAuthState();\n              }\n            });\n          } else {\n            this.clearAuthState();\n          }\n        },\n        error: () => {\n          this.clearAuthState();\n        }\n      });\n    }\n    /**\n     * Login user with credentials\n     */\n    login(credentials) {\n      this.updateAuthState({\n        loading: true,\n        error: null\n      });\n      return this.http.post(`${this.API_URL}/auth/login`, credentials, {\n        withCredentials: true // Important for httpOnly cookies\n      }).pipe(map(response => {\n        if (!response.success || !response.data) {\n          throw new Error(response.message || 'Login failed');\n        }\n        return response.data;\n      }), tap(loginResponse => {\n        this.updateAuthState({\n          isAuthenticated: true,\n          user: loginResponse.user,\n          loading: false,\n          error: null\n        });\n        this.scheduleTokenRefresh();\n        this.logSecurityEvent(SecurityEventType.LOGIN_SUCCESS, 'User logged in successfully');\n      }), catchError(error => {\n        this.updateAuthState({\n          isAuthenticated: false,\n          user: null,\n          loading: false,\n          error: this.getErrorMessage(error)\n        });\n        this.logSecurityEvent(SecurityEventType.LOGIN_FAILURE, `Login failed: ${this.getErrorMessage(error)}`, SecurityEventSeverity.MEDIUM);\n        return throwError(() => error);\n      }));\n    }\n    /**\n     * Logout user and clear session\n     */\n    logout() {\n      return this.http.post(`${this.API_URL}/auth/logout`, {}, {\n        withCredentials: true\n      }).pipe(tap(() => {\n        this.logSecurityEvent(SecurityEventType.LOGOUT, 'User logged out');\n      }), map(() => void 0),\n      // Convert to void\n      catchError(error => {\n        console.error('Logout error:', error);\n        // Even if logout fails on server, clear local state\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n        return of(void 0);\n      }), finalize(() => {\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n      }));\n    }\n    /**\n     * Refresh authentication token\n     */\n    refreshToken() {\n      return this.http.post(`${this.API_URL}/auth/refresh`, {}, {\n        withCredentials: true\n      }).pipe(map(response => {\n        if (!response.success || !response.data) {\n          throw new Error('Token refresh failed');\n        }\n        return response.data;\n      }), tap(() => {\n        this.scheduleTokenRefresh();\n        this.logSecurityEvent(SecurityEventType.TOKEN_REFRESH, 'Token refreshed successfully');\n      }), catchError(error => {\n        console.error('Token refresh failed:', error);\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n        return throwError(() => error);\n      }));\n    }\n    /**\n     * Get current user information\n     */\n    getCurrentUser() {\n      return this.http.get(`${this.API_URL}/auth/me`, {\n        withCredentials: true\n      }).pipe(map(response => {\n        if (!response.success || !response.data) {\n          throw new Error('Failed to get user information');\n        }\n        return response.data;\n      }));\n    }\n    /**\n     * Validate current session\n     */\n    validateCurrentSession() {\n      return this.http.get(`${this.API_URL}/auth/validate`, {\n        withCredentials: true\n      }).pipe(map(response => response.success && response.data?.valid === true), catchError(() => {\n        return throwError(() => false);\n      }));\n    }\n    /**\n     * Check if user has specific permission\n     */\n    hasPermission(resource, action) {\n      const user = this.authStateSubject.value.user;\n      if (!user) return false;\n      // Admin has all permissions\n      if (user.roles.some(role => role.name === 'admin')) {\n        return true;\n      }\n      // Check specific permissions\n      return user.permissions.some(permission => permission.resource === resource && permission.action === action);\n    }\n    /**\n     * Check if user has specific role\n     */\n    hasRole(roleName) {\n      const user = this.authStateSubject.value.user;\n      return user?.roles.some(role => role.name === roleName) || false;\n    }\n    /**\n     * Get current authentication state\n     */\n    getAuthState() {\n      return this.authStateSubject.value;\n    }\n    /**\n     * Check if user is authenticated\n     */\n    isAuthenticated() {\n      return this.authStateSubject.value.isAuthenticated;\n    }\n    /**\n     * Get current user\n     */\n    getCurrentUserSync() {\n      return this.authStateSubject.value.user;\n    }\n    /**\n     * Schedule automatic token refresh\n     */\n    scheduleTokenRefresh() {\n      if (this.refreshTokenTimer) {\n        clearTimeout(this.refreshTokenTimer);\n      }\n      // Schedule refresh 5 minutes before token expiration\n      this.refreshTokenTimer = setTimeout(() => {\n        this.refreshToken().subscribe({\n          error: error => {\n            console.error('Automatic token refresh failed:', error);\n          }\n        });\n      }, this.TOKEN_REFRESH_THRESHOLD);\n    }\n    /**\n     * Update authentication state\n     */\n    updateAuthState(updates) {\n      const currentState = this.authStateSubject.value;\n      this.authStateSubject.next({\n        ...currentState,\n        ...updates\n      });\n    }\n    /**\n     * Clear authentication state\n     */\n    clearAuthState() {\n      if (this.refreshTokenTimer) {\n        clearTimeout(this.refreshTokenTimer);\n      }\n      this.authStateSubject.next({\n        isAuthenticated: false,\n        user: null,\n        loading: false,\n        error: null\n      });\n    }\n    /**\n     * Log security events\n     */\n    logSecurityEvent(eventType, description, severity = SecurityEventSeverity.LOW) {\n      const event = {\n        eventType,\n        description,\n        severity,\n        timestamp: new Date()\n      };\n      // Send to backend for logging\n      this.http.post(`${this.API_URL}/security/events`, event, {\n        withCredentials: true\n      }).subscribe({\n        error: error => console.error('Failed to log security event:', error)\n      });\n    }\n    /**\n     * Extract error message from HTTP error\n     */\n    getErrorMessage(error) {\n      if (error?.error?.message) {\n        return error.error.message;\n      }\n      if (error?.message) {\n        return error.message;\n      }\n      return 'An unexpected error occurred';\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}