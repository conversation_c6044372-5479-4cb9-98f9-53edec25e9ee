using System.ComponentModel.DataAnnotations;

namespace WarehouseManagement.Core.Models;

public class ItemDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? Barcode { get; set; }
    public int CategoryId { get; set; }
    public string CategoryName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public decimal CostPrice { get; set; }
    public string Unit { get; set; } = string.Empty;
    public int MinimumStock { get; set; }
    public int MaximumStock { get; set; }
    public int ReorderLevel { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public decimal CurrentStock { get; set; }
    public string? ImageUrl { get; set; }
}

public class CreateItemRequest
{
    [Required]
    [StringLength(50)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? Barcode { get; set; }

    [Required]
    public int CategoryId { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal UnitPrice { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal CostPrice { get; set; }

    [Required]
    [StringLength(20)]
    public string Unit { get; set; } = string.Empty;

    [Range(0, int.MaxValue)]
    public int MinimumStock { get; set; } = 0;

    [Range(0, int.MaxValue)]
    public int MaximumStock { get; set; } = 0;

    [Range(0, int.MaxValue)]
    public int ReorderLevel { get; set; } = 0;

    public bool IsActive { get; set; } = true;

    public string? ImageUrl { get; set; }
}

public class UpdateItemRequest
{
    [StringLength(200)]
    public string? Name { get; set; }

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? Barcode { get; set; }

    public int? CategoryId { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? UnitPrice { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? CostPrice { get; set; }

    [StringLength(20)]
    public string? Unit { get; set; }

    [Range(0, int.MaxValue)]
    public int? MinimumStock { get; set; }

    [Range(0, int.MaxValue)]
    public int? MaximumStock { get; set; }

    [Range(0, int.MaxValue)]
    public int? ReorderLevel { get; set; }

    public bool? IsActive { get; set; }

    public string? ImageUrl { get; set; }
}
