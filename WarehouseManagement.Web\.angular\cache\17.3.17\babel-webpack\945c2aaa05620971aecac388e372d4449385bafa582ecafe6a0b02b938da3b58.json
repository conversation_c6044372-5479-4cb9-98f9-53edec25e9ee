{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthInterceptor {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isRefreshing = false;\n    this.refreshTokenSubject = new BehaviorSubject(null);\n  }\n  intercept(request, next) {\n    // Skip authentication for certain endpoints\n    if (this.shouldSkipAuth(request)) {\n      return next.handle(request);\n    }\n    // Add credentials for cookie-based authentication\n    const authRequest = request.clone({\n      setHeaders: {\n        'Content-Type': 'application/json',\n        'X-Requested-With': 'XMLHttpRequest'\n      },\n      withCredentials: true\n    });\n    return next.handle(authRequest).pipe(tap(event => {\n      // Handle successful responses\n      if (event instanceof HttpResponse) {\n        this.handleSuccessfulResponse(event);\n      }\n    }), catchError(error => {\n      return this.handleErrorResponse(error, authRequest, next);\n    }));\n  }\n  shouldSkipAuth(request) {\n    const skipAuthUrls = ['/auth/login', '/auth/refresh', '/auth/logout', '/public/', '/assets/', '.json'];\n    return skipAuthUrls.some(url => request.url.includes(url));\n  }\n  handleSuccessfulResponse(response) {\n    // Handle rate limiting headers\n    if (response.headers.has('X-RateLimit-Remaining')) {\n      const remaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0', 10);\n      const limit = parseInt(response.headers.get('X-RateLimit-Limit') || '0', 10);\n      const resetTime = response.headers.get('X-RateLimit-Reset');\n      if (remaining < limit * 0.1) {\n        // Less than 10% remaining\n        console.warn(`Rate limit warning: ${remaining}/${limit} requests remaining`);\n      }\n    }\n    // Handle CSRF token updates\n    if (response.headers.has('X-CSRF-Token')) {\n      const csrfToken = response.headers.get('X-CSRF-Token');\n      if (csrfToken) {\n        // Store CSRF token for future requests\n        sessionStorage.setItem('csrf-token', csrfToken);\n      }\n    }\n  }\n  handleErrorResponse(error, request, next) {\n    // Handle different error status codes\n    switch (error.status) {\n      case 401:\n        return this.handle401Error(request, next);\n      case 403:\n        return this.handle403Error(error);\n      case 429:\n        return this.handle429Error(error);\n      case 419:\n        // CSRF token mismatch\n        return this.handle419Error(error);\n      default:\n        return this.handleGenericError(error);\n    }\n  }\n  handle401Error(request, next) {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n      return this.authService.refreshToken().pipe(switchMap(() => {\n        this.isRefreshing = false;\n        this.refreshTokenSubject.next(true);\n        // Retry the original request\n        return next.handle(request);\n      }), catchError(refreshError => {\n        this.isRefreshing = false;\n        this.refreshTokenSubject.next(null);\n        // Refresh failed, redirect to login\n        this.authService.logout().subscribe();\n        this.router.navigate(['/login']);\n        return throwError(() => refreshError);\n      }));\n    } else {\n      // Wait for refresh to complete\n      return this.refreshTokenSubject.pipe(filter(result => result !== null), take(1), switchMap(() => next.handle(request)));\n    }\n  }\n  handle403Error(error) {\n    // Forbidden - user doesn't have permission\n    console.error('Access forbidden:', error.message);\n    // Don't redirect automatically, let the component handle it\n    // The route guards should prevent most 403 errors\n    return throwError(() => error);\n  }\n  handle429Error(error) {\n    // Rate limit exceeded\n    const retryAfter = error.headers.get('Retry-After');\n    const retryAfterMs = retryAfter ? parseInt(retryAfter, 10) * 1000 : 5000;\n    console.warn(`Rate limit exceeded. Retry after ${retryAfterMs}ms`);\n    // You could implement automatic retry with exponential backoff here\n    // For now, just pass the error through\n    return throwError(() => error);\n  }\n  handle419Error(error) {\n    // CSRF token mismatch - clear stored token and let user retry\n    sessionStorage.removeItem('csrf-token');\n    console.error('CSRF token mismatch:', error.message);\n    return throwError(() => error);\n  }\n  handleGenericError(error) {\n    // Log error for debugging\n    console.error('HTTP Error:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      error: error.error\n    });\n    // Handle network errors\n    if (error.status === 0) {\n      console.error('Network error - check your internet connection');\n    }\n    // Handle server errors\n    if (error.status >= 500) {\n      console.error('Server error - please try again later');\n    }\n    return throwError(() => error);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}\nexport class CsrfInterceptor {\n  intercept(request, next) {\n    // Only add CSRF token to state-changing requests\n    if (this.shouldAddCsrfToken(request)) {\n      const csrfToken = sessionStorage.getItem('csrf-token');\n      if (csrfToken) {\n        const csrfRequest = request.clone({\n          setHeaders: {\n            'X-CSRF-Token': csrfToken\n          }\n        });\n        return next.handle(csrfRequest);\n      }\n    }\n    return next.handle(request);\n  }\n  shouldAddCsrfToken(request) {\n    // Add CSRF token to POST, PUT, PATCH, DELETE requests\n    const stateMutatingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];\n    return stateMutatingMethods.includes(request.method.toUpperCase());\n  }\n  static {\n    this.ɵfac = function CsrfInterceptor_Factory(t) {\n      return new (t || CsrfInterceptor)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CsrfInterceptor,\n      factory: CsrfInterceptor.ɵfac\n    });\n  }\n}\nexport class SecurityHeadersInterceptor {\n  intercept(request, next) {\n    // Add security headers to all requests\n    const secureRequest = request.clone({\n      setHeaders: {\n        'X-Content-Type-Options': 'nosniff',\n        'X-Frame-Options': 'DENY',\n        'X-XSS-Protection': '1; mode=block',\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\n      }\n    });\n    return next.handle(secureRequest);\n  }\n  static {\n    this.ɵfac = function SecurityHeadersInterceptor_Factory(t) {\n      return new (t || SecurityHeadersInterceptor)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SecurityHeadersInterceptor,\n      factory: SecurityHeadersInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpResponse", "throwError", "BehaviorSubject", "catchError", "filter", "take", "switchMap", "tap", "AuthInterceptor", "constructor", "authService", "router", "isRefreshing", "refreshTokenSubject", "intercept", "request", "next", "shouldSkipAuth", "handle", "authRequest", "clone", "setHeaders", "withCredentials", "pipe", "event", "handleSuccessfulResponse", "error", "handleErrorResponse", "skipAuthUrls", "some", "url", "includes", "response", "headers", "has", "remaining", "parseInt", "get", "limit", "resetTime", "console", "warn", "csrfToken", "sessionStorage", "setItem", "status", "handle401Error", "handle403Error", "handle429Error", "handle419Error", "handleGenericError", "refreshToken", "refreshError", "logout", "subscribe", "navigate", "result", "message", "retryAfter", "retryAfterMs", "removeItem", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "CsrfInterceptor", "shouldAddCsrfToken", "getItem", "csrfRequest", "stateMutatingMethods", "method", "toUpperCase", "SecurityHeadersInterceptor", "secureRequest"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { \n  HttpInterceptor, \n  HttpRequest, \n  HttpHandler, \n  HttpEvent, \n  HttpErrorResponse,\n  HttpResponse \n} from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject, EMPTY } from 'rxjs';\nimport { catchError, filter, take, switchMap, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  private isRefreshing = false;\n  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Skip authentication for certain endpoints\n    if (this.shouldSkipAuth(request)) {\n      return next.handle(request);\n    }\n\n    // Add credentials for cookie-based authentication\n    const authRequest = request.clone({\n      setHeaders: {\n        'Content-Type': 'application/json',\n        'X-Requested-With': 'XMLHttpRequest'\n      },\n      withCredentials: true\n    });\n\n    return next.handle(authRequest).pipe(\n      tap((event: HttpEvent<any>) => {\n        // Handle successful responses\n        if (event instanceof HttpResponse) {\n          this.handleSuccessfulResponse(event);\n        }\n      }),\n      catchError((error: HttpErrorResponse) => {\n        return this.handleErrorResponse(error, authRequest, next);\n      })\n    );\n  }\n\n  private shouldSkipAuth(request: HttpRequest<any>): boolean {\n    const skipAuthUrls = [\n      '/auth/login',\n      '/auth/refresh',\n      '/auth/logout',\n      '/public/',\n      '/assets/',\n      '.json'\n    ];\n\n    return skipAuthUrls.some(url => request.url.includes(url));\n  }\n\n  private handleSuccessfulResponse(response: HttpResponse<any>): void {\n    // Handle rate limiting headers\n    if (response.headers.has('X-RateLimit-Remaining')) {\n      const remaining = parseInt(response.headers.get('X-RateLimit-Remaining') || '0', 10);\n      const limit = parseInt(response.headers.get('X-RateLimit-Limit') || '0', 10);\n      const resetTime = response.headers.get('X-RateLimit-Reset');\n\n      if (remaining < limit * 0.1) { // Less than 10% remaining\n        console.warn(`Rate limit warning: ${remaining}/${limit} requests remaining`);\n      }\n    }\n\n    // Handle CSRF token updates\n    if (response.headers.has('X-CSRF-Token')) {\n      const csrfToken = response.headers.get('X-CSRF-Token');\n      if (csrfToken) {\n        // Store CSRF token for future requests\n        sessionStorage.setItem('csrf-token', csrfToken);\n      }\n    }\n  }\n\n  private handleErrorResponse(\n    error: HttpErrorResponse, \n    request: HttpRequest<any>, \n    next: HttpHandler\n  ): Observable<HttpEvent<any>> {\n    \n    // Handle different error status codes\n    switch (error.status) {\n      case 401:\n        return this.handle401Error(request, next);\n      \n      case 403:\n        return this.handle403Error(error);\n      \n      case 429:\n        return this.handle429Error(error);\n      \n      case 419: // CSRF token mismatch\n        return this.handle419Error(error);\n      \n      default:\n        return this.handleGenericError(error);\n    }\n  }\n\n  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n\n      return this.authService.refreshToken().pipe(\n        switchMap(() => {\n          this.isRefreshing = false;\n          this.refreshTokenSubject.next(true);\n          \n          // Retry the original request\n          return next.handle(request);\n        }),\n        catchError((refreshError) => {\n          this.isRefreshing = false;\n          this.refreshTokenSubject.next(null);\n          \n          // Refresh failed, redirect to login\n          this.authService.logout().subscribe();\n          this.router.navigate(['/login']);\n          \n          return throwError(() => refreshError);\n        })\n      );\n    } else {\n      // Wait for refresh to complete\n      return this.refreshTokenSubject.pipe(\n        filter(result => result !== null),\n        take(1),\n        switchMap(() => next.handle(request))\n      );\n    }\n  }\n\n  private handle403Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {\n    // Forbidden - user doesn't have permission\n    console.error('Access forbidden:', error.message);\n    \n    // Don't redirect automatically, let the component handle it\n    // The route guards should prevent most 403 errors\n    \n    return throwError(() => error);\n  }\n\n  private handle429Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {\n    // Rate limit exceeded\n    const retryAfter = error.headers.get('Retry-After');\n    const retryAfterMs = retryAfter ? parseInt(retryAfter, 10) * 1000 : 5000;\n    \n    console.warn(`Rate limit exceeded. Retry after ${retryAfterMs}ms`);\n    \n    // You could implement automatic retry with exponential backoff here\n    // For now, just pass the error through\n    \n    return throwError(() => error);\n  }\n\n  private handle419Error(error: HttpErrorResponse): Observable<HttpEvent<any>> {\n    // CSRF token mismatch - clear stored token and let user retry\n    sessionStorage.removeItem('csrf-token');\n    \n    console.error('CSRF token mismatch:', error.message);\n    \n    return throwError(() => error);\n  }\n\n  private handleGenericError(error: HttpErrorResponse): Observable<HttpEvent<any>> {\n    // Log error for debugging\n    console.error('HTTP Error:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      error: error.error\n    });\n\n    // Handle network errors\n    if (error.status === 0) {\n      console.error('Network error - check your internet connection');\n    }\n\n    // Handle server errors\n    if (error.status >= 500) {\n      console.error('Server error - please try again later');\n    }\n\n    return throwError(() => error);\n  }\n}\n\n@Injectable()\nexport class CsrfInterceptor implements HttpInterceptor {\n  \n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Only add CSRF token to state-changing requests\n    if (this.shouldAddCsrfToken(request)) {\n      const csrfToken = sessionStorage.getItem('csrf-token');\n      \n      if (csrfToken) {\n        const csrfRequest = request.clone({\n          setHeaders: {\n            'X-CSRF-Token': csrfToken\n          }\n        });\n        \n        return next.handle(csrfRequest);\n      }\n    }\n\n    return next.handle(request);\n  }\n\n  private shouldAddCsrfToken(request: HttpRequest<any>): boolean {\n    // Add CSRF token to POST, PUT, PATCH, DELETE requests\n    const stateMutatingMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];\n    return stateMutatingMethods.includes(request.method.toUpperCase());\n  }\n}\n\n@Injectable()\nexport class SecurityHeadersInterceptor implements HttpInterceptor {\n  \n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Add security headers to all requests\n    const secureRequest = request.clone({\n      setHeaders: {\n        'X-Content-Type-Options': 'nosniff',\n        'X-Frame-Options': 'DENY',\n        'X-XSS-Protection': '1; mode=block',\n        'Referrer-Policy': 'strict-origin-when-cross-origin'\n      }\n    });\n\n    return next.handle(secureRequest);\n  }\n}\n"], "mappings": "AACA,SAMEA,YAAY,QACP,sBAAsB;AAC7B,SAAqBC,UAAU,EAAEC,eAAe,QAAe,MAAM;AACrE,SAASC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAKzE,OAAM,MAAOC,eAAe;EAI1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALR,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAyB,IAAIX,eAAe,CAAM,IAAI,CAAC;EAK/E;EAEHY,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,IAAI,CAACC,cAAc,CAACF,OAAO,CAAC,EAAE;MAChC,OAAOC,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC;;IAG7B;IACA,MAAMI,WAAW,GAAGJ,OAAO,CAACK,KAAK,CAAC;MAChCC,UAAU,EAAE;QACV,cAAc,EAAE,kBAAkB;QAClC,kBAAkB,EAAE;OACrB;MACDC,eAAe,EAAE;KAClB,CAAC;IAEF,OAAON,IAAI,CAACE,MAAM,CAACC,WAAW,CAAC,CAACI,IAAI,CAClChB,GAAG,CAAEiB,KAAqB,IAAI;MAC5B;MACA,IAAIA,KAAK,YAAYxB,YAAY,EAAE;QACjC,IAAI,CAACyB,wBAAwB,CAACD,KAAK,CAAC;;IAExC,CAAC,CAAC,EACFrB,UAAU,CAAEuB,KAAwB,IAAI;MACtC,OAAO,IAAI,CAACC,mBAAmB,CAACD,KAAK,EAAEP,WAAW,EAAEH,IAAI,CAAC;IAC3D,CAAC,CAAC,CACH;EACH;EAEQC,cAAcA,CAACF,OAAyB;IAC9C,MAAMa,YAAY,GAAG,CACnB,aAAa,EACb,eAAe,EACf,cAAc,EACd,UAAU,EACV,UAAU,EACV,OAAO,CACR;IAED,OAAOA,YAAY,CAACC,IAAI,CAACC,GAAG,IAAIf,OAAO,CAACe,GAAG,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC;EAC5D;EAEQL,wBAAwBA,CAACO,QAA2B;IAC1D;IACA,IAAIA,QAAQ,CAACC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,EAAE;MACjD,MAAMC,SAAS,GAAGC,QAAQ,CAACJ,QAAQ,CAACC,OAAO,CAACI,GAAG,CAAC,uBAAuB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MACpF,MAAMC,KAAK,GAAGF,QAAQ,CAACJ,QAAQ,CAACC,OAAO,CAACI,GAAG,CAAC,mBAAmB,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MAC5E,MAAME,SAAS,GAAGP,QAAQ,CAACC,OAAO,CAACI,GAAG,CAAC,mBAAmB,CAAC;MAE3D,IAAIF,SAAS,GAAGG,KAAK,GAAG,GAAG,EAAE;QAAE;QAC7BE,OAAO,CAACC,IAAI,CAAC,uBAAuBN,SAAS,IAAIG,KAAK,qBAAqB,CAAC;;;IAIhF;IACA,IAAIN,QAAQ,CAACC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE;MACxC,MAAMQ,SAAS,GAAGV,QAAQ,CAACC,OAAO,CAACI,GAAG,CAAC,cAAc,CAAC;MACtD,IAAIK,SAAS,EAAE;QACb;QACAC,cAAc,CAACC,OAAO,CAAC,YAAY,EAAEF,SAAS,CAAC;;;EAGrD;EAEQf,mBAAmBA,CACzBD,KAAwB,EACxBX,OAAyB,EACzBC,IAAiB;IAGjB;IACA,QAAQU,KAAK,CAACmB,MAAM;MAClB,KAAK,GAAG;QACN,OAAO,IAAI,CAACC,cAAc,CAAC/B,OAAO,EAAEC,IAAI,CAAC;MAE3C,KAAK,GAAG;QACN,OAAO,IAAI,CAAC+B,cAAc,CAACrB,KAAK,CAAC;MAEnC,KAAK,GAAG;QACN,OAAO,IAAI,CAACsB,cAAc,CAACtB,KAAK,CAAC;MAEnC,KAAK,GAAG;QAAE;QACR,OAAO,IAAI,CAACuB,cAAc,CAACvB,KAAK,CAAC;MAEnC;QACE,OAAO,IAAI,CAACwB,kBAAkB,CAACxB,KAAK,CAAC;;EAE3C;EAEQoB,cAAcA,CAAC/B,OAAyB,EAAEC,IAAiB;IACjE,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;MACtB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAAC;MAEnC,OAAO,IAAI,CAACN,WAAW,CAACyC,YAAY,EAAE,CAAC5B,IAAI,CACzCjB,SAAS,CAAC,MAAK;QACb,IAAI,CAACM,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAAC;QAEnC;QACA,OAAOA,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC;MAC7B,CAAC,CAAC,EACFZ,UAAU,CAAEiD,YAAY,IAAI;QAC1B,IAAI,CAACxC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAAC;QAEnC;QACA,IAAI,CAACN,WAAW,CAAC2C,MAAM,EAAE,CAACC,SAAS,EAAE;QACrC,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAEhC,OAAOtD,UAAU,CAAC,MAAMmD,YAAY,CAAC;MACvC,CAAC,CAAC,CACH;KACF,MAAM;MACL;MACA,OAAO,IAAI,CAACvC,mBAAmB,CAACU,IAAI,CAClCnB,MAAM,CAACoD,MAAM,IAAIA,MAAM,KAAK,IAAI,CAAC,EACjCnD,IAAI,CAAC,CAAC,CAAC,EACPC,SAAS,CAAC,MAAMU,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC,CAAC,CACtC;;EAEL;EAEQgC,cAAcA,CAACrB,KAAwB;IAC7C;IACAc,OAAO,CAACd,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC+B,OAAO,CAAC;IAEjD;IACA;IAEA,OAAOxD,UAAU,CAAC,MAAMyB,KAAK,CAAC;EAChC;EAEQsB,cAAcA,CAACtB,KAAwB;IAC7C;IACA,MAAMgC,UAAU,GAAGhC,KAAK,CAACO,OAAO,CAACI,GAAG,CAAC,aAAa,CAAC;IACnD,MAAMsB,YAAY,GAAGD,UAAU,GAAGtB,QAAQ,CAACsB,UAAU,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;IAExElB,OAAO,CAACC,IAAI,CAAC,oCAAoCkB,YAAY,IAAI,CAAC;IAElE;IACA;IAEA,OAAO1D,UAAU,CAAC,MAAMyB,KAAK,CAAC;EAChC;EAEQuB,cAAcA,CAACvB,KAAwB;IAC7C;IACAiB,cAAc,CAACiB,UAAU,CAAC,YAAY,CAAC;IAEvCpB,OAAO,CAACd,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC+B,OAAO,CAAC;IAEpD,OAAOxD,UAAU,CAAC,MAAMyB,KAAK,CAAC;EAChC;EAEQwB,kBAAkBA,CAACxB,KAAwB;IACjD;IACAc,OAAO,CAACd,KAAK,CAAC,aAAa,EAAE;MAC3BmB,MAAM,EAAEnB,KAAK,CAACmB,MAAM;MACpBY,OAAO,EAAE/B,KAAK,CAAC+B,OAAO;MACtB3B,GAAG,EAAEJ,KAAK,CAACI,GAAG;MACdJ,KAAK,EAAEA,KAAK,CAACA;KACd,CAAC;IAEF;IACA,IAAIA,KAAK,CAACmB,MAAM,KAAK,CAAC,EAAE;MACtBL,OAAO,CAACd,KAAK,CAAC,gDAAgD,CAAC;;IAGjE;IACA,IAAIA,KAAK,CAACmB,MAAM,IAAI,GAAG,EAAE;MACvBL,OAAO,CAACd,KAAK,CAAC,uCAAuC,CAAC;;IAGxD,OAAOzB,UAAU,CAAC,MAAMyB,KAAK,CAAC;EAChC;;;uBAvLWlB,eAAe,EAAAqD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAf1D,eAAe;MAAA2D,OAAA,EAAf3D,eAAe,CAAA4D;IAAA;EAAA;;AA2L5B,OAAM,MAAOC,eAAe;EAE1BvD,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,IAAI,CAACsD,kBAAkB,CAACvD,OAAO,CAAC,EAAE;MACpC,MAAM2B,SAAS,GAAGC,cAAc,CAAC4B,OAAO,CAAC,YAAY,CAAC;MAEtD,IAAI7B,SAAS,EAAE;QACb,MAAM8B,WAAW,GAAGzD,OAAO,CAACK,KAAK,CAAC;UAChCC,UAAU,EAAE;YACV,cAAc,EAAEqB;;SAEnB,CAAC;QAEF,OAAO1B,IAAI,CAACE,MAAM,CAACsD,WAAW,CAAC;;;IAInC,OAAOxD,IAAI,CAACE,MAAM,CAACH,OAAO,CAAC;EAC7B;EAEQuD,kBAAkBA,CAACvD,OAAyB;IAClD;IACA,MAAM0D,oBAAoB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC;IAC/D,OAAOA,oBAAoB,CAAC1C,QAAQ,CAAChB,OAAO,CAAC2D,MAAM,CAACC,WAAW,EAAE,CAAC;EACpE;;;uBAzBWN,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAF,OAAA,EAAfE,eAAe,CAAAD;IAAA;EAAA;;AA6B5B,OAAM,MAAOQ,0BAA0B;EAErC9D,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,MAAM6D,aAAa,GAAG9D,OAAO,CAACK,KAAK,CAAC;MAClCC,UAAU,EAAE;QACV,wBAAwB,EAAE,SAAS;QACnC,iBAAiB,EAAE,MAAM;QACzB,kBAAkB,EAAE,eAAe;QACnC,iBAAiB,EAAE;;KAEtB,CAAC;IAEF,OAAOL,IAAI,CAACE,MAAM,CAAC2D,aAAa,CAAC;EACnC;;;uBAdWD,0BAA0B;IAAA;EAAA;;;aAA1BA,0BAA0B;MAAAT,OAAA,EAA1BS,0BAA0B,CAAAR;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}