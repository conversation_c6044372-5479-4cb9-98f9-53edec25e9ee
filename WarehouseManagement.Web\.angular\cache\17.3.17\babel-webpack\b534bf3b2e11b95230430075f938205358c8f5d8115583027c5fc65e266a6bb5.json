{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, take, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"../services/authorization.service\";\nimport * as i3 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, authorizationService, router) {\n    this.authService = authService;\n    this.authorizationService = authorizationService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkAuthentication(state.url);\n  }\n  canActivateChild(childRoute, state) {\n    return this.checkAuthentication(state.url);\n  }\n  canLoad(route, segments) {\n    const url = segments.map(segment => segment.path).join('/');\n    return this.checkAuthentication(`/${url}`);\n  }\n  checkAuthentication(url) {\n    return this.authService.authState$.pipe(take(1), map(authState => {\n      if (authState.loading) {\n        // Still loading, wait for authentication to complete\n        return false;\n      }\n      if (!authState.isAuthenticated) {\n        // Not authenticated, redirect to login\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return false;\n      }\n      // Check if user has permission to access this route\n      if (!this.authorizationService.canAccessRoute(url)) {\n        // User doesn't have permission, redirect to unauthorized page\n        this.router.navigate(['/unauthorized']);\n        this.authorizationService.logPermissionDenied('route', 'access', url);\n        return false;\n      }\n      return true;\n    }), catchError(() => {\n      // Error occurred, redirect to login\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: url\n        }\n      });\n      return of(false);\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class NoAuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    return this.authService.authState$.pipe(take(1), map(authState => {\n      if (authState.isAuthenticated) {\n        // User is already authenticated, redirect to dashboard\n        this.router.navigate(['/dashboard']);\n        return false;\n      }\n      return true;\n    }));\n  }\n  static {\n    this.ɵfac = function NoAuthGuard_Factory(t) {\n      return new (t || NoAuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NoAuthGuard,\n      factory: NoAuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class RoleGuard {\n  constructor(authService, authorizationService, router) {\n    this.authService = authService;\n    this.authorizationService = authorizationService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkRole(route, state.url);\n  }\n  canActivateChild(childRoute, state) {\n    return this.checkRole(childRoute, state.url);\n  }\n  checkRole(route, url) {\n    const requiredRoles = route.data['roles'];\n    const requiredPermissions = route.data['permissions'];\n    return this.authService.authState$.pipe(take(1), map(authState => {\n      if (!authState.isAuthenticated) {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return false;\n      }\n      // Check required roles\n      if (requiredRoles && requiredRoles.length > 0) {\n        const hasRequiredRole = requiredRoles.some(role => this.authorizationService.hasRole(role));\n        if (!hasRequiredRole) {\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied('role', requiredRoles.join(','), url);\n          return false;\n        }\n      }\n      // Check required permissions\n      if (requiredPermissions && requiredPermissions.length > 0) {\n        const hasRequiredPermissions = requiredPermissions.every(permission => this.authorizationService.hasPermission(permission.resource, permission.action));\n        if (!hasRequiredPermissions) {\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied('permission', requiredPermissions.map(p => `${p.action}:${p.resource}`).join(','), url);\n          return false;\n        }\n      }\n      return true;\n    }));\n  }\n  static {\n    this.ɵfac = function RoleGuard_Factory(t) {\n      return new (t || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RoleGuard,\n      factory: RoleGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class PermissionGuard {\n  constructor(authService, authorizationService, router) {\n    this.authService = authService;\n    this.authorizationService = authorizationService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.checkPermission(route, state.url);\n  }\n  canActivateChild(childRoute, state) {\n    return this.checkPermission(childRoute, state.url);\n  }\n  checkPermission(route, url) {\n    const resource = route.data['resource'];\n    const action = route.data['action'];\n    const permissions = route.data['permissions'];\n    return this.authService.authState$.pipe(take(1), map(authState => {\n      if (!authState.isAuthenticated) {\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return false;\n      }\n      // Check single permission\n      if (resource && action) {\n        if (!this.authorizationService.hasPermission(resource, action)) {\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied(resource, action, url);\n          return false;\n        }\n      }\n      // Check multiple permissions\n      if (permissions && permissions.length > 0) {\n        const hasAllPermissions = permissions.every(permission => this.authorizationService.hasPermission(permission.resource, permission.action));\n        if (!hasAllPermissions) {\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied('multiple', permissions.map(p => `${p.action}:${p.resource}`).join(','), url);\n          return false;\n        }\n      }\n      return true;\n    }));\n  }\n  static {\n    this.ɵfac = function PermissionGuard_Factory(t) {\n      return new (t || PermissionGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PermissionGuard,\n      factory: PermissionGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "map", "take", "catchError", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "authorizationService", "router", "canActivate", "route", "state", "checkAuthentication", "url", "canActivateChild", "childRoute", "canLoad", "segments", "segment", "path", "join", "authState$", "pipe", "authState", "loading", "isAuthenticated", "navigate", "queryParams", "returnUrl", "canAccessRoute", "logPermissionDenied", "i0", "ɵɵinject", "i1", "AuthService", "i2", "AuthorizationService", "i3", "Router", "factory", "ɵfac", "providedIn", "NoAuth<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "checkRole", "requiredRoles", "data", "requiredPermissions", "length", "hasRequiredRole", "some", "role", "hasRole", "hasRequiredPermissions", "every", "permission", "hasPermission", "resource", "action", "p", "PermissionGuard", "checkPermission", "permissions", "hasAllPermissions"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { \n  CanActivate, \n  CanActivateChild, \n  CanLoad, \n  Router, \n  ActivatedRouteSnapshot, \n  RouterStateSnapshot, \n  Route, \n  UrlSegment \n} from '@angular/router';\nimport { Observable, of } from 'rxjs';\nimport { map, take, tap, catchError } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nimport { AuthorizationService } from '../services/authorization.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate, CanActivateChild, CanLoad {\n\n  constructor(\n    private authService: AuthService,\n    private authorizationService: AuthorizationService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuthentication(state.url);\n  }\n\n  canActivateChild(\n    childRoute: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuthentication(state.url);\n  }\n\n  canLoad(route: Route, segments: UrlSegment[]): Observable<boolean> {\n    const url = segments.map(segment => segment.path).join('/');\n    return this.checkAuthentication(`/${url}`);\n  }\n\n  private checkAuthentication(url: string): Observable<boolean> {\n    return this.authService.authState$.pipe(\n      take(1),\n      map(authState => {\n        if (authState.loading) {\n          // Still loading, wait for authentication to complete\n          return false;\n        }\n\n        if (!authState.isAuthenticated) {\n          // Not authenticated, redirect to login\n          this.router.navigate(['/login'], { \n            queryParams: { returnUrl: url } \n          });\n          return false;\n        }\n\n        // Check if user has permission to access this route\n        if (!this.authorizationService.canAccessRoute(url)) {\n          // User doesn't have permission, redirect to unauthorized page\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied('route', 'access', url);\n          return false;\n        }\n\n        return true;\n      }),\n      catchError(() => {\n        // Error occurred, redirect to login\n        this.router.navigate(['/login'], { \n          queryParams: { returnUrl: url } \n        });\n        return of(false);\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NoAuthGuard implements CanActivate {\n  \n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): Observable<boolean> {\n    return this.authService.authState$.pipe(\n      take(1),\n      map(authState => {\n        if (authState.isAuthenticated) {\n          // User is already authenticated, redirect to dashboard\n          this.router.navigate(['/dashboard']);\n          return false;\n        }\n        return true;\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleGuard implements CanActivate, CanActivateChild {\n\n  constructor(\n    private authService: AuthService,\n    private authorizationService: AuthorizationService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkRole(route, state.url);\n  }\n\n  canActivateChild(\n    childRoute: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkRole(childRoute, state.url);\n  }\n\n  private checkRole(route: ActivatedRouteSnapshot, url: string): Observable<boolean> {\n    const requiredRoles = route.data['roles'] as string[];\n    const requiredPermissions = route.data['permissions'] as Array<{resource: string, action: string}>;\n\n    return this.authService.authState$.pipe(\n      take(1),\n      map(authState => {\n        if (!authState.isAuthenticated) {\n          this.router.navigate(['/login'], { \n            queryParams: { returnUrl: url } \n          });\n          return false;\n        }\n\n        // Check required roles\n        if (requiredRoles && requiredRoles.length > 0) {\n          const hasRequiredRole = requiredRoles.some(role => \n            this.authorizationService.hasRole(role)\n          );\n\n          if (!hasRequiredRole) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied('role', requiredRoles.join(','), url);\n            return false;\n          }\n        }\n\n        // Check required permissions\n        if (requiredPermissions && requiredPermissions.length > 0) {\n          const hasRequiredPermissions = requiredPermissions.every(permission => \n            this.authorizationService.hasPermission(permission.resource, permission.action)\n          );\n\n          if (!hasRequiredPermissions) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied(\n              'permission', \n              requiredPermissions.map(p => `${p.action}:${p.resource}`).join(','), \n              url\n            );\n            return false;\n          }\n        }\n\n        return true;\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PermissionGuard implements CanActivate, CanActivateChild {\n\n  constructor(\n    private authService: AuthService,\n    private authorizationService: AuthorizationService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkPermission(route, state.url);\n  }\n\n  canActivateChild(\n    childRoute: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkPermission(childRoute, state.url);\n  }\n\n  private checkPermission(route: ActivatedRouteSnapshot, url: string): Observable<boolean> {\n    const resource = route.data['resource'] as string;\n    const action = route.data['action'] as string;\n    const permissions = route.data['permissions'] as Array<{resource: string, action: string}>;\n\n    return this.authService.authState$.pipe(\n      take(1),\n      map(authState => {\n        if (!authState.isAuthenticated) {\n          this.router.navigate(['/login'], { \n            queryParams: { returnUrl: url } \n          });\n          return false;\n        }\n\n        // Check single permission\n        if (resource && action) {\n          if (!this.authorizationService.hasPermission(resource, action)) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied(resource, action, url);\n            return false;\n          }\n        }\n\n        // Check multiple permissions\n        if (permissions && permissions.length > 0) {\n          const hasAllPermissions = permissions.every(permission => \n            this.authorizationService.hasPermission(permission.resource, permission.action)\n          );\n\n          if (!hasAllPermissions) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied(\n              'multiple', \n              permissions.map(p => `${p.action}:${p.resource}`).join(','), \n              url\n            );\n            return false;\n          }\n        }\n\n        return true;\n      })\n    );\n  }\n}\n"], "mappings": "AAWA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,IAAI,EAAOC,UAAU,QAAQ,gBAAgB;;;;;AAO3D,OAAM,MAAOC,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAACE,GAAG,CAAC;EAC5C;EAEAC,gBAAgBA,CACdC,UAAkC,EAClCJ,KAA0B;IAE1B,OAAO,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAACE,GAAG,CAAC;EAC5C;EAEAG,OAAOA,CAACN,KAAY,EAAEO,QAAsB;IAC1C,MAAMJ,GAAG,GAAGI,QAAQ,CAAChB,GAAG,CAACiB,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IAC3D,OAAO,IAAI,CAACR,mBAAmB,CAAC,IAAIC,GAAG,EAAE,CAAC;EAC5C;EAEQD,mBAAmBA,CAACC,GAAW;IACrC,OAAO,IAAI,CAACP,WAAW,CAACe,UAAU,CAACC,IAAI,CACrCpB,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACsB,SAAS,IAAG;MACd,IAAIA,SAAS,CAACC,OAAO,EAAE;QACrB;QACA,OAAO,KAAK;;MAGd,IAAI,CAACD,SAAS,CAACE,eAAe,EAAE;QAC9B;QACA,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BC,WAAW,EAAE;YAAEC,SAAS,EAAEf;UAAG;SAC9B,CAAC;QACF,OAAO,KAAK;;MAGd;MACA,IAAI,CAAC,IAAI,CAACN,oBAAoB,CAACsB,cAAc,CAAChB,GAAG,CAAC,EAAE;QAClD;QACA,IAAI,CAACL,MAAM,CAACkB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC,IAAI,CAACnB,oBAAoB,CAACuB,mBAAmB,CAAC,OAAO,EAAE,QAAQ,EAAEjB,GAAG,CAAC;QACrE,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC,EACFV,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACK,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC/BC,WAAW,EAAE;UAAEC,SAAS,EAAEf;QAAG;OAC9B,CAAC;MACF,OAAOb,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACH;;;uBA9DWI,SAAS,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATlC,SAAS;MAAAmC,OAAA,EAATnC,SAAS,CAAAoC,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;AAsEpB,OAAM,MAAOC,WAAW;EAEtBrC,YACUC,WAAwB,EACxBE,MAAc;IADd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAE,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACH,WAAW,CAACe,UAAU,CAACC,IAAI,CACrCpB,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACsB,SAAS,IAAG;MACd,IAAIA,SAAS,CAACE,eAAe,EAAE;QAC7B;QACA,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACpC,OAAO,KAAK;;MAEd,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;;;uBAnBWgB,WAAW,EAAAX,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXI,WAAW;MAAAH,OAAA,EAAXG,WAAW,CAAAF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;AA2BpB,OAAM,MAAOE,SAAS;EAEpBtC,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACiC,SAAS,CAAClC,KAAK,EAAEC,KAAK,CAACE,GAAG,CAAC;EACzC;EAEAC,gBAAgBA,CACdC,UAAkC,EAClCJ,KAA0B;IAE1B,OAAO,IAAI,CAACiC,SAAS,CAAC7B,UAAU,EAAEJ,KAAK,CAACE,GAAG,CAAC;EAC9C;EAEQ+B,SAASA,CAAClC,KAA6B,EAAEG,GAAW;IAC1D,MAAMgC,aAAa,GAAGnC,KAAK,CAACoC,IAAI,CAAC,OAAO,CAAa;IACrD,MAAMC,mBAAmB,GAAGrC,KAAK,CAACoC,IAAI,CAAC,aAAa,CAA8C;IAElG,OAAO,IAAI,CAACxC,WAAW,CAACe,UAAU,CAACC,IAAI,CACrCpB,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACsB,SAAS,IAAG;MACd,IAAI,CAACA,SAAS,CAACE,eAAe,EAAE;QAC9B,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BC,WAAW,EAAE;YAAEC,SAAS,EAAEf;UAAG;SAC9B,CAAC;QACF,OAAO,KAAK;;MAGd;MACA,IAAIgC,aAAa,IAAIA,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAMC,eAAe,GAAGJ,aAAa,CAACK,IAAI,CAACC,IAAI,IAC7C,IAAI,CAAC5C,oBAAoB,CAAC6C,OAAO,CAACD,IAAI,CAAC,CACxC;QAED,IAAI,CAACF,eAAe,EAAE;UACpB,IAAI,CAACzC,MAAM,CAACkB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;UACvC,IAAI,CAACnB,oBAAoB,CAACuB,mBAAmB,CAAC,MAAM,EAAEe,aAAa,CAACzB,IAAI,CAAC,GAAG,CAAC,EAAEP,GAAG,CAAC;UACnF,OAAO,KAAK;;;MAIhB;MACA,IAAIkC,mBAAmB,IAAIA,mBAAmB,CAACC,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMK,sBAAsB,GAAGN,mBAAmB,CAACO,KAAK,CAACC,UAAU,IACjE,IAAI,CAAChD,oBAAoB,CAACiD,aAAa,CAACD,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,CAAC,CAChF;QAED,IAAI,CAACL,sBAAsB,EAAE;UAC3B,IAAI,CAAC7C,MAAM,CAACkB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;UACvC,IAAI,CAACnB,oBAAoB,CAACuB,mBAAmB,CAC3C,YAAY,EACZiB,mBAAmB,CAAC9C,GAAG,CAAC0D,CAAC,IAAI,GAAGA,CAAC,CAACD,MAAM,IAAIC,CAAC,CAACF,QAAQ,EAAE,CAAC,CAACrC,IAAI,CAAC,GAAG,CAAC,EACnEP,GAAG,CACJ;UACD,OAAO,KAAK;;;MAIhB,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;;;uBArEW8B,SAAS,EAAAZ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATK,SAAS;MAAAJ,OAAA,EAATI,SAAS,CAAAH,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;AA6EpB,OAAM,MAAOmB,eAAe;EAE1BvD,YACUC,WAAwB,EACxBC,oBAA0C,EAC1CC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,OAAO,IAAI,CAACkD,eAAe,CAACnD,KAAK,EAAEC,KAAK,CAACE,GAAG,CAAC;EAC/C;EAEAC,gBAAgBA,CACdC,UAAkC,EAClCJ,KAA0B;IAE1B,OAAO,IAAI,CAACkD,eAAe,CAAC9C,UAAU,EAAEJ,KAAK,CAACE,GAAG,CAAC;EACpD;EAEQgD,eAAeA,CAACnD,KAA6B,EAAEG,GAAW;IAChE,MAAM4C,QAAQ,GAAG/C,KAAK,CAACoC,IAAI,CAAC,UAAU,CAAW;IACjD,MAAMY,MAAM,GAAGhD,KAAK,CAACoC,IAAI,CAAC,QAAQ,CAAW;IAC7C,MAAMgB,WAAW,GAAGpD,KAAK,CAACoC,IAAI,CAAC,aAAa,CAA8C;IAE1F,OAAO,IAAI,CAACxC,WAAW,CAACe,UAAU,CAACC,IAAI,CACrCpB,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACsB,SAAS,IAAG;MACd,IAAI,CAACA,SAAS,CAACE,eAAe,EAAE;QAC9B,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BC,WAAW,EAAE;YAAEC,SAAS,EAAEf;UAAG;SAC9B,CAAC;QACF,OAAO,KAAK;;MAGd;MACA,IAAI4C,QAAQ,IAAIC,MAAM,EAAE;QACtB,IAAI,CAAC,IAAI,CAACnD,oBAAoB,CAACiD,aAAa,CAACC,QAAQ,EAAEC,MAAM,CAAC,EAAE;UAC9D,IAAI,CAAClD,MAAM,CAACkB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;UACvC,IAAI,CAACnB,oBAAoB,CAACuB,mBAAmB,CAAC2B,QAAQ,EAAEC,MAAM,EAAE7C,GAAG,CAAC;UACpE,OAAO,KAAK;;;MAIhB;MACA,IAAIiD,WAAW,IAAIA,WAAW,CAACd,MAAM,GAAG,CAAC,EAAE;QACzC,MAAMe,iBAAiB,GAAGD,WAAW,CAACR,KAAK,CAACC,UAAU,IACpD,IAAI,CAAChD,oBAAoB,CAACiD,aAAa,CAACD,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,CAAC,CAChF;QAED,IAAI,CAACK,iBAAiB,EAAE;UACtB,IAAI,CAACvD,MAAM,CAACkB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;UACvC,IAAI,CAACnB,oBAAoB,CAACuB,mBAAmB,CAC3C,UAAU,EACVgC,WAAW,CAAC7D,GAAG,CAAC0D,CAAC,IAAI,GAAGA,CAAC,CAACD,MAAM,IAAIC,CAAC,CAACF,QAAQ,EAAE,CAAC,CAACrC,IAAI,CAAC,GAAG,CAAC,EAC3DP,GAAG,CACJ;UACD,OAAO,KAAK;;;MAIhB,OAAO,IAAI;IACb,CAAC,CAAC,CACH;EACH;;;uBAlEW+C,eAAe,EAAA7B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAfsB,eAAe;MAAArB,OAAA,EAAfqB,eAAe,CAAApB,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}