{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { PLATFORM_ID, booleanAttribute, Directive, Inject, Input, HostListener, Pipe, forwardRef, EventEmitter, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { EyeIcon } from 'primeng/icons/eye';\nimport { EyeSlashIcon } from 'primeng/icons/eyeslash';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i4 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\n/**\n * Password directive.\n * @group Components\n */\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c2 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c3 = a0 => ({\n  width: a0\n});\nfunction Password_ng_container_6_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 10);\n    i0.ɵɵlistener(\"click\", function Password_ng_container_6_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-password-clear-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n  }\n}\nfunction Password_ng_container_6_3_ng_template_0_Template(rf, ctx) {}\nfunction Password_ng_container_6_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Password_ng_container_6_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Password_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Password_ng_container_6_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 7);\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵlistener(\"click\", function Password_ng_container_6_Template_span_click_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.clear());\n    });\n    i0.ɵɵtemplate(3, Password_ng_container_6_3_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"clearIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.clearIconTemplate);\n  }\n}\nfunction Password_ng_container_7_ng_container_1_EyeSlashIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"EyeSlashIcon\", 13);\n    i0.ɵɵlistener(\"keypress\", function Password_ng_container_7_ng_container_1_EyeSlashIcon_1_Template_EyeSlashIcon_keypress_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onEyeIconPress($event));\n    })(\"click\", function Password_ng_container_7_ng_container_1_EyeSlashIcon_1_Template_EyeSlashIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onMaskToggle());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"hideIcon\");\n  }\n}\nfunction Password_ng_container_7_ng_container_1_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Password_ng_container_7_ng_container_1_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Password_ng_container_7_ng_container_1_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Password_ng_container_7_ng_container_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵlistener(\"click\", function Password_ng_container_7_ng_container_1_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onMaskToggle());\n    });\n    i0.ɵɵtemplate(1, Password_ng_container_7_ng_container_1_span_2_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.hideIconTemplate);\n  }\n}\nfunction Password_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Password_ng_container_7_ng_container_1_EyeSlashIcon_1_Template, 1, 1, \"EyeSlashIcon\", 11)(2, Password_ng_container_7_ng_container_1_span_2_Template, 2, 1, \"span\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.hideIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.hideIconTemplate);\n  }\n}\nfunction Password_ng_container_7_ng_container_2_EyeIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"EyeIcon\", 13);\n    i0.ɵɵlistener(\"keypress\", function Password_ng_container_7_ng_container_2_EyeIcon_1_Template_EyeIcon_keypress_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onEyeIconPress($event));\n    })(\"click\", function Password_ng_container_7_ng_container_2_EyeIcon_1_Template_EyeIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onMaskToggle());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"data-pc-section\", \"showIcon\");\n  }\n}\nfunction Password_ng_container_7_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Password_ng_container_7_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Password_ng_container_7_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Password_ng_container_7_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵlistener(\"click\", function Password_ng_container_7_ng_container_2_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onMaskToggle());\n    });\n    i0.ɵɵtemplate(1, Password_ng_container_7_ng_container_2_span_2_1_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.showIconTemplate);\n  }\n}\nfunction Password_ng_container_7_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Password_ng_container_7_ng_container_2_EyeIcon_1_Template, 1, 1, \"EyeIcon\", 11)(2, Password_ng_container_7_ng_container_2_span_2_Template, 2, 1, \"span\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.showIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showIconTemplate);\n  }\n}\nfunction Password_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Password_ng_container_7_ng_container_1_Template, 3, 2, \"ng-container\", 5)(2, Password_ng_container_7_ng_container_2_Template, 3, 2, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.unmasked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.unmasked);\n  }\n}\nfunction Password_div_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Password_div_8_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Password_div_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Password_div_8_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.contentTemplate);\n  }\n}\nfunction Password_div_8_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"div\", 3);\n    i0.ɵɵpipe(2, \"mapper\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"meter\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpipeBind2(2, 6, ctx_r3.meter, ctx_r3.strengthClass))(\"ngStyle\", i0.ɵɵpureFunction1(9, _c3, ctx_r3.meter ? ctx_r3.meter.width : \"\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"meterLabel\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"info\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.infoText);\n  }\n}\nfunction Password_div_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Password_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15, 1);\n    i0.ɵɵlistener(\"click\", function Password_div_8_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Password_div_8_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Password_div_8_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Password_div_8_ng_container_2_Template, 1, 0, \"ng-container\", 9)(3, Password_div_8_ng_container_3_Template, 2, 1, \"ng-container\", 16)(4, Password_div_8_ng_template_4_Template, 5, 11, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, Password_div_8_ng_container_6_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const content_r10 = i0.ɵɵreference(5);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"p-password-panel p-component\")(\"@overlayAnimation\", i0.ɵɵpureFunction1(10, _c2, i0.ɵɵpureFunction2(7, _c1, ctx_r3.showTransitionOptions, ctx_r3.hideTransitionOptions)));\n    i0.ɵɵattribute(\"data-pc-section\", \"panel\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.contentTemplate)(\"ngIfElse\", content_r10);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.footerTemplate);\n  }\n}\nlet PasswordDirective = /*#__PURE__*/(() => {\n  class PasswordDirective {\n    document;\n    platformId;\n    renderer;\n    el;\n    zone;\n    config;\n    /**\n     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    promptLabel = 'Enter a password';\n    /**\n     * Text for a weak password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    weakLabel = 'Weak';\n    /**\n     * Text for a medium password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    mediumLabel = 'Medium';\n    /**\n     * Text for a strong password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    strongLabel = 'Strong';\n    /**\n     * Whether to show the strength indicator or not.\n     * @group Props\n     */\n    feedback = true;\n    /**\n     * Sets the visibility of the password field.\n     * @group Props\n     */\n    set showPassword(show) {\n      this.el.nativeElement.type = show ? 'text' : 'password';\n    }\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    panel;\n    meter;\n    info;\n    filled;\n    scrollHandler;\n    documentResizeListener;\n    constructor(document, platformId, renderer, el, zone, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.el = el;\n      this.zone = zone;\n      this.config = config;\n    }\n    ngDoCheck() {\n      this.updateFilledState();\n    }\n    onInput(e) {\n      this.updateFilledState();\n    }\n    updateFilledState() {\n      this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    createPanel() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.panel = this.renderer.createElement('div');\n        this.renderer.addClass(this.panel, 'p-password-panel');\n        this.renderer.addClass(this.panel, 'p-component');\n        this.renderer.addClass(this.panel, 'p-password-panel-overlay');\n        this.renderer.addClass(this.panel, 'p-connected-overlay');\n        this.meter = this.renderer.createElement('div');\n        this.renderer.addClass(this.meter, 'p-password-meter');\n        this.renderer.appendChild(this.panel, this.meter);\n        this.info = this.renderer.createElement('div');\n        this.renderer.addClass(this.info, 'p-password-info');\n        this.renderer.setProperty(this.info, 'textContent', this.promptLabel);\n        this.renderer.appendChild(this.panel, this.info);\n        this.renderer.setStyle(this.panel, 'minWidth', `${this.el.nativeElement.offsetWidth}px`);\n        this.renderer.appendChild(document.body, this.panel);\n      }\n    }\n    showOverlay() {\n      if (this.feedback) {\n        if (!this.panel) {\n          this.createPanel();\n        }\n        this.renderer.setStyle(this.panel, 'zIndex', String(++DomHandler.zindex));\n        this.renderer.setStyle(this.panel, 'display', 'block');\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => {\n            DomHandler.addClass(this.panel, 'p-connected-overlay-visible');\n            this.bindScrollListener();\n            this.bindDocumentResizeListener();\n          }, 1);\n        });\n        DomHandler.absolutePosition(this.panel, this.el.nativeElement);\n      }\n    }\n    hideOverlay() {\n      if (this.feedback && this.panel) {\n        DomHandler.addClass(this.panel, 'p-connected-overlay-hidden');\n        DomHandler.removeClass(this.panel, 'p-connected-overlay-visible');\n        this.unbindScrollListener();\n        this.unbindDocumentResizeListener();\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => {\n            this.ngOnDestroy();\n          }, 150);\n        });\n      }\n    }\n    onFocus() {\n      this.showOverlay();\n    }\n    onBlur() {\n      this.hideOverlay();\n    }\n    onKeyup(e) {\n      if (this.feedback) {\n        let value = e.target.value,\n          label = null,\n          meterPos = null;\n        if (value.length === 0) {\n          label = this.promptLabel;\n          meterPos = '0px 0px';\n        } else {\n          var score = this.testStrength(value);\n          if (score < 30) {\n            label = this.weakLabel;\n            meterPos = '0px -10px';\n          } else if (score >= 30 && score < 80) {\n            label = this.mediumLabel;\n            meterPos = '0px -20px';\n          } else if (score >= 80) {\n            label = this.strongLabel;\n            meterPos = '0px -30px';\n          }\n        }\n        if (!this.panel || !DomHandler.hasClass(this.panel, 'p-connected-overlay-visible')) {\n          this.showOverlay();\n        }\n        this.renderer.setStyle(this.meter, 'backgroundPosition', meterPos);\n        this.info.textContent = label;\n      }\n    }\n    testStrength(str) {\n      let grade = 0;\n      let val;\n      val = str.match('[0-9]');\n      grade += this.normalize(val ? val.length : 1 / 4, 1) * 25;\n      val = str.match('[a-zA-Z]');\n      grade += this.normalize(val ? val.length : 1 / 2, 3) * 10;\n      val = str.match('[!@#$%^&*?_~.,;=]');\n      grade += this.normalize(val ? val.length : 1 / 6, 1) * 35;\n      val = str.match('[A-Z]');\n      grade += this.normalize(val ? val.length : 1 / 6, 1) * 30;\n      grade *= str.length / 8;\n      return grade > 100 ? 100 : grade;\n    }\n    normalize(x, y) {\n      let diff = x - y;\n      if (diff <= 0) return x / y;else return 1 + 0.5 * (x / (x + y / 4));\n    }\n    get disabled() {\n      return this.el.nativeElement.disabled;\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n          if (DomHandler.hasClass(this.panel, 'p-connected-overlay-visible')) {\n            this.hideOverlay();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    bindDocumentResizeListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.documentResizeListener) {\n          const window = this.document.defaultView;\n          this.documentResizeListener = this.renderer.listen(window, 'resize', this.onWindowResize.bind(this));\n        }\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    onWindowResize() {\n      if (!DomHandler.isTouchDevice()) {\n        this.hideOverlay();\n      }\n    }\n    ngOnDestroy() {\n      if (this.panel) {\n        if (this.scrollHandler) {\n          this.scrollHandler.destroy();\n          this.scrollHandler = null;\n        }\n        this.unbindDocumentResizeListener();\n        this.renderer.removeChild(this.document.body, this.panel);\n        this.panel = null;\n        this.meter = null;\n        this.info = null;\n      }\n    }\n    static ɵfac = function PasswordDirective_Factory(t) {\n      return new (t || PasswordDirective)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PasswordDirective,\n      selectors: [[\"\", \"pPassword\", \"\"]],\n      hostAttrs: [1, \"p-inputtext\", \"p-component\", \"p-element\"],\n      hostVars: 4,\n      hostBindings: function PasswordDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function PasswordDirective_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          })(\"focus\", function PasswordDirective_focus_HostBindingHandler() {\n            return ctx.onFocus();\n          })(\"blur\", function PasswordDirective_blur_HostBindingHandler() {\n            return ctx.onBlur();\n          })(\"keyup\", function PasswordDirective_keyup_HostBindingHandler($event) {\n            return ctx.onKeyup($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-variant-filled\", ctx.variant === \"filled\" || ctx.config.inputStyle() === \"filled\");\n        }\n      },\n      inputs: {\n        promptLabel: \"promptLabel\",\n        weakLabel: \"weakLabel\",\n        mediumLabel: \"mediumLabel\",\n        strongLabel: \"strongLabel\",\n        feedback: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"feedback\", \"feedback\", booleanAttribute],\n        showPassword: \"showPassword\",\n        variant: \"variant\"\n      },\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n  return PasswordDirective;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MapperPipe = /*#__PURE__*/(() => {\n  class MapperPipe {\n    transform(value, mapper, ...args) {\n      return mapper(value, ...args);\n    }\n    static ɵfac = function MapperPipe_Factory(t) {\n      return new (t || MapperPipe)();\n    };\n    static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"mapper\",\n      type: MapperPipe,\n      pure: true\n    });\n  }\n  return MapperPipe;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst Password_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Password),\n  multi: true\n};\n/**\n * Password displays strength indicator for password fields.\n * @group Components\n */\nlet Password = /*#__PURE__*/(() => {\n  class Password {\n    document;\n    platformId;\n    renderer;\n    cd;\n    config;\n    el;\n    overlayService;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Specifies one or more IDs in the DOM that labels the input field.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Label of the input for accessibility.\n     * @group Props\n     */\n    label;\n    /**\n     * Indicates whether the component is disabled or not.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Text to prompt password entry. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    promptLabel;\n    /**\n     * Regex value for medium regex.\n     * @group Props\n     */\n    mediumRegex = '^(((?=.*[a-z])(?=.*[A-Z]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[A-Z])(?=.*[0-9])))(?=.{6,})';\n    /**\n     * Regex value for strong regex.\n     * @group Props\n     */\n    strongRegex = '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})';\n    /**\n     * Text for a weak password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    weakLabel;\n    /**\n     * Text for a medium password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    mediumLabel;\n    /**\n     * specifies the maximum number of characters allowed in the input element.\n     * @group Props\n     */\n    maxLength;\n    /**\n     * Text for a strong password. Defaults to PrimeNG I18N API configuration.\n     * @group Props\n     */\n    strongLabel;\n    /**\n     * Identifier of the accessible input element.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Whether to show the strength indicator or not.\n     * @group Props\n     */\n    feedback = true;\n    /**\n     * Id of the element or \"body\" for document where the overlay should be appended to.\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to show an icon to display the password as plain text.\n     * @group Props\n     */\n    toggleMask;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Specify automated assistance in filling out password by browser.\n     * @group Props\n     */\n    autocomplete;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * Callback to invoke when the component receives focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when the component loses focus.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when clear button is clicked.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    input;\n    contentTemplate;\n    footerTemplate;\n    headerTemplate;\n    clearIconTemplate;\n    hideIconTemplate;\n    showIconTemplate;\n    templates;\n    overlayVisible = false;\n    meter;\n    infoText;\n    focused = false;\n    unmasked = false;\n    mediumCheckRegExp;\n    strongCheckRegExp;\n    resizeListener;\n    scrollHandler;\n    overlay;\n    value = null;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    translationSubscription;\n    constructor(document, platformId, renderer, cd, config, el, overlayService) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.config = config;\n      this.el = el;\n      this.overlayService = overlayService;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'hideicon':\n            this.hideIconTemplate = item.template;\n            break;\n          case 'showicon':\n            this.showIconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnInit() {\n      this.infoText = this.promptText();\n      this.mediumCheckRegExp = new RegExp(this.mediumRegex);\n      this.strongCheckRegExp = new RegExp(this.strongRegex);\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        this.updateUI(this.value || '');\n      });\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.overlay = event.element;\n          ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n          this.appendContainer();\n          this.alignOverlay();\n          this.bindScrollListener();\n          this.bindResizeListener();\n          break;\n        case 'void':\n          this.unbindScrollListener();\n          this.unbindResizeListener();\n          this.overlay = null;\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          ZIndexUtils.clear(event.element);\n          break;\n      }\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.overlay);else this.document.getElementById(this.appendTo).appendChild(this.overlay);\n      }\n    }\n    alignOverlay() {\n      if (this.appendTo) {\n        this.overlay.style.minWidth = DomHandler.getOuterWidth(this.input.nativeElement) + 'px';\n        DomHandler.absolutePosition(this.overlay, this.input.nativeElement);\n      } else {\n        DomHandler.relativePosition(this.overlay, this.input.nativeElement);\n      }\n    }\n    onInput(event) {\n      this.value = event.target.value;\n      this.onModelChange(this.value);\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      if (this.feedback) {\n        this.overlayVisible = true;\n      }\n      this.onFocus.emit(event);\n    }\n    onEyeIconPress(event) {\n      switch (event.code) {\n        case 'Enter':\n          this.onMaskToggle();\n          event.preventDefault();\n          break;\n        default:\n          break;\n      }\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      if (this.feedback) {\n        this.overlayVisible = false;\n      }\n      this.onModelTouched();\n      this.onBlur.emit(event);\n    }\n    onKeyUp(event) {\n      if (this.feedback) {\n        let value = event.target.value;\n        this.updateUI(value);\n        if (event.code === 'Escape') {\n          this.overlayVisible && (this.overlayVisible = false);\n          return;\n        }\n        if (!this.overlayVisible) {\n          this.overlayVisible = true;\n        }\n      }\n    }\n    updateUI(value) {\n      let label = null;\n      let meter = null;\n      switch (this.testStrength(value)) {\n        case 1:\n          label = this.weakText();\n          meter = {\n            strength: 'weak',\n            width: '33.33%'\n          };\n          break;\n        case 2:\n          label = this.mediumText();\n          meter = {\n            strength: 'medium',\n            width: '66.66%'\n          };\n          break;\n        case 3:\n          label = this.strongText();\n          meter = {\n            strength: 'strong',\n            width: '100%'\n          };\n          break;\n        default:\n          label = this.promptText();\n          meter = null;\n          break;\n      }\n      this.meter = meter;\n      this.infoText = label;\n    }\n    onMaskToggle() {\n      this.unmasked = !this.unmasked;\n    }\n    onOverlayClick(event) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n    testStrength(str) {\n      let level = 0;\n      if (this.strongCheckRegExp.test(str)) level = 3;else if (this.mediumCheckRegExp.test(str)) level = 2;else if (str.length) level = 1;\n      return level;\n    }\n    writeValue(value) {\n      if (value === undefined) this.value = null;else this.value = value;\n      if (this.feedback) this.updateUI(this.value || '');\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    bindScrollListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.scrollHandler) {\n          this.scrollHandler = new ConnectedOverlayScrollHandler(this.input.nativeElement, () => {\n            if (this.overlayVisible) {\n              this.overlayVisible = false;\n            }\n          });\n        }\n        this.scrollHandler.bindScrollListener();\n      }\n    }\n    bindResizeListener() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.resizeListener) {\n          const window = this.document.defaultView;\n          this.resizeListener = this.renderer.listen(window, 'resize', () => {\n            if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n              this.overlayVisible = false;\n            }\n          });\n        }\n      }\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    unbindResizeListener() {\n      if (this.resizeListener) {\n        this.resizeListener();\n        this.resizeListener = null;\n      }\n    }\n    containerClass(toggleMask) {\n      return {\n        'p-password p-component p-inputwrapper': true,\n        'p-input-icon-right': toggleMask\n      };\n    }\n    inputFieldClass(disabled) {\n      return {\n        'p-password-input': true,\n        'p-disabled': disabled\n      };\n    }\n    strengthClass(meter) {\n      return `p-password-strength ${meter ? meter.strength : ''}`;\n    }\n    filled() {\n      return this.value != null && this.value.toString().length > 0;\n    }\n    promptText() {\n      return this.promptLabel || this.getTranslation(TranslationKeys.PASSWORD_PROMPT);\n    }\n    weakText() {\n      return this.weakLabel || this.getTranslation(TranslationKeys.WEAK);\n    }\n    mediumText() {\n      return this.mediumLabel || this.getTranslation(TranslationKeys.MEDIUM);\n    }\n    strongText() {\n      return this.strongLabel || this.getTranslation(TranslationKeys.STRONG);\n    }\n    restoreAppend() {\n      if (this.overlay && this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.removeChild(this.document.body, this.overlay);else this.document.getElementById(this.appendTo).removeChild(this.overlay);\n      }\n    }\n    inputType(unmasked) {\n      return unmasked ? 'text' : 'password';\n    }\n    getTranslation(option) {\n      return this.config.getTranslation(option);\n    }\n    clear() {\n      this.value = null;\n      this.onModelChange(this.value);\n      this.writeValue(this.value);\n      this.onClear.emit();\n    }\n    ngOnDestroy() {\n      if (this.overlay) {\n        ZIndexUtils.clear(this.overlay);\n        this.overlay = null;\n      }\n      this.restoreAppend();\n      this.unbindResizeListener();\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function Password_Factory(t) {\n      return new (t || Password)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.OverlayService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Password,\n      selectors: [[\"p-password\"]],\n      contentQueries: function Password_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Password_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 8,\n      hostBindings: function Password_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled())(\"p-inputwrapper-focus\", ctx.focused)(\"p-password-clearable\", ctx.showClear)(\"p-password-mask\", ctx.toggleMask);\n        }\n      },\n      inputs: {\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        label: \"label\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        promptLabel: \"promptLabel\",\n        mediumRegex: \"mediumRegex\",\n        strongRegex: \"strongRegex\",\n        weakLabel: \"weakLabel\",\n        mediumLabel: \"mediumLabel\",\n        maxLength: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"maxLength\", \"maxLength\", numberAttribute],\n        strongLabel: \"strongLabel\",\n        inputId: \"inputId\",\n        feedback: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"feedback\", \"feedback\", booleanAttribute],\n        appendTo: \"appendTo\",\n        toggleMask: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"toggleMask\", \"toggleMask\", booleanAttribute],\n        inputStyleClass: \"inputStyleClass\",\n        styleClass: \"styleClass\",\n        style: \"style\",\n        inputStyle: \"inputStyle\",\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        autocomplete: \"autocomplete\",\n        placeholder: \"placeholder\",\n        showClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showClear\", \"showClear\", booleanAttribute],\n        autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute],\n        variant: \"variant\"\n      },\n      outputs: {\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onClear: \"onClear\"\n      },\n      features: [i0.ɵɵProvidersFeature([Password_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n      decls: 9,\n      vars: 35,\n      consts: [[\"input\", \"\"], [\"overlay\", \"\"], [\"content\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"pInputText\", \"\", \"pAutoFocus\", \"\", 3, \"input\", \"focus\", \"blur\", \"keyup\", \"ngClass\", \"disabled\", \"ngStyle\", \"value\", \"variant\", \"autofocus\"], [4, \"ngIf\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [1, \"p-password-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [3, \"click\", \"styleClass\"], [\"tabIndex\", \"0\", 3, \"keypress\", \"click\", 4, \"ngIf\"], [3, \"click\", 4, \"ngIf\"], [\"tabIndex\", \"0\", 3, \"keypress\", \"click\"], [3, \"click\"], [3, \"click\", \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"p-password-meter\"], [1, \"p-password-info\"]],\n      template: function Password_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵpipe(1, \"mapper\");\n          i0.ɵɵelementStart(2, \"input\", 4, 0);\n          i0.ɵɵpipe(4, \"mapper\");\n          i0.ɵɵpipe(5, \"mapper\");\n          i0.ɵɵlistener(\"input\", function Password_Template_input_input_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInput($event));\n          })(\"focus\", function Password_Template_input_focus_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputFocus($event));\n          })(\"blur\", function Password_Template_input_blur_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onInputBlur($event));\n          })(\"keyup\", function Password_Template_input_keyup_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyUp($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, Password_ng_container_6_Template, 4, 3, \"ng-container\", 5)(7, Password_ng_container_7_Template, 3, 2, \"ng-container\", 5)(8, Password_div_8_Template, 7, 12, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpipeBind2(1, 26, ctx.toggleMask, ctx.containerClass))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"password\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.inputStyleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpipeBind2(4, 29, ctx.disabled, ctx.inputFieldClass))(\"disabled\", ctx.disabled)(\"ngStyle\", ctx.inputStyle)(\"value\", ctx.value)(\"variant\", ctx.variant)(\"autofocus\", ctx.autofocus);\n          i0.ɵɵattribute(\"label\", ctx.label)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"id\", ctx.inputId)(\"type\", i0.ɵɵpipeBind2(5, 32, ctx.unmasked, ctx.inputType))(\"placeholder\", ctx.placeholder)(\"autocomplete\", ctx.autocomplete)(\"maxlength\", ctx.maxLength)(\"data-pc-section\", \"input\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showClear && ctx.value != null);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.toggleMask);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.overlayVisible);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.InputText, i4.AutoFocus, TimesIcon, EyeSlashIcon, EyeIcon, MapperPipe],\n      styles: [\"@layer primeng{.p-password{position:relative;display:inline-flex}.p-password-panel{position:absolute;top:0;left:0}.p-password .p-password-panel{min-width:100%}.p-password-meter{height:10px}.p-password-strength{height:100%;width:0%;transition:width 1s ease-in-out}.p-fluid .p-password{display:flex}.p-password-input::-ms-reveal,.p-password-input::-ms-clear{display:none}.p-password-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-password .p-icon{cursor:pointer}.p-password-clearable.p-password-mask .p-password-clear-icon{margin-top:unset}.p-password-clearable{position:relative}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('overlayAnimation', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'scaleY(0.8)'\n        }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Password;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PasswordModule = /*#__PURE__*/(() => {\n  class PasswordModule {\n    static ɵfac = function PasswordModule_Factory(t) {\n      return new (t || PasswordModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PasswordModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, InputTextModule, AutoFocusModule, TimesIcon, EyeSlashIcon, EyeIcon, SharedModule]\n    });\n  }\n  return PasswordModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MapperPipe, Password, PasswordDirective, PasswordModule, Password_VALUE_ACCESSOR };\n//# sourceMappingURL=primeng-password.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}