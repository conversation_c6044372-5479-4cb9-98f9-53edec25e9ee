{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n// Interceptors\nimport { AuthInterceptor, CsrfInterceptor, SecurityHeadersInterceptor } from './interceptors/auth.interceptor';\n// Guards\nimport { AuthGuard, NoAuthGuard, RoleGuard, PermissionGuard } from './guards/auth.guard';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { AuthorizationService } from './services/authorization.service';\nimport { LanguageService } from './services/language.service';\nimport * as i0 from \"@angular/core\";\nexport let CoreModule = /*#__PURE__*/(() => {\n  class CoreModule {\n    constructor(parentModule) {\n      if (parentModule) {\n        throw new Error('CoreModule is already loaded. Import it in the AppModule only.');\n      }\n    }\n    static {\n      this.ɵfac = function CoreModule_Factory(t) {\n        return new (t || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CoreModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [\n        // Services\n        AuthService, AuthorizationService, LanguageService,\n        // Guards\n        AuthGuard, NoAuthGuard, RoleGuard, PermissionGuard,\n        // HTTP Interceptors\n        {\n          provide: HTTP_INTERCEPTORS,\n          useClass: SecurityHeadersInterceptor,\n          multi: true\n        }, {\n          provide: HTTP_INTERCEPTORS,\n          useClass: CsrfInterceptor,\n          multi: true\n        }, {\n          provide: HTTP_INTERCEPTORS,\n          useClass: AuthInterceptor,\n          multi: true\n        }],\n        imports: [CommonModule]\n      });\n    }\n  }\n  return CoreModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}