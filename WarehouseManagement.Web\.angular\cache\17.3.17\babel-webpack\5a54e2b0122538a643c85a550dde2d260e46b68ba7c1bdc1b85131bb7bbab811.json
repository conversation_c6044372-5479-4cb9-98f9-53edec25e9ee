{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputGroupAddon displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nconst _c0 = [\"*\"];\nclass InputGroupAddon {\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  static ɵfac = function InputGroupAddon_Factory(t) {\n    return new (t || InputGroupAddon)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputGroupAddon,\n    selectors: [[\"p-inputGroupAddon\"]],\n    hostAttrs: [1, \"p-element\", \"p-inputgroup-addon\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\"\n    },\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 3,\n    consts: [[3, \"ngClass\", \"ngStyle\"]],\n    template: function InputGroupAddon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.styleClass)(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"inputgroupaddon\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupAddon, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputGroupAddon',\n      template: `\n        <div [attr.data-pc-name]=\"'inputgroupaddon'\" [ngClass]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      host: {\n        class: 'p-element p-inputgroup-addon'\n      }\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\nclass InputGroupAddonModule {\n  static ɵfac = function InputGroupAddonModule_Factory(t) {\n    return new (t || InputGroupAddonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputGroupAddonModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputGroupAddonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [InputGroupAddon, SharedModule],\n      declarations: [InputGroupAddon]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroupAddon, InputGroupAddonModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "Component", "Input", "NgModule", "SharedModule", "_c0", "InputGroupAddon", "style", "styleClass", "ɵfac", "InputGroupAddon_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "inputs", "ngContentSelectors", "decls", "vars", "consts", "template", "InputGroupAddon_Template", "rf", "ctx", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵproperty", "ɵɵattribute", "dependencies", "Ng<PERSON><PERSON>", "NgStyle", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "InputGroupAddonModule", "InputGroupAddonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/primeng/fesm2022/primeng-inputgroupaddon.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputGroupAddon displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nclass InputGroupAddon {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddon, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.0.1\", type: InputGroupAddon, selector: \"p-inputGroupAddon\", inputs: { style: \"style\", styleClass: \"styleClass\" }, host: { classAttribute: \"p-element p-inputgroup-addon\" }, ngImport: i0, template: `\n        <div [attr.data-pc-name]=\"'inputgroupaddon'\" [ngClass]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-inputGroupAddon',\n                    template: `\n        <div [attr.data-pc-name]=\"'inputgroupaddon'\" [ngClass]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    host: {\n                        class: 'p-element p-inputgroup-addon'\n                    }\n                }]\n        }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }] } });\nclass InputGroupAddonModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddonModule, declarations: [InputGroupAddon], imports: [CommonModule], exports: [InputGroupAddon, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddonModule, imports: [CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.0.1\", ngImport: i0, type: InputGroupAddonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [InputGroupAddon, SharedModule],\n                    declarations: [InputGroupAddon]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroupAddon, InputGroupAddonModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC1D,SAASC,YAAY,QAAQ,aAAa;;AAE1C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAIA,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV,OAAOC,IAAI,YAAAC,wBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFL,eAAe;EAAA;EAClH,OAAOM,IAAI,kBAD8EZ,EAAE,CAAAa,iBAAA;IAAAC,IAAA,EACJR,eAAe;IAAAS,SAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAV,KAAA;MAAAC,UAAA;IAAA;IAAAU,kBAAA,EAAAb,GAAA;IAAAc,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADbxB,EAAE,CAAA0B,eAAA;QAAF1B,EAAE,CAAA2B,cAAA,YAEF,CAAC;QAFD3B,EAAE,CAAA4B,YAAA,EAG3D,CAAC;QAHwD5B,EAAE,CAAA6B,YAAA,CAIlF,CAAC;MAAA;MAAA,IAAAL,EAAA;QAJ+ExB,EAAE,CAAA8B,UAAA,YAAAL,GAAA,CAAAjB,UAErB,CAAC,YAAAiB,GAAA,CAAAlB,KAAiB,CAAC;QAFAP,EAAE,CAAA+B,WAAA;MAAA;IAAA;IAAAC,YAAA,GAK9BlC,EAAE,CAACmC,OAAO,EAAoFnC,EAAE,CAACoC,OAAO;IAAAC,aAAA;EAAA;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAP6FpC,EAAE,CAAAqC,iBAAA,CAOJ/B,eAAe,EAAc,CAAC;IAC7GQ,IAAI,EAAEb,SAAS;IACfqC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BjB,QAAQ,EAAE;AAC9B;AACA;AACA;AACA,KAAK;MACekB,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElC,KAAK,EAAE,CAAC;MACtBO,IAAI,EAAEZ;IACV,CAAC,CAAC;IAAEM,UAAU,EAAE,CAAC;MACbM,IAAI,EAAEZ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwC,qBAAqB,CAAC;EACxB,OAAOjC,IAAI,YAAAkC,8BAAAhC,CAAA;IAAA,YAAAA,CAAA,IAAwF+B,qBAAqB;EAAA;EACxH,OAAOE,IAAI,kBA3B8E5C,EAAE,CAAA6C,gBAAA;IAAA/B,IAAA,EA2BS4B;EAAqB;EACzH,OAAOI,IAAI,kBA5B8E9C,EAAE,CAAA+C,gBAAA;IAAAC,OAAA,GA4B0CjD,YAAY,EAAEK,YAAY;EAAA;AACnK;AACA;EAAA,QAAAgC,SAAA,oBAAAA,SAAA,KA9B6FpC,EAAE,CAAAqC,iBAAA,CA8BJK,qBAAqB,EAAc,CAAC;IACnH5B,IAAI,EAAEX,QAAQ;IACdmC,IAAI,EAAE,CAAC;MACCU,OAAO,EAAE,CAACjD,YAAY,CAAC;MACvBkD,OAAO,EAAE,CAAC3C,eAAe,EAAEF,YAAY,CAAC;MACxC8C,YAAY,EAAE,CAAC5C,eAAe;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,eAAe,EAAEoC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}