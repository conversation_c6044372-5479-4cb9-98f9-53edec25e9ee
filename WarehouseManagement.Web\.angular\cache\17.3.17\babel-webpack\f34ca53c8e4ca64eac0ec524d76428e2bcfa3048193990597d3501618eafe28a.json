{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n// Interceptors\nimport { AuthInterceptor, CsrfInterceptor, SecurityHeadersInterceptor } from './interceptors/auth.interceptor';\n// Directives\nimport { HasPermissionDirective, HasRoleDirective, IsAuthenticatedDirective } from './directives/has-permission.directive';\n// Guards\nimport { AuthGuard, NoAuthGuard, RoleGuard, PermissionGuard } from './guards/auth.guard';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { AuthorizationService } from './services/authorization.service';\nimport { LanguageService } from './services/language.service';\nimport * as i0 from \"@angular/core\";\nexport class CoreModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');\n    }\n  }\n  static {\n    this.ɵfac = function CoreModule_Factory(t) {\n      return new (t || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CoreModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [\n      // Services\n      AuthService, AuthorizationService, LanguageService,\n      // Guards\n      AuthGuard, NoAuthGuard, RoleGuard, PermissionGuard,\n      // HTTP Interceptors\n      {\n        provide: HTTP_INTERCEPTORS,\n        useClass: SecurityHeadersInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: CsrfInterceptor,\n        multi: true\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }],\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreModule, {\n    declarations: [HasPermissionDirective, HasRoleDirective, IsAuthenticatedDirective],\n    imports: [CommonModule],\n    exports: [HasPermissionDirective, HasRoleDirective, IsAuthenticatedDirective]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "HTTP_INTERCEPTORS", "AuthInterceptor", "CsrfInterceptor", "SecurityHeadersInterceptor", "HasPermissionDirective", "HasRoleDirective", "IsAuthenticatedDirective", "<PERSON><PERSON><PERSON><PERSON>", "NoAuth<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "PermissionGuard", "AuthService", "AuthorizationService", "LanguageService", "CoreModule", "constructor", "parentModule", "Error", "i0", "ɵɵinject", "provide", "useClass", "multi", "imports", "declarations", "exports"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\core.module.ts"], "sourcesContent": ["import { NgModule, Optional, SkipSelf } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\n\n// Interceptors\nimport { \n  AuthInterceptor, \n  CsrfInterceptor, \n  SecurityHeadersInterceptor \n} from './interceptors/auth.interceptor';\n\n// Directives\nimport { \n  HasPermissionDirective, \n  HasRoleDirective, \n  IsAuthenticatedDirective \n} from './directives/has-permission.directive';\n\n// Guards\nimport { \n  AuthGuard, \n  NoAuthGuard, \n  RoleGuard, \n  PermissionGuard \n} from './guards/auth.guard';\n\n// Services\nimport { AuthService } from './services/auth.service';\nimport { AuthorizationService } from './services/authorization.service';\nimport { LanguageService } from './services/language.service';\n\n@NgModule({\n  declarations: [\n    HasPermissionDirective,\n    HasRoleDirective,\n    IsAuthenticatedDirective\n  ],\n  imports: [\n    CommonModule\n  ],\n  providers: [\n    // Services\n    AuthService,\n    AuthorizationService,\n    LanguageService,\n    \n    // Guards\n    AuthGuard,\n    NoAuthGuard,\n    RoleGuard,\n    PermissionGuard,\n    \n    // HTTP Interceptors\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: SecurityHeadersInterceptor,\n      multi: true\n    },\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: CsrfInterceptor,\n      multi: true\n    },\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }\n  ],\n  exports: [\n    HasPermissionDirective,\n    HasRoleDirective,\n    IsAuthenticatedDirective\n  ]\n})\nexport class CoreModule {\n  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {\n    if (parentModule) {\n      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,sBAAsB;AAExD;AACA,SACEC,eAAe,EACfC,eAAe,EACfC,0BAA0B,QACrB,iCAAiC;AAExC;AACA,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,wBAAwB,QACnB,uCAAuC;AAE9C;AACA,SACEC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,qBAAqB;AAE5B;AACA,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,eAAe,QAAQ,6BAA6B;;AA8C7D,OAAM,MAAOC,UAAU;EACrBC,YAAoCC,YAAwB;IAC1D,IAAIA,YAAY,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,gEAAgE,CAAC;;EAErF;;;uBALWH,UAAU,EAAAI,EAAA,CAAAC,QAAA,CAAAL,UAAA;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;iBAnCV;MACT;MACAH,WAAW,EACXC,oBAAoB,EACpBC,eAAe;MAEf;MACAN,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,eAAe;MAEf;MACA;QACEU,OAAO,EAAEpB,iBAAiB;QAC1BqB,QAAQ,EAAElB,0BAA0B;QACpCmB,KAAK,EAAE;OACR,EACD;QACEF,OAAO,EAAEpB,iBAAiB;QAC1BqB,QAAQ,EAAEnB,eAAe;QACzBoB,KAAK,EAAE;OACR,EACD;QACEF,OAAO,EAAEpB,iBAAiB;QAC1BqB,QAAQ,EAAEpB,eAAe;QACzBqB,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GA9BCxB,YAAY;IAAA;EAAA;;;2EAqCHe,UAAU;IAAAU,YAAA,GA1CnBpB,sBAAsB,EACtBC,gBAAgB,EAChBC,wBAAwB;IAAAiB,OAAA,GAGxBxB,YAAY;IAAA0B,OAAA,GAgCZrB,sBAAsB,EACtBC,gBAAgB,EAChBC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}