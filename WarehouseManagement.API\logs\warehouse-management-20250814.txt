2025-08-14 10:01:11.029 +03:00 [ERR] An error occurred while ensuring database creation
System.InvalidOperationException: No backing field could be found for property 'Invoice.BalanceAmount' and the property does not have a setter.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidateFieldMapping>g__Validate|24_0(ITypeBase typeBase)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateFieldMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
2025-08-14 10:01:11.104 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:01:11.145 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:01:11.150 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:01:11.151 +03:00 [INF] Hosting environment: Production
2025-08-14 10:01:11.152 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:01:16.371 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 10:01:16.464 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 10:01:16.494 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 124.6071ms
2025-08-14 10:01:16.512 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 10:01:30.990 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:01:30.999 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 9.2852ms
2025-08-14 10:01:31.005 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:01:51.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 10:01:51.655 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 5.1107ms
2025-08-14 10:01:51.661 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 10:01:58.897 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 10:01:58.901 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 4.0318ms
2025-08-14 10:01:58.905 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 10:02:12.155 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:02:12.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 3.7601ms
2025-08-14 10:02:12.170 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:02:12.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:02:12.306 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 4.4087ms
2025-08-14 10:02:12.314 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:02:53.628 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 10:02:53.633 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 4.998ms
2025-08-14 10:02:53.637 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 10:03:08.819 +03:00 [INF] Application is shutting down...
2025-08-14 10:30:26.180 +03:00 [ERR] An error occurred while ensuring database creation
System.InvalidOperationException: No backing field could be found for property 'Invoice.BalanceAmount' and the property does not have a setter.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidateFieldMapping>g__Validate|24_0(ITypeBase typeBase)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateFieldMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
2025-08-14 10:30:26.295 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:30:26.346 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:30:26.350 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:30:26.351 +03:00 [INF] Hosting environment: Production
2025-08-14 10:30:26.352 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:31:17.895 +03:00 [INF] Application is shutting down...
2025-08-14 10:31:48.298 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.345 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.348 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.349 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.350 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.352 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'CashTransaction'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.353 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.354 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Expense'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.356 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.357 +03:00 [WRN] No store type was specified for the decimal property 'UnitCost' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.359 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Payment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.360 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.362 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.364 +03:00 [WRN] No store type was specified for the decimal property 'ReservedQuantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.052 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.054 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.055 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.057 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.058 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.060 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'CashTransaction'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.062 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.064 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Expense'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.065 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.067 +03:00 [WRN] No store type was specified for the decimal property 'UnitCost' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.068 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Payment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.069 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.071 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.072 +03:00 [WRN] No store type was specified for the decimal property 'ReservedQuantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.672 +03:00 [INF] Executed DbCommand (506ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [WarehouseManagementDb];
2025-08-14 10:31:52.807 +03:00 [INF] Executed DbCommand (128ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [WarehouseManagementDb] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-08-14 10:31:52.850 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 10:31:53.035 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [OpeningBalance] decimal(18,2) NOT NULL,
    [CurrentBalance] decimal(18,2) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.040 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [ParentCategoryId] int NULL,
    [Level] int NOT NULL,
    [Path] nvarchar(1000) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Categories_Categories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.045 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [City] nvarchar(max) NOT NULL,
    [State] nvarchar(max) NOT NULL,
    [Country] nvarchar(max) NOT NULL,
    [PostalCode] nvarchar(max) NOT NULL,
    [TaxNumber] nvarchar(max) NOT NULL,
    [CreditLimit] decimal(18,2) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.051 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Expenses] (
    [Id] int NOT NULL IDENTITY,
    [ExpenseNumber] nvarchar(max) NOT NULL,
    [ExpenseDate] datetime2 NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Category] int NOT NULL,
    [Reference] nvarchar(max) NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [IsApproved] bit NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Expenses] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.059 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockAdjustments] (
    [Id] int NOT NULL IDENTITY,
    [AdjustmentNumber] nvarchar(max) NOT NULL,
    [AdjustmentDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Reason] nvarchar(max) NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockAdjustments] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.064 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [City] nvarchar(max) NOT NULL,
    [State] nvarchar(max) NOT NULL,
    [Country] nvarchar(max) NOT NULL,
    [PostalCode] nvarchar(max) NOT NULL,
    [TaxNumber] nvarchar(max) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.069 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UnitsOfMeasurement] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Symbol] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_UnitsOfMeasurement] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.073 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Warehouses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Location] nvarchar(200) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Warehouses] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.078 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Invoices] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [DueDate] datetime2 NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [SubTotal] decimal(18,4) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TotalAmount] decimal(18,4) NOT NULL,
    [PaidAmount] decimal(18,4) NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [Reference] nvarchar(100) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Invoices_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Invoices_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.084 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Items] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Barcode] nvarchar(50) NOT NULL,
    [Description] nvarchar(1000) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitOfMeasurementId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [StorageLocation] nvarchar(100) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [MinimumOrderLimit] decimal(18,4) NOT NULL,
    [MaximumOrderLimit] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [SellingPrice] decimal(18,4) NOT NULL,
    [ImagePath] nvarchar(500) NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Items] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Items_Categories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_UnitsOfMeasurement_UnitOfMeasurementId] FOREIGN KEY ([UnitOfMeasurementId]) REFERENCES [UnitsOfMeasurement] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.101 +03:00 [ERR] Failed executing DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transfers] (
    [Id] int NOT NULL IDENTITY,
    [TransferNumber] nvarchar(max) NOT NULL,
    [FromWarehouseId] int NOT NULL,
    [ToWarehouseId] int NOT NULL,
    [TransferDate] datetime2 NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Transfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transfers_Warehouses_FromWarehouseId] FOREIGN KEY ([FromWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Transfers_Warehouses_ToWarehouseId] FOREIGN KEY ([ToWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE CASCADE
);
2025-08-14 10:31:53.106 +03:00 [ERR] An error occurred while ensuring database creation
Microsoft.Data.SqlClient.SqlException (0x80131904): Introducing FOREIGN KEY constraint 'FK_Transfers_Warehouses_ToWarehouseId' on table 'Transfers' may cause cycles or multiple cascade paths. Specify ON DELETE NO ACTION or ON UPDATE NO ACTION, or modify other FOREIGN KEY constraints.
Could not create constraint or index. See previous errors.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.CreateTables()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
ClientConnectionId:108eaae9-7dba-48ac-addb-4566ed0644ac
Error Number:1785,State:0,Class:16
2025-08-14 10:31:53.128 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:31:53.170 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:31:53.173 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:31:53.175 +03:00 [INF] Hosting environment: Production
2025-08-14 10:31:53.176 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:34:04.731 +03:00 [INF] Application is shutting down...
2025-08-14 10:35:08.942 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 10:35:09.518 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 10:35:09.633 +03:00 [INF] Executed DbCommand (103ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 10:35:09.830 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 10:35:10.010 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.015 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [ParentCategoryId] int NULL,
    [Level] int NOT NULL,
    [Path] nvarchar(1000) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Categories_Categories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.019 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NOT NULL,
    [Address] nvarchar(500) NOT NULL,
    [City] nvarchar(100) NOT NULL,
    [State] nvarchar(100) NOT NULL,
    [Country] nvarchar(100) NOT NULL,
    [PostalCode] nvarchar(20) NOT NULL,
    [TaxNumber] nvarchar(50) NOT NULL,
    [CreditLimit] decimal(18,4) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.023 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Expenses] (
    [Id] int NOT NULL IDENTITY,
    [ExpenseNumber] nvarchar(50) NOT NULL,
    [ExpenseDate] datetime2 NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Category] int NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [IsApproved] bit NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Expenses] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.029 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockAdjustments] (
    [Id] int NOT NULL IDENTITY,
    [AdjustmentNumber] nvarchar(50) NOT NULL,
    [AdjustmentDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Reason] nvarchar(500) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockAdjustments] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.036 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NOT NULL,
    [Address] nvarchar(500) NOT NULL,
    [City] nvarchar(100) NOT NULL,
    [State] nvarchar(100) NOT NULL,
    [Country] nvarchar(100) NOT NULL,
    [PostalCode] nvarchar(20) NOT NULL,
    [TaxNumber] nvarchar(50) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.040 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UnitsOfMeasurement] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Symbol] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_UnitsOfMeasurement] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.044 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Warehouses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Location] nvarchar(200) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Warehouses] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.048 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Invoices] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [DueDate] datetime2 NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [SubTotal] decimal(18,4) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TotalAmount] decimal(18,4) NOT NULL,
    [PaidAmount] decimal(18,4) NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [Reference] nvarchar(100) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Invoices_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Invoices_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.054 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Items] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Barcode] nvarchar(50) NOT NULL,
    [Description] nvarchar(1000) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitOfMeasurementId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [StorageLocation] nvarchar(100) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [MinimumOrderLimit] decimal(18,4) NOT NULL,
    [MaximumOrderLimit] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [SellingPrice] decimal(18,4) NOT NULL,
    [ImagePath] nvarchar(500) NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Items] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Items_Categories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_UnitsOfMeasurement_UnitOfMeasurementId] FOREIGN KEY ([UnitOfMeasurementId]) REFERENCES [UnitsOfMeasurement] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.059 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transfers] (
    [Id] int NOT NULL IDENTITY,
    [TransferNumber] nvarchar(50) NOT NULL,
    [FromWarehouseId] int NOT NULL,
    [ToWarehouseId] int NOT NULL,
    [TransferDate] datetime2 NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Transfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transfers_Warehouses_FromWarehouseId] FOREIGN KEY ([FromWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Transfers_Warehouses_ToWarehouseId] FOREIGN KEY ([ToWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.068 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Payments] (
    [Id] int NOT NULL IDENTITY,
    [PaymentNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [PaymentDate] datetime2 NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Method] int NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [InvoiceId] int NULL,
    [Status] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Payments] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Payments_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Payments_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Payments_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.074 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceItems] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceId] int NOT NULL,
    [ItemId] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [UnitPrice] decimal(18,4) NOT NULL,
    [DiscountPercentage] decimal(5,2) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TaxPercentage] decimal(5,2) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [LineTotal] decimal(18,4) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_InvoiceItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceItems_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_InvoiceItems_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.082 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ItemAlternatives] (
    [Id] int NOT NULL IDENTITY,
    [ItemId] int NOT NULL,
    [AlternativeItemId] int NOT NULL,
    [Notes] nvarchar(500) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_ItemAlternatives] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ItemAlternatives_Items_AlternativeItemId] FOREIGN KEY ([AlternativeItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_ItemAlternatives_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.087 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockBalances] (
    [Id] int NOT NULL IDENTITY,
    [ItemId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [ReservedQuantity] decimal(18,4) NOT NULL,
    [AverageCost] decimal(18,4) NOT NULL,
    [LastMovementDate] datetime2 NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockBalances] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_StockBalances_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockBalances_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.094 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryMovements] (
    [Id] int NOT NULL IDENTITY,
    [DocumentNumber] nvarchar(50) NOT NULL,
    [ItemId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [MovementType] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [MovementDate] datetime2 NOT NULL,
    [InvoiceId] int NULL,
    [TransferId] int NULL,
    [AdjustmentId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_InventoryMovements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InventoryMovements_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_StockAdjustments_AdjustmentId] FOREIGN KEY ([AdjustmentId]) REFERENCES [StockAdjustments] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Transfers_TransferId] FOREIGN KEY ([TransferId]) REFERENCES [Transfers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.100 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AccountStatements] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [DebitAmount] decimal(18,4) NOT NULL,
    [CreditAmount] decimal(18,4) NOT NULL,
    [Balance] decimal(18,4) NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [InvoiceId] int NULL,
    [PaymentId] int NULL,
    [PaymentId1] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_AccountStatements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AccountStatements_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Payments_PaymentId] FOREIGN KEY ([PaymentId]) REFERENCES [Payments] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Payments_PaymentId1] FOREIGN KEY ([PaymentId1]) REFERENCES [Payments] ([Id]),
    CONSTRAINT [FK_AccountStatements_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.105 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashTransactions] (
    [Id] int NOT NULL IDENTITY,
    [CashRegisterId] int NOT NULL,
    [TransactionNumber] nvarchar(50) NOT NULL,
    [TransactionDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [PaymentId] int NULL,
    [ExpenseId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashTransactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_CashTransactions_CashRegisters_CashRegisterId] FOREIGN KEY ([CashRegisterId]) REFERENCES [CashRegisters] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_CashTransactions_Expenses_ExpenseId] FOREIGN KEY ([ExpenseId]) REFERENCES [Expenses] ([Id]),
    CONSTRAINT [FK_CashTransactions_Payments_PaymentId] FOREIGN KEY ([PaymentId]) REFERENCES [Payments] ([Id])
);
2025-08-14 10:35:10.109 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_CustomerId] ON [AccountStatements] ([CustomerId]);
2025-08-14 10:35:10.112 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_InvoiceId] ON [AccountStatements] ([InvoiceId]);
2025-08-14 10:35:10.115 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_PaymentId] ON [AccountStatements] ([PaymentId]);
2025-08-14 10:35:10.118 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_PaymentId1] ON [AccountStatements] ([PaymentId1]);
2025-08-14 10:35:10.122 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_SupplierId] ON [AccountStatements] ([SupplierId]);
2025-08-14 10:35:10.125 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_CashRegisters_Code] ON [CashRegisters] ([Code]);
2025-08-14 10:35:10.128 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_CashRegisterId] ON [CashTransactions] ([CashRegisterId]);
2025-08-14 10:35:10.130 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_ExpenseId] ON [CashTransactions] ([ExpenseId]);
2025-08-14 10:35:10.133 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_PaymentId] ON [CashTransactions] ([PaymentId]);
2025-08-14 10:35:10.137 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_CashTransactions_TransactionNumber] ON [CashTransactions] ([TransactionNumber]);
2025-08-14 10:35:10.152 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-08-14 10:35:10.156 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_ParentCategoryId] ON [Categories] ([ParentCategoryId]);
2025-08-14 10:35:10.159 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_Code] ON [Customers] ([Code]);
2025-08-14 10:35:10.163 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_Email] ON [Customers] ([Email]) WHERE [Email] IS NOT NULL AND [Email] != '';
2025-08-14 10:35:10.166 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Expenses_ExpenseNumber] ON [Expenses] ([ExpenseNumber]);
2025-08-14 10:35:10.170 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_AdjustmentId] ON [InventoryMovements] ([AdjustmentId]);
2025-08-14 10:35:10.173 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_InvoiceId] ON [InventoryMovements] ([InvoiceId]);
2025-08-14 10:35:10.176 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_ItemId] ON [InventoryMovements] ([ItemId]);
2025-08-14 10:35:10.178 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_TransferId] ON [InventoryMovements] ([TransferId]);
2025-08-14 10:35:10.181 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_WarehouseId] ON [InventoryMovements] ([WarehouseId]);
2025-08-14 10:35:10.184 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_InvoiceId] ON [InvoiceItems] ([InvoiceId]);
2025-08-14 10:35:10.187 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_ItemId] ON [InvoiceItems] ([ItemId]);
2025-08-14 10:35:10.190 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Invoices_CustomerId] ON [Invoices] ([CustomerId]);
2025-08-14 10:35:10.193 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Invoices_InvoiceNumber] ON [Invoices] ([InvoiceNumber]);
2025-08-14 10:35:10.196 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Invoices_SupplierId] ON [Invoices] ([SupplierId]);
2025-08-14 10:35:10.199 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ItemAlternatives_AlternativeItemId] ON [ItemAlternatives] ([AlternativeItemId]);
2025-08-14 10:35:10.204 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ItemAlternatives_ItemId_AlternativeItemId] ON [ItemAlternatives] ([ItemId], [AlternativeItemId]);
2025-08-14 10:35:10.208 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Items_Barcode] ON [Items] ([Barcode]) WHERE [Barcode] IS NOT NULL AND [Barcode] != '';
2025-08-14 10:35:10.210 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_CategoryId] ON [Items] ([CategoryId]);
2025-08-14 10:35:10.213 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Items_Code] ON [Items] ([Code]);
2025-08-14 10:35:10.217 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_UnitOfMeasurementId] ON [Items] ([UnitOfMeasurementId]);
2025-08-14 10:35:10.221 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_WarehouseId] ON [Items] ([WarehouseId]);
2025-08-14 10:35:10.223 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_CustomerId] ON [Payments] ([CustomerId]);
2025-08-14 10:35:10.226 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_InvoiceId] ON [Payments] ([InvoiceId]);
2025-08-14 10:35:10.229 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Payments_PaymentNumber] ON [Payments] ([PaymentNumber]);
2025-08-14 10:35:10.231 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_SupplierId] ON [Payments] ([SupplierId]);
2025-08-14 10:35:10.234 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_StockAdjustments_AdjustmentNumber] ON [StockAdjustments] ([AdjustmentNumber]);
2025-08-14 10:35:10.237 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_StockBalances_ItemId_WarehouseId] ON [StockBalances] ([ItemId], [WarehouseId]);
2025-08-14 10:35:10.240 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockBalances_WarehouseId] ON [StockBalances] ([WarehouseId]);
2025-08-14 10:35:10.243 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_Code] ON [Suppliers] ([Code]);
2025-08-14 10:35:10.246 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_Email] ON [Suppliers] ([Email]) WHERE [Email] IS NOT NULL AND [Email] != '';
2025-08-14 10:35:10.249 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transfers_FromWarehouseId] ON [Transfers] ([FromWarehouseId]);
2025-08-14 10:35:10.252 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transfers_ToWarehouseId] ON [Transfers] ([ToWarehouseId]);
2025-08-14 10:35:10.255 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Transfers_TransferNumber] ON [Transfers] ([TransferNumber]);
2025-08-14 10:35:10.258 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Warehouses_Code] ON [Warehouses] ([Code]);
2025-08-14 10:35:10.266 +03:00 [INF] Database ensured created successfully
2025-08-14 10:35:10.272 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:35:10.303 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:35:10.306 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:35:10.307 +03:00 [INF] Hosting environment: Production
2025-08-14 10:35:10.308 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:05:42.756 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:05:45.032 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:05:45.124 +03:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:05:45.129 +03:00 [INF] Database ensured created successfully
2025-08-14 12:05:45.135 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:05:45.150 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:05:45.265 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:05:45.269 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:05:45.270 +03:00 [INF] Hosting environment: Production
2025-08-14 12:05:45.271 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:44:12.903 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:44:14.453 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:44:14.531 +03:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:44:14.536 +03:00 [INF] Database ensured created successfully
2025-08-14 12:44:14.541 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:44:14.554 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:44:14.600 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:44:14.603 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:44:14.604 +03:00 [INF] Hosting environment: Production
2025-08-14 12:44:14.605 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:45:03.775 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 12:45:03.797 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 12:45:05.191 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 1417.7306ms
2025-08-14 12:45:05.200 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 12:57:26.944 +03:00 [INF] Application is shutting down...
2025-08-14 12:58:17.282 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:58:18.969 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:58:19.045 +03:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:58:19.049 +03:00 [INF] Database ensured created successfully
2025-08-14 12:58:19.054 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:58:19.069 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:58:19.120 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:58:19.123 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:58:19.125 +03:00 [INF] Hosting environment: Production
2025-08-14 12:58:19.126 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:58:25.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 12:58:25.669 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 12:58:25.698 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 55.4814ms
2025-08-14 12:58:25.709 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 12:58:37.343 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 12:58:37.348 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 5.5233ms
2025-08-14 12:58:37.355 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 12:58:49.091 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 12:58:49.098 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 6.368ms
2025-08-14 12:58:49.102 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 12:59:00.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 12:59:00.786 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 4.024ms
2025-08-14 12:59:00.789 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 12:59:32.787 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:59:33.341 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:59:33.387 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:59:33.391 +03:00 [INF] Database ensured created successfully
2025-08-14 12:59:33.396 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:59:33.409 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:59:33.458 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:59:33.461 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:59:33.462 +03:00 [INF] Hosting environment: Production
2025-08-14 12:59:33.463 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 13:00:26.636 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:00:26.658 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 13:00:26.689 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 52.9788ms
2025-08-14 13:00:26.697 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:01:02.982 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:01:02.988 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 6.3953ms
2025-08-14 13:01:02.994 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:01:38.197 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 13:01:38.203 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 6.4041ms
2025-08-14 13:01:38.209 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 13:05:52.806 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 13:05:53.322 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 13:05:53.367 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 13:05:53.371 +03:00 [INF] Database ensured created successfully
2025-08-14 13:05:53.376 +03:00 [INF] Starting Warehouse Management API
2025-08-14 13:05:53.387 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 13:05:53.433 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 13:05:53.437 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 13:05:53.438 +03:00 [INF] Hosting environment: Production
2025-08-14 13:05:53.440 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 13:05:59.098 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:05:59.120 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 13:05:59.150 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 53.0483ms
2025-08-14 13:05:59.157 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:06:05.977 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 13:06:05.983 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 6.0545ms
2025-08-14 13:06:05.989 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 13:06:11.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 13:06:11.821 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 6.6391ms
2025-08-14 13:06:11.826 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 13:06:19.206 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 13:06:19.210 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 3.7622ms
2025-08-14 13:06:19.214 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 13:06:54.431 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:06:54.435 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 4.0549ms
2025-08-14 13:06:54.439 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:07:00.255 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api - null null
2025-08-14 13:07:00.260 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api - 404 0 null 4.8266ms
2025-08-14 13:07:00.264 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/api, Response status code: 404
2025-08-14 13:08:33.733 +03:00 [INF] Application is shutting down...
2025-08-14 13:12:00.547 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 13:12:01.175 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 13:12:01.218 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 13:12:01.222 +03:00 [INF] Database ensured created successfully
2025-08-14 13:12:01.227 +03:00 [INF] Starting Warehouse Management API
2025-08-14 13:12:01.241 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 13:12:01.292 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 13:12:01.294 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 13:12:01.296 +03:00 [INF] Hosting environment: Production
2025-08-14 13:12:01.297 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:19:38.515 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:19:40.133 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:19:40.212 +03:00 [INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 14:19:40.216 +03:00 [INF] Database ensured created successfully
2025-08-14 14:19:40.223 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:19:40.240 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:19:40.307 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 14:19:40.310 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:19:40.312 +03:00 [INF] Hosting environment: Production
2025-08-14 14:19:40.313 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:19:51.136 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 14:19:51.158 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 14:19:51.189 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 54.0701ms
2025-08-14 14:19:51.194 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 14:19:51.198 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 14:19:51.204 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 10.3199ms
2025-08-14 14:19:51.215 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 14:20:11.553 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 14:20:11.561 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 7.4703ms
2025-08-14 14:20:11.565 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 14:21:02.946 +03:00 [INF] Application is shutting down...
2025-08-14 14:22:37.401 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:22:38.339 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:22:38.387 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 14:22:38.390 +03:00 [INF] Database ensured created successfully
2025-08-14 14:22:38.398 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:22:38.456 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:22:38.867 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:22:38.869 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:22:38.971 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:22:38.974 +03:00 [INF] Hosting environment: Development
2025-08-14 14:22:38.975 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:22:39.748 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/ - null null
2025-08-14 14:22:39.979 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/ - 301 0 null 236.9608ms
2025-08-14 14:22:39.990 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 14:22:40.076 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 85.4402ms
2025-08-14 14:22:40.100 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui.css - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui-standalone-preset.js - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui-bundle.js - null null
2025-08-14 14:22:40.110 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_vs/browserLink - null null
2025-08-14 14:22:40.136 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - 200 16531 application/javascript; charset=utf-8 34.9122ms
2025-08-14 14:22:40.222 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-08-14 14:22:40.222 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-08-14 14:22:40.225 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui.css - 200 144929 text/css 125.0301ms
2025-08-14 14:22:40.227 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_vs/browserLink - 200 null text/javascript; charset=UTF-8 118.0195ms
2025-08-14 14:22:40.227 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui-standalone-preset.js - 200 312163 text/javascript 126.5256ms
2025-08-14 14:22:40.335 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-08-14 14:22:40.338 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui-bundle.js - 200 1061536 text/javascript 236.8783ms
2025-08-14 14:22:40.644 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 14:22:40.674 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/favicon-32x32.png - null null
2025-08-14 14:22:40.678 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-08-14 14:22:40.680 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/favicon-32x32.png - 200 628 image/png 7.3533ms
2025-08-14 14:22:40.687 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 42.924ms
2025-08-14 14:24:48.392 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/api/auth/validate - null null
2025-08-14 14:24:48.414 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:24:48.584 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:24:48.605 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:24:48.621 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/api/auth/validate - 401 0 null 229.7458ms
2025-08-14 14:25:18.634 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/api/auth/validate - null null
2025-08-14 14:25:18.648 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:25:18.655 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:25:18.659 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:25:18.660 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/api/auth/validate - 401 0 null 25.89ms
