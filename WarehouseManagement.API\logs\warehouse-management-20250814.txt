2025-08-14 10:01:11.029 +03:00 [ERR] An error occurred while ensuring database creation
System.InvalidOperationException: No backing field could be found for property 'Invoice.BalanceAmount' and the property does not have a setter.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidateFieldMapping>g__Validate|24_0(ITypeBase typeBase)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateFieldMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
2025-08-14 10:01:11.104 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:01:11.145 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:01:11.150 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:01:11.151 +03:00 [INF] Hosting environment: Production
2025-08-14 10:01:11.152 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:01:16.371 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 10:01:16.464 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 10:01:16.494 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 124.6071ms
2025-08-14 10:01:16.512 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 10:01:30.990 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:01:30.999 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 9.2852ms
2025-08-14 10:01:31.005 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:01:51.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 10:01:51.655 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 5.1107ms
2025-08-14 10:01:51.661 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 10:01:58.897 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 10:01:58.901 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 4.0318ms
2025-08-14 10:01:58.905 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 10:02:12.155 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:02:12.159 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 3.7601ms
2025-08-14 10:02:12.170 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:02:12.302 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 10:02:12.306 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 4.4087ms
2025-08-14 10:02:12.314 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 10:02:53.628 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 10:02:53.633 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 4.998ms
2025-08-14 10:02:53.637 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 10:03:08.819 +03:00 [INF] Application is shutting down...
2025-08-14 10:30:26.180 +03:00 [ERR] An error occurred while ensuring database creation
System.InvalidOperationException: No backing field could be found for property 'Invoice.BalanceAmount' and the property does not have a setter.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.<ValidateFieldMapping>g__Validate|24_0(ITypeBase typeBase)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateFieldMapping(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_InternalServiceProvider()
   at Microsoft.EntityFrameworkCore.DbContext.Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<System.IServiceProvider>.get_Instance()
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService(IInfrastructure`1 accessor, Type serviceType)
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.InfrastructureExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.AccessorExtensions.GetService[TService](IInfrastructure`1 accessor)
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.get_Dependencies()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
2025-08-14 10:30:26.295 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:30:26.346 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:30:26.350 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:30:26.351 +03:00 [INF] Hosting environment: Production
2025-08-14 10:30:26.352 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:31:17.895 +03:00 [INF] Application is shutting down...
2025-08-14 10:31:48.298 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.345 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.348 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.349 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.350 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.352 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'CashTransaction'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.353 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.354 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Expense'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.356 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.357 +03:00 [WRN] No store type was specified for the decimal property 'UnitCost' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.359 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Payment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.360 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.362 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:48.364 +03:00 [WRN] No store type was specified for the decimal property 'ReservedQuantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.052 +03:00 [WRN] No store type was specified for the decimal property 'Balance' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.054 +03:00 [WRN] No store type was specified for the decimal property 'CreditAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.055 +03:00 [WRN] No store type was specified for the decimal property 'DebitAmount' on entity type 'AccountStatement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.057 +03:00 [WRN] No store type was specified for the decimal property 'CurrentBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.058 +03:00 [WRN] No store type was specified for the decimal property 'OpeningBalance' on entity type 'CashRegister'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.060 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'CashTransaction'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.062 +03:00 [WRN] No store type was specified for the decimal property 'CreditLimit' on entity type 'Customer'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.064 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Expense'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.065 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.067 +03:00 [WRN] No store type was specified for the decimal property 'UnitCost' on entity type 'InventoryMovement'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.068 +03:00 [WRN] No store type was specified for the decimal property 'Amount' on entity type 'Payment'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.069 +03:00 [WRN] No store type was specified for the decimal property 'AverageCost' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.071 +03:00 [WRN] No store type was specified for the decimal property 'Quantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.072 +03:00 [WRN] No store type was specified for the decimal property 'ReservedQuantity' on entity type 'StockBalance'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-08-14 10:31:52.672 +03:00 [INF] Executed DbCommand (506ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
CREATE DATABASE [WarehouseManagementDb];
2025-08-14 10:31:52.807 +03:00 [INF] Executed DbCommand (128ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
IF SERVERPROPERTY('EngineEdition') <> 5
BEGIN
    ALTER DATABASE [WarehouseManagementDb] SET READ_COMMITTED_SNAPSHOT ON;
END;
2025-08-14 10:31:52.850 +03:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 10:31:53.035 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [OpeningBalance] decimal(18,2) NOT NULL,
    [CurrentBalance] decimal(18,2) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.040 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [ParentCategoryId] int NULL,
    [Level] int NOT NULL,
    [Path] nvarchar(1000) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Categories_Categories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.045 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [City] nvarchar(max) NOT NULL,
    [State] nvarchar(max) NOT NULL,
    [Country] nvarchar(max) NOT NULL,
    [PostalCode] nvarchar(max) NOT NULL,
    [TaxNumber] nvarchar(max) NOT NULL,
    [CreditLimit] decimal(18,2) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.051 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Expenses] (
    [Id] int NOT NULL IDENTITY,
    [ExpenseNumber] nvarchar(max) NOT NULL,
    [ExpenseDate] datetime2 NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Amount] decimal(18,2) NOT NULL,
    [Category] int NOT NULL,
    [Reference] nvarchar(max) NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [IsApproved] bit NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Expenses] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.059 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockAdjustments] (
    [Id] int NOT NULL IDENTITY,
    [AdjustmentNumber] nvarchar(max) NOT NULL,
    [AdjustmentDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Reason] nvarchar(max) NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockAdjustments] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.064 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Email] nvarchar(max) NOT NULL,
    [Phone] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [City] nvarchar(max) NOT NULL,
    [State] nvarchar(max) NOT NULL,
    [Country] nvarchar(max) NOT NULL,
    [PostalCode] nvarchar(max) NOT NULL,
    [TaxNumber] nvarchar(max) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.069 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UnitsOfMeasurement] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Symbol] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_UnitsOfMeasurement] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.073 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Warehouses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Location] nvarchar(200) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Warehouses] PRIMARY KEY ([Id])
);
2025-08-14 10:31:53.078 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Invoices] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [DueDate] datetime2 NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [SubTotal] decimal(18,4) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TotalAmount] decimal(18,4) NOT NULL,
    [PaidAmount] decimal(18,4) NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [Reference] nvarchar(100) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Invoices_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Invoices_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.084 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Items] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Barcode] nvarchar(50) NOT NULL,
    [Description] nvarchar(1000) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitOfMeasurementId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [StorageLocation] nvarchar(100) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [MinimumOrderLimit] decimal(18,4) NOT NULL,
    [MaximumOrderLimit] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [SellingPrice] decimal(18,4) NOT NULL,
    [ImagePath] nvarchar(500) NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Items] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Items_Categories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_UnitsOfMeasurement_UnitOfMeasurementId] FOREIGN KEY ([UnitOfMeasurementId]) REFERENCES [UnitsOfMeasurement] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:31:53.101 +03:00 [ERR] Failed executing DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transfers] (
    [Id] int NOT NULL IDENTITY,
    [TransferNumber] nvarchar(max) NOT NULL,
    [FromWarehouseId] int NOT NULL,
    [ToWarehouseId] int NOT NULL,
    [TransferDate] datetime2 NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(max) NOT NULL,
    [ApprovedBy] nvarchar(max) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Transfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transfers_Warehouses_FromWarehouseId] FOREIGN KEY ([FromWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_Transfers_Warehouses_ToWarehouseId] FOREIGN KEY ([ToWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE CASCADE
);
2025-08-14 10:31:53.106 +03:00 [ERR] An error occurred while ensuring database creation
Microsoft.Data.SqlClient.SqlException (0x80131904): Introducing FOREIGN KEY constraint 'FK_Transfers_Warehouses_ToWarehouseId' on table 'Transfers' may cause cycles or multiple cascade paths. Specify ON DELETE NO ACTION or ON UPDATE NO ACTION, or modify other FOREIGN KEY constraints.
Could not create constraint or index. See previous errors.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.CreateTables()
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreated()
   at Microsoft.EntityFrameworkCore.Infrastructure.DatabaseFacade.EnsureCreated()
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 96
ClientConnectionId:108eaae9-7dba-48ac-addb-4566ed0644ac
Error Number:1785,State:0,Class:16
2025-08-14 10:31:53.128 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:31:53.170 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:31:53.173 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:31:53.175 +03:00 [INF] Hosting environment: Production
2025-08-14 10:31:53.176 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 10:34:04.731 +03:00 [INF] Application is shutting down...
2025-08-14 10:35:08.942 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 10:35:09.518 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 10:35:09.633 +03:00 [INF] Executed DbCommand (103ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 10:35:09.830 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 10:35:10.010 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.015 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [ParentCategoryId] int NULL,
    [Level] int NOT NULL,
    [Path] nvarchar(1000) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Categories_Categories_ParentCategoryId] FOREIGN KEY ([ParentCategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.019 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NOT NULL,
    [Address] nvarchar(500) NOT NULL,
    [City] nvarchar(100) NOT NULL,
    [State] nvarchar(100) NOT NULL,
    [Country] nvarchar(100) NOT NULL,
    [PostalCode] nvarchar(20) NOT NULL,
    [TaxNumber] nvarchar(50) NOT NULL,
    [CreditLimit] decimal(18,4) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.023 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Expenses] (
    [Id] int NOT NULL IDENTITY,
    [ExpenseNumber] nvarchar(50) NOT NULL,
    [ExpenseDate] datetime2 NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Category] int NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [IsApproved] bit NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Expenses] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.029 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockAdjustments] (
    [Id] int NOT NULL IDENTITY,
    [AdjustmentNumber] nvarchar(50) NOT NULL,
    [AdjustmentDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Reason] nvarchar(500) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockAdjustments] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.036 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Suppliers] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Email] nvarchar(100) NOT NULL,
    [Phone] nvarchar(20) NOT NULL,
    [Address] nvarchar(500) NOT NULL,
    [City] nvarchar(100) NOT NULL,
    [State] nvarchar(100) NOT NULL,
    [Country] nvarchar(100) NOT NULL,
    [PostalCode] nvarchar(20) NOT NULL,
    [TaxNumber] nvarchar(50) NOT NULL,
    [PaymentTerms] int NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Suppliers] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.040 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UnitsOfMeasurement] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(max) NOT NULL,
    [Code] nvarchar(max) NOT NULL,
    [Symbol] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_UnitsOfMeasurement] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.044 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Warehouses] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Location] nvarchar(200) NOT NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Warehouses] PRIMARY KEY ([Id])
);
2025-08-14 10:35:10.048 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Invoices] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [InvoiceDate] datetime2 NOT NULL,
    [DueDate] datetime2 NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [SubTotal] decimal(18,4) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TotalAmount] decimal(18,4) NOT NULL,
    [PaidAmount] decimal(18,4) NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [Reference] nvarchar(100) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Invoices] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Invoices_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Invoices_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.054 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Items] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(200) NOT NULL,
    [Code] nvarchar(50) NOT NULL,
    [Barcode] nvarchar(50) NOT NULL,
    [Description] nvarchar(1000) NOT NULL,
    [CategoryId] int NOT NULL,
    [UnitOfMeasurementId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [StorageLocation] nvarchar(100) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [MinimumOrderLimit] decimal(18,4) NOT NULL,
    [MaximumOrderLimit] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [SellingPrice] decimal(18,4) NOT NULL,
    [ImagePath] nvarchar(500) NULL,
    [Type] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Items] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Items_Categories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_UnitsOfMeasurement_UnitOfMeasurementId] FOREIGN KEY ([UnitOfMeasurementId]) REFERENCES [UnitsOfMeasurement] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Items_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.059 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Transfers] (
    [Id] int NOT NULL IDENTITY,
    [TransferNumber] nvarchar(50) NOT NULL,
    [FromWarehouseId] int NOT NULL,
    [ToWarehouseId] int NOT NULL,
    [TransferDate] datetime2 NOT NULL,
    [Status] int NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [ApprovedBy] nvarchar(100) NULL,
    [ApprovedDate] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Transfers] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Transfers_Warehouses_FromWarehouseId] FOREIGN KEY ([FromWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Transfers_Warehouses_ToWarehouseId] FOREIGN KEY ([ToWarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.068 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Payments] (
    [Id] int NOT NULL IDENTITY,
    [PaymentNumber] nvarchar(50) NOT NULL,
    [Type] int NOT NULL,
    [PaymentDate] datetime2 NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Method] int NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [InvoiceId] int NULL,
    [Status] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_Payments] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Payments_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Payments_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Payments_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.074 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InvoiceItems] (
    [Id] int NOT NULL IDENTITY,
    [InvoiceId] int NOT NULL,
    [ItemId] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [UnitPrice] decimal(18,4) NOT NULL,
    [DiscountPercentage] decimal(5,2) NOT NULL,
    [DiscountAmount] decimal(18,4) NOT NULL,
    [TaxPercentage] decimal(5,2) NOT NULL,
    [TaxAmount] decimal(18,4) NOT NULL,
    [LineTotal] decimal(18,4) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_InvoiceItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InvoiceItems_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_InvoiceItems_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.082 +03:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [ItemAlternatives] (
    [Id] int NOT NULL IDENTITY,
    [ItemId] int NOT NULL,
    [AlternativeItemId] int NOT NULL,
    [Notes] nvarchar(500) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_ItemAlternatives] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ItemAlternatives_Items_AlternativeItemId] FOREIGN KEY ([AlternativeItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_ItemAlternatives_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.087 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockBalances] (
    [Id] int NOT NULL IDENTITY,
    [ItemId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [ReservedQuantity] decimal(18,4) NOT NULL,
    [AverageCost] decimal(18,4) NOT NULL,
    [LastMovementDate] datetime2 NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_StockBalances] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_StockBalances_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockBalances_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.094 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryMovements] (
    [Id] int NOT NULL IDENTITY,
    [DocumentNumber] nvarchar(50) NOT NULL,
    [ItemId] int NOT NULL,
    [WarehouseId] int NOT NULL,
    [MovementType] int NOT NULL,
    [Quantity] decimal(18,4) NOT NULL,
    [UnitCost] decimal(18,4) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [Notes] nvarchar(1000) NOT NULL,
    [MovementDate] datetime2 NOT NULL,
    [InvoiceId] int NULL,
    [TransferId] int NULL,
    [AdjustmentId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_InventoryMovements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_InventoryMovements_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Items_ItemId] FOREIGN KEY ([ItemId]) REFERENCES [Items] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_StockAdjustments_AdjustmentId] FOREIGN KEY ([AdjustmentId]) REFERENCES [StockAdjustments] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Transfers_TransferId] FOREIGN KEY ([TransferId]) REFERENCES [Transfers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryMovements_Warehouses_WarehouseId] FOREIGN KEY ([WarehouseId]) REFERENCES [Warehouses] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.100 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AccountStatements] (
    [Id] int NOT NULL IDENTITY,
    [TransactionDate] datetime2 NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [DebitAmount] decimal(18,4) NOT NULL,
    [CreditAmount] decimal(18,4) NOT NULL,
    [Balance] decimal(18,4) NOT NULL,
    [CustomerId] int NULL,
    [SupplierId] int NULL,
    [InvoiceId] int NULL,
    [PaymentId] int NULL,
    [PaymentId1] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_AccountStatements] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AccountStatements_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Invoices_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [Invoices] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Payments_PaymentId] FOREIGN KEY ([PaymentId]) REFERENCES [Payments] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_AccountStatements_Payments_PaymentId1] FOREIGN KEY ([PaymentId1]) REFERENCES [Payments] ([Id]),
    CONSTRAINT [FK_AccountStatements_Suppliers_SupplierId] FOREIGN KEY ([SupplierId]) REFERENCES [Suppliers] ([Id]) ON DELETE NO ACTION
);
2025-08-14 10:35:10.105 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashTransactions] (
    [Id] int NOT NULL IDENTITY,
    [CashRegisterId] int NOT NULL,
    [TransactionNumber] nvarchar(50) NOT NULL,
    [TransactionDate] datetime2 NOT NULL,
    [Type] int NOT NULL,
    [Amount] decimal(18,4) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Reference] nvarchar(100) NOT NULL,
    [PaymentId] int NULL,
    [ExpenseId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashTransactions] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_CashTransactions_CashRegisters_CashRegisterId] FOREIGN KEY ([CashRegisterId]) REFERENCES [CashRegisters] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_CashTransactions_Expenses_ExpenseId] FOREIGN KEY ([ExpenseId]) REFERENCES [Expenses] ([Id]),
    CONSTRAINT [FK_CashTransactions_Payments_PaymentId] FOREIGN KEY ([PaymentId]) REFERENCES [Payments] ([Id])
);
2025-08-14 10:35:10.109 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_CustomerId] ON [AccountStatements] ([CustomerId]);
2025-08-14 10:35:10.112 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_InvoiceId] ON [AccountStatements] ([InvoiceId]);
2025-08-14 10:35:10.115 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_PaymentId] ON [AccountStatements] ([PaymentId]);
2025-08-14 10:35:10.118 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_PaymentId1] ON [AccountStatements] ([PaymentId1]);
2025-08-14 10:35:10.122 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AccountStatements_SupplierId] ON [AccountStatements] ([SupplierId]);
2025-08-14 10:35:10.125 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_CashRegisters_Code] ON [CashRegisters] ([Code]);
2025-08-14 10:35:10.128 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_CashRegisterId] ON [CashTransactions] ([CashRegisterId]);
2025-08-14 10:35:10.130 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_ExpenseId] ON [CashTransactions] ([ExpenseId]);
2025-08-14 10:35:10.133 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_CashTransactions_PaymentId] ON [CashTransactions] ([PaymentId]);
2025-08-14 10:35:10.137 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_CashTransactions_TransactionNumber] ON [CashTransactions] ([TransactionNumber]);
2025-08-14 10:35:10.152 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Categories_Code] ON [Categories] ([Code]);
2025-08-14 10:35:10.156 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Categories_ParentCategoryId] ON [Categories] ([ParentCategoryId]);
2025-08-14 10:35:10.159 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_Code] ON [Customers] ([Code]);
2025-08-14 10:35:10.163 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Customers_Email] ON [Customers] ([Email]) WHERE [Email] IS NOT NULL AND [Email] != '';
2025-08-14 10:35:10.166 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Expenses_ExpenseNumber] ON [Expenses] ([ExpenseNumber]);
2025-08-14 10:35:10.170 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_AdjustmentId] ON [InventoryMovements] ([AdjustmentId]);
2025-08-14 10:35:10.173 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_InvoiceId] ON [InventoryMovements] ([InvoiceId]);
2025-08-14 10:35:10.176 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_ItemId] ON [InventoryMovements] ([ItemId]);
2025-08-14 10:35:10.178 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_TransferId] ON [InventoryMovements] ([TransferId]);
2025-08-14 10:35:10.181 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryMovements_WarehouseId] ON [InventoryMovements] ([WarehouseId]);
2025-08-14 10:35:10.184 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_InvoiceId] ON [InvoiceItems] ([InvoiceId]);
2025-08-14 10:35:10.187 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InvoiceItems_ItemId] ON [InvoiceItems] ([ItemId]);
2025-08-14 10:35:10.190 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Invoices_CustomerId] ON [Invoices] ([CustomerId]);
2025-08-14 10:35:10.193 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Invoices_InvoiceNumber] ON [Invoices] ([InvoiceNumber]);
2025-08-14 10:35:10.196 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Invoices_SupplierId] ON [Invoices] ([SupplierId]);
2025-08-14 10:35:10.199 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_ItemAlternatives_AlternativeItemId] ON [ItemAlternatives] ([AlternativeItemId]);
2025-08-14 10:35:10.204 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_ItemAlternatives_ItemId_AlternativeItemId] ON [ItemAlternatives] ([ItemId], [AlternativeItemId]);
2025-08-14 10:35:10.208 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Items_Barcode] ON [Items] ([Barcode]) WHERE [Barcode] IS NOT NULL AND [Barcode] != '';
2025-08-14 10:35:10.210 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_CategoryId] ON [Items] ([CategoryId]);
2025-08-14 10:35:10.213 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Items_Code] ON [Items] ([Code]);
2025-08-14 10:35:10.217 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_UnitOfMeasurementId] ON [Items] ([UnitOfMeasurementId]);
2025-08-14 10:35:10.221 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Items_WarehouseId] ON [Items] ([WarehouseId]);
2025-08-14 10:35:10.223 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_CustomerId] ON [Payments] ([CustomerId]);
2025-08-14 10:35:10.226 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_InvoiceId] ON [Payments] ([InvoiceId]);
2025-08-14 10:35:10.229 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Payments_PaymentNumber] ON [Payments] ([PaymentNumber]);
2025-08-14 10:35:10.231 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Payments_SupplierId] ON [Payments] ([SupplierId]);
2025-08-14 10:35:10.234 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_StockAdjustments_AdjustmentNumber] ON [StockAdjustments] ([AdjustmentNumber]);
2025-08-14 10:35:10.237 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_StockBalances_ItemId_WarehouseId] ON [StockBalances] ([ItemId], [WarehouseId]);
2025-08-14 10:35:10.240 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockBalances_WarehouseId] ON [StockBalances] ([WarehouseId]);
2025-08-14 10:35:10.243 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_Code] ON [Suppliers] ([Code]);
2025-08-14 10:35:10.246 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Suppliers_Email] ON [Suppliers] ([Email]) WHERE [Email] IS NOT NULL AND [Email] != '';
2025-08-14 10:35:10.249 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transfers_FromWarehouseId] ON [Transfers] ([FromWarehouseId]);
2025-08-14 10:35:10.252 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Transfers_ToWarehouseId] ON [Transfers] ([ToWarehouseId]);
2025-08-14 10:35:10.255 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Transfers_TransferNumber] ON [Transfers] ([TransferNumber]);
2025-08-14 10:35:10.258 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Warehouses_Code] ON [Warehouses] ([Code]);
2025-08-14 10:35:10.266 +03:00 [INF] Database ensured created successfully
2025-08-14 10:35:10.272 +03:00 [INF] Starting Warehouse Management API
2025-08-14 10:35:10.303 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 10:35:10.306 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 10:35:10.307 +03:00 [INF] Hosting environment: Production
2025-08-14 10:35:10.308 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:05:42.756 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:05:45.032 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:05:45.124 +03:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:05:45.129 +03:00 [INF] Database ensured created successfully
2025-08-14 12:05:45.135 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:05:45.150 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:05:45.265 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:05:45.269 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:05:45.270 +03:00 [INF] Hosting environment: Production
2025-08-14 12:05:45.271 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:44:12.903 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:44:14.453 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:44:14.531 +03:00 [INF] Executed DbCommand (65ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:44:14.536 +03:00 [INF] Database ensured created successfully
2025-08-14 12:44:14.541 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:44:14.554 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:44:14.600 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:44:14.603 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:44:14.604 +03:00 [INF] Hosting environment: Production
2025-08-14 12:44:14.605 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:45:03.775 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 12:45:03.797 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 12:45:05.191 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 1417.7306ms
2025-08-14 12:45:05.200 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 12:57:26.944 +03:00 [INF] Application is shutting down...
2025-08-14 12:58:17.282 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:58:18.969 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:58:19.045 +03:00 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:58:19.049 +03:00 [INF] Database ensured created successfully
2025-08-14 12:58:19.054 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:58:19.069 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:58:19.120 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:58:19.123 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:58:19.125 +03:00 [INF] Hosting environment: Production
2025-08-14 12:58:19.126 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 12:58:25.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 12:58:25.669 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 12:58:25.698 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 55.4814ms
2025-08-14 12:58:25.709 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 12:58:37.343 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 12:58:37.348 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 5.5233ms
2025-08-14 12:58:37.355 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 12:58:49.091 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 12:58:49.098 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 6.368ms
2025-08-14 12:58:49.102 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 12:59:00.782 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 12:59:00.786 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 4.024ms
2025-08-14 12:59:00.789 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 12:59:32.787 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 12:59:33.341 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 12:59:33.387 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 12:59:33.391 +03:00 [INF] Database ensured created successfully
2025-08-14 12:59:33.396 +03:00 [INF] Starting Warehouse Management API
2025-08-14 12:59:33.409 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 12:59:33.458 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 12:59:33.461 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 12:59:33.462 +03:00 [INF] Hosting environment: Production
2025-08-14 12:59:33.463 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 13:00:26.636 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:00:26.658 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 13:00:26.689 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 52.9788ms
2025-08-14 13:00:26.697 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:01:02.982 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:01:02.988 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 6.3953ms
2025-08-14 13:01:02.994 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:01:38.197 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 13:01:38.203 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 6.4041ms
2025-08-14 13:01:38.209 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 13:05:52.806 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 13:05:53.322 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 13:05:53.367 +03:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 13:05:53.371 +03:00 [INF] Database ensured created successfully
2025-08-14 13:05:53.376 +03:00 [INF] Starting Warehouse Management API
2025-08-14 13:05:53.387 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 13:05:53.433 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 13:05:53.437 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 13:05:53.438 +03:00 [INF] Hosting environment: Production
2025-08-14 13:05:53.440 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 13:05:59.098 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:05:59.120 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 13:05:59.150 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 53.0483ms
2025-08-14 13:05:59.157 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:06:05.977 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-08-14 13:06:05.983 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 6.0545ms
2025-08-14 13:06:05.989 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-08-14 13:06:11.815 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 13:06:11.821 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 6.6391ms
2025-08-14 13:06:11.826 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 13:06:19.206 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-08-14 13:06:19.210 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 404 0 null 3.7622ms
2025-08-14 13:06:19.214 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index.html, Response status code: 404
2025-08-14 13:06:54.431 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 13:06:54.435 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 4.0549ms
2025-08-14 13:06:54.439 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 13:07:00.255 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api - null null
2025-08-14 13:07:00.260 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api - 404 0 null 4.8266ms
2025-08-14 13:07:00.264 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/api, Response status code: 404
2025-08-14 13:08:33.733 +03:00 [INF] Application is shutting down...
2025-08-14 13:12:00.547 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 13:12:01.175 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 13:12:01.218 +03:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 13:12:01.222 +03:00 [INF] Database ensured created successfully
2025-08-14 13:12:01.227 +03:00 [INF] Starting Warehouse Management API
2025-08-14 13:12:01.241 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 13:12:01.292 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 13:12:01.294 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 13:12:01.296 +03:00 [INF] Hosting environment: Production
2025-08-14 13:12:01.297 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:19:38.515 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:19:40.133 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:19:40.212 +03:00 [INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 14:19:40.216 +03:00 [INF] Database ensured created successfully
2025-08-14 14:19:40.223 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:19:40.240 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:19:40.307 +03:00 [INF] Now listening on: http://localhost:5000
2025-08-14 14:19:40.310 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:19:40.312 +03:00 [INF] Hosting environment: Production
2025-08-14 14:19:40.313 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:19:51.136 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 14:19:51.158 +03:00 [WRN] Failed to determine the https port for redirect.
2025-08-14 14:19:51.189 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 54.0701ms
2025-08-14 14:19:51.194 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-08-14 14:19:51.198 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 14:19:51.204 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 404 0 null 10.3199ms
2025-08-14 14:19:51.215 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/, Response status code: 404
2025-08-14 14:20:11.553 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index - null null
2025-08-14 14:20:11.561 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index - 404 0 null 7.4703ms
2025-08-14 14:20:11.565 +03:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger/index, Response status code: 404
2025-08-14 14:21:02.946 +03:00 [INF] Application is shutting down...
2025-08-14 14:22:37.401 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:22:38.339 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:22:38.387 +03:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-08-14 14:22:38.390 +03:00 [INF] Database ensured created successfully
2025-08-14 14:22:38.398 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:22:38.456 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:22:38.867 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:22:38.869 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:22:38.971 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:22:38.974 +03:00 [INF] Hosting environment: Development
2025-08-14 14:22:38.975 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:22:39.748 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/ - null null
2025-08-14 14:22:39.979 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/ - 301 0 null 236.9608ms
2025-08-14 14:22:39.990 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 14:22:40.076 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 85.4402ms
2025-08-14 14:22:40.100 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui.css - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui-standalone-preset.js - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - null null
2025-08-14 14:22:40.101 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger-ui-bundle.js - null null
2025-08-14 14:22:40.110 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/_vs/browserLink - null null
2025-08-14 14:22:40.136 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_framework/aspnetcore-browser-refresh.js - 200 16531 application/javascript; charset=utf-8 34.9122ms
2025-08-14 14:22:40.222 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-08-14 14:22:40.222 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-08-14 14:22:40.225 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui.css - 200 144929 text/css 125.0301ms
2025-08-14 14:22:40.227 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/_vs/browserLink - 200 null text/javascript; charset=UTF-8 118.0195ms
2025-08-14 14:22:40.227 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui-standalone-preset.js - 200 312163 text/javascript 126.5256ms
2025-08-14 14:22:40.335 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-08-14 14:22:40.338 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger-ui-bundle.js - 200 1061536 text/javascript 236.8783ms
2025-08-14 14:22:40.644 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 14:22:40.674 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/favicon-32x32.png - null null
2025-08-14 14:22:40.678 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-08-14 14:22:40.680 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/favicon-32x32.png - 200 628 image/png 7.3533ms
2025-08-14 14:22:40.687 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 42.924ms
2025-08-14 14:24:48.392 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/api/auth/validate - null null
2025-08-14 14:24:48.414 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:24:48.584 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:24:48.605 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:24:48.621 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/api/auth/validate - 401 0 null 229.7458ms
2025-08-14 14:25:18.634 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/api/auth/validate - null null
2025-08-14 14:25:18.648 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:25:18.655 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:25:18.659 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:25:18.660 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/api/auth/validate - 401 0 null 25.89ms
2025-08-14 14:39:47.662 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:39:48.357 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:40:44.075 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:40:44.599 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:40:44.624 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:40:56.529 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:40:57.047 +03:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:40:57.075 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:40:57.080 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:40:57.220 +03:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [__EFMigrationsHistory] (
    [MigrationId] nvarchar(150) NOT NULL,
    [ProductVersion] nvarchar(32) NOT NULL,
    CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
);
2025-08-14 14:40:57.224 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:40:57.227 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:40:57.234 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:40:57.248 +03:00 [INF] Applying migration '20250814113951_AddAuthenticationTables'.
2025-08-14 14:40:57.445 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 14:40:57.450 +03:00 [ERR] An error occurred while setting up the database
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'CashRegisters' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 178
ClientConnectionId:ad7c2cd2-20ca-4325-8456-97119029dce3
Error Number:2714,State:6,Class:16
2025-08-14 14:40:57.493 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:40:57.504 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:40:57.596 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:40:57.598 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:40:57.600 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:40:57.601 +03:00 [INF] Hosting environment: Development
2025-08-14 14:40:57.602 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:41:47.581 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:41:48.116 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:41:48.142 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:41:48.148 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:41:48.151 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:41:48.169 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:41:48.186 +03:00 [INF] Applying migration '20250814113951_AddAuthenticationTables'.
2025-08-14 14:41:48.379 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 14:42:03.737 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:42:04.333 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:42:05.374 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:42:05.402 +03:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:42:05.419 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:42:31.911 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:42:32.506 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:42:48.863 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:42:49.348 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:42:49.373 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:42:49.379 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:42:49.382 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:42:49.399 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:42:49.415 +03:00 [INF] Applying migration '20250814114233_AddAuthenticationTablesOnly'.
2025-08-14 14:42:49.606 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 14:43:25.204 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:43:25.721 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:43:25.748 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:43:25.753 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:43:25.756 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:43:25.771 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:43:25.791 +03:00 [INF] Applying migration '20250814114233_AddAuthenticationTablesOnly'.
2025-08-14 14:43:25.977 +03:00 [ERR] Failed executing DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [CashRegisters] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Code] nvarchar(20) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [OpeningBalance] decimal(18,4) NOT NULL,
    [CurrentBalance] decimal(18,4) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    [CreatedBy] nvarchar(max) NULL,
    [UpdatedBy] nvarchar(max) NULL,
    [IsDeleted] bit NOT NULL,
    CONSTRAINT [PK_CashRegisters] PRIMARY KEY ([Id])
);
2025-08-14 14:43:25.982 +03:00 [ERR] An error occurred while setting up the database
Microsoft.Data.SqlClient.SqlException (0x80131904): There is already an object named 'CashRegisters' in the database.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.RunExecuteNonQueryTds(String methodName, Boolean isAsync, Int32 timeout, Boolean asyncWrite)
   at Microsoft.Data.SqlClient.SqlCommand.InternalExecuteNonQuery(TaskCompletionSource`1 completion, Boolean sendToPipe, Int32 timeout, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry, String methodName)
   at Microsoft.Data.SqlClient.SqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 178
ClientConnectionId:3d06ca7c-70cb-4503-8631-c6603fb5e416
Error Number:2714,State:6,Class:16
2025-08-14 14:43:25.999 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:43:26.009 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:43:26.097 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:43:26.099 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:43:26.102 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:43:26.103 +03:00 [INF] Hosting environment: Development
2025-08-14 14:43:26.104 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:44:19.541 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:44:20.235 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:44:21.176 +03:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:44:21.203 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:44:21.215 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:46:32.719 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:46:33.563 +03:00 [INF] Executed DbCommand (74ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:46:33.593 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:46:33.598 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:46:33.601 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:46:33.616 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:46:33.634 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 14:46:33.636 +03:00 [INF] Database migrations applied successfully
2025-08-14 14:46:33.640 +03:00 [INF] Starting authentication data seeding...
2025-08-14 14:46:34.151 +03:00 [ERR] Failed executing DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:46:34.204 +03:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WarehouseManagement.Infrastructure.Data.WarehouseDbContext'.
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'Permissions'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:781c7d9e-7211-4688-a099-7f39d726e386
Error Number:208,State:1,Class:16
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'Permissions'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
ClientConnectionId:781c7d9e-7211-4688-a099-7f39d726e386
Error Number:208,State:1,Class:16
2025-08-14 14:46:34.213 +03:00 [ERR] Error occurred while seeding authentication data
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'Permissions'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedPermissionsAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 44
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 18
ClientConnectionId:781c7d9e-7211-4688-a099-7f39d726e386
Error Number:208,State:1,Class:16
2025-08-14 14:46:34.231 +03:00 [ERR] An error occurred while setting up the database
Microsoft.Data.SqlClient.SqlException (0x80131904): Invalid object name 'Permissions'.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__209_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedPermissionsAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 44
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 18
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 182
ClientConnectionId:781c7d9e-7211-4688-a099-7f39d726e386
Error Number:208,State:1,Class:16
2025-08-14 14:46:34.247 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:46:34.262 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:46:34.381 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:46:34.383 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:46:34.384 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:46:34.386 +03:00 [INF] Hosting environment: Development
2025-08-14 14:46:34.387 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:48:47.499 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:48:47.991 +03:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:48:48.016 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:48:48.021 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:48:48.024 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:48:48.040 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:48:48.057 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 14:48:48.059 +03:00 [INF] Database migrations applied successfully
2025-08-14 14:48:48.064 +03:00 [INF] Starting authentication data seeding...
2025-08-14 14:48:48.322 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:48:48.493 +03:00 [INF] Seeded 34 permissions
2025-08-14 14:48:48.500 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:48:48.527 +03:00 [INF] Seeded 4 roles
2025-08-14 14:48:48.536 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:48:48.597 +03:00 [INF] Seeded admin user with username: admin, password: Admin@123
2025-08-14 14:48:48.605 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:48:48.647 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'admin'
2025-08-14 14:48:48.669 +03:00 [ERR] Error occurred while seeding authentication data
System.InvalidOperationException: Sequence contains no elements.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedRolePermissionsAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 193
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 27
2025-08-14 14:48:48.680 +03:00 [ERR] An error occurred while setting up the database
System.InvalidOperationException: Sequence contains no elements.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedRolePermissionsAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 193
   at WarehouseManagement.Infrastructure.Data.Seeders.AuthDataSeeder.SeedAsync(WarehouseDbContext context, ILogger logger) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.Infrastructure\Data\Seeders\AuthDataSeeder.cs:line 27
   at Program.<Main>$(String[] args) in G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API\Program.cs:line 182
2025-08-14 14:48:48.686 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:48:48.698 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:48:48.795 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:48:48.797 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:48:48.798 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:48:48.799 +03:00 [INF] Hosting environment: Development
2025-08-14 14:48:48.800 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:49:18.814 +03:00 [INF] Application is shutting down...
2025-08-14 14:50:05.616 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:50:06.129 +03:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:50:06.158 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:50:06.162 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:50:06.166 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:50:06.180 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:50:06.197 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 14:50:06.199 +03:00 [INF] Database migrations applied successfully
2025-08-14 14:50:06.205 +03:00 [INF] Starting authentication data seeding...
2025-08-14 14:50:06.451 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:50:06.553 +03:00 [INF] Seeded 34 permissions
2025-08-14 14:50:06.559 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:50:06.582 +03:00 [INF] Seeded 4 roles
2025-08-14 14:50:06.838 +03:00 [INF] Executed DbCommand (128ms) [Parameters=[@p0='?' (Size = 20), @p1='?' (DbType = DateTime2), @p2='?' (Size = 4000), @p3='?' (Size = 500), @p4='?' (DbType = Boolean), @p5='?' (Size = 50), @p6='?' (Size = 50), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (Size = 20), @p10='?' (DbType = DateTime2), @p11='?' (Size = 4000), @p12='?' (Size = 500), @p13='?' (DbType = Boolean), @p14='?' (Size = 50), @p15='?' (Size = 50), @p16='?' (DbType = DateTime2), @p17='?' (Size = 4000), @p18='?' (Size = 20), @p19='?' (DbType = DateTime2), @p20='?' (Size = 4000), @p21='?' (Size = 500), @p22='?' (DbType = Boolean), @p23='?' (Size = 50), @p24='?' (Size = 50), @p25='?' (DbType = DateTime2), @p26='?' (Size = 4000), @p27='?' (Size = 20), @p28='?' (DbType = DateTime2), @p29='?' (Size = 4000), @p30='?' (Size = 500), @p31='?' (DbType = Boolean), @p32='?' (Size = 50), @p33='?' (Size = 50), @p34='?' (DbType = DateTime2), @p35='?' (Size = 4000), @p36='?' (Size = 20), @p37='?' (DbType = DateTime2), @p38='?' (Size = 4000), @p39='?' (Size = 500), @p40='?' (DbType = Boolean), @p41='?' (Size = 50), @p42='?' (Size = 50), @p43='?' (DbType = DateTime2), @p44='?' (Size = 4000), @p45='?' (Size = 20), @p46='?' (DbType = DateTime2), @p47='?' (Size = 4000), @p48='?' (Size = 500), @p49='?' (DbType = Boolean), @p50='?' (Size = 50), @p51='?' (Size = 50), @p52='?' (DbType = DateTime2), @p53='?' (Size = 4000), @p54='?' (Size = 20), @p55='?' (DbType = DateTime2), @p56='?' (Size = 4000), @p57='?' (Size = 500), @p58='?' (DbType = Boolean), @p59='?' (Size = 50), @p60='?' (Size = 50), @p61='?' (DbType = DateTime2), @p62='?' (Size = 4000), @p63='?' (Size = 20), @p64='?' (DbType = DateTime2), @p65='?' (Size = 4000), @p66='?' (Size = 500), @p67='?' (DbType = Boolean), @p68='?' (Size = 50), @p69='?' (Size = 50), @p70='?' (DbType = DateTime2), @p71='?' (Size = 4000), @p72='?' (Size = 20), @p73='?' (DbType = DateTime2), @p74='?' (Size = 4000), @p75='?' (Size = 500), @p76='?' (DbType = Boolean), @p77='?' (Size = 50), @p78='?' (Size = 50), @p79='?' (DbType = DateTime2), @p80='?' (Size = 4000), @p81='?' (Size = 20), @p82='?' (DbType = DateTime2), @p83='?' (Size = 4000), @p84='?' (Size = 500), @p85='?' (DbType = Boolean), @p86='?' (Size = 50), @p87='?' (Size = 50), @p88='?' (DbType = DateTime2), @p89='?' (Size = 4000), @p90='?' (Size = 20), @p91='?' (DbType = DateTime2), @p92='?' (Size = 4000), @p93='?' (Size = 500), @p94='?' (DbType = Boolean), @p95='?' (Size = 50), @p96='?' (Size = 50), @p97='?' (DbType = DateTime2), @p98='?' (Size = 4000), @p99='?' (Size = 20), @p100='?' (DbType = DateTime2), @p101='?' (Size = 4000), @p102='?' (Size = 500), @p103='?' (DbType = Boolean), @p104='?' (Size = 50), @p105='?' (Size = 50), @p106='?' (DbType = DateTime2), @p107='?' (Size = 4000), @p108='?' (Size = 20), @p109='?' (DbType = DateTime2), @p110='?' (Size = 4000), @p111='?' (Size = 500), @p112='?' (DbType = Boolean), @p113='?' (Size = 50), @p114='?' (Size = 50), @p115='?' (DbType = DateTime2), @p116='?' (Size = 4000), @p117='?' (Size = 20), @p118='?' (DbType = DateTime2), @p119='?' (Size = 4000), @p120='?' (Size = 500), @p121='?' (DbType = Boolean), @p122='?' (Size = 50), @p123='?' (Size = 50), @p124='?' (DbType = DateTime2), @p125='?' (Size = 4000), @p126='?' (Size = 20), @p127='?' (DbType = DateTime2), @p128='?' (Size = 4000), @p129='?' (Size = 500), @p130='?' (DbType = Boolean), @p131='?' (Size = 50), @p132='?' (Size = 50), @p133='?' (DbType = DateTime2), @p134='?' (Size = 4000), @p135='?' (Size = 20), @p136='?' (DbType = DateTime2), @p137='?' (Size = 4000), @p138='?' (Size = 500), @p139='?' (DbType = Boolean), @p140='?' (Size = 50), @p141='?' (Size = 50), @p142='?' (DbType = DateTime2), @p143='?' (Size = 4000), @p144='?' (Size = 20), @p145='?' (DbType = DateTime2), @p146='?' (Size = 4000), @p147='?' (Size = 500), @p148='?' (DbType = Boolean), @p149='?' (Size = 50), @p150='?' (Size = 50), @p151='?' (DbType = DateTime2), @p152='?' (Size = 4000), @p153='?' (Size = 20), @p154='?' (DbType = DateTime2), @p155='?' (Size = 4000), @p156='?' (Size = 500), @p157='?' (DbType = Boolean), @p158='?' (Size = 50), @p159='?' (Size = 50), @p160='?' (DbType = DateTime2), @p161='?' (Size = 4000), @p162='?' (Size = 20), @p163='?' (DbType = DateTime2), @p164='?' (Size = 4000), @p165='?' (Size = 500), @p166='?' (DbType = Boolean), @p167='?' (Size = 50), @p168='?' (Size = 50), @p169='?' (DbType = DateTime2), @p170='?' (Size = 4000), @p171='?' (Size = 20), @p172='?' (DbType = DateTime2), @p173='?' (Size = 4000), @p174='?' (Size = 500), @p175='?' (DbType = Boolean), @p176='?' (Size = 50), @p177='?' (Size = 50), @p178='?' (DbType = DateTime2), @p179='?' (Size = 4000), @p180='?' (Size = 20), @p181='?' (DbType = DateTime2), @p182='?' (Size = 4000), @p183='?' (Size = 500), @p184='?' (DbType = Boolean), @p185='?' (Size = 50), @p186='?' (Size = 50), @p187='?' (DbType = DateTime2), @p188='?' (Size = 4000), @p189='?' (Size = 20), @p190='?' (DbType = DateTime2), @p191='?' (Size = 4000), @p192='?' (Size = 500), @p193='?' (DbType = Boolean), @p194='?' (Size = 50), @p195='?' (Size = 50), @p196='?' (DbType = DateTime2), @p197='?' (Size = 4000), @p198='?' (Size = 20), @p199='?' (DbType = DateTime2), @p200='?' (Size = 4000), @p201='?' (Size = 500), @p202='?' (DbType = Boolean), @p203='?' (Size = 50), @p204='?' (Size = 50), @p205='?' (DbType = DateTime2), @p206='?' (Size = 4000), @p207='?' (Size = 20), @p208='?' (DbType = DateTime2), @p209='?' (Size = 4000), @p210='?' (Size = 500), @p211='?' (DbType = Boolean), @p212='?' (Size = 50), @p213='?' (Size = 50), @p214='?' (DbType = DateTime2), @p215='?' (Size = 4000), @p216='?' (Size = 20), @p217='?' (DbType = DateTime2), @p218='?' (Size = 4000), @p219='?' (Size = 500), @p220='?' (DbType = Boolean), @p221='?' (Size = 50), @p222='?' (Size = 50), @p223='?' (DbType = DateTime2), @p224='?' (Size = 4000), @p225='?' (Size = 20), @p226='?' (DbType = DateTime2), @p227='?' (Size = 4000), @p228='?' (Size = 500), @p229='?' (DbType = Boolean), @p230='?' (Size = 50), @p231='?' (Size = 50), @p232='?' (DbType = DateTime2), @p233='?' (Size = 4000), @p234='?' (Size = 20), @p235='?' (DbType = DateTime2), @p236='?' (Size = 4000), @p237='?' (Size = 500), @p238='?' (DbType = Boolean), @p239='?' (Size = 50), @p240='?' (Size = 50), @p241='?' (DbType = DateTime2), @p242='?' (Size = 4000), @p243='?' (Size = 20), @p244='?' (DbType = DateTime2), @p245='?' (Size = 4000), @p246='?' (Size = 500), @p247='?' (DbType = Boolean), @p248='?' (Size = 50), @p249='?' (Size = 50), @p250='?' (DbType = DateTime2), @p251='?' (Size = 4000), @p252='?' (Size = 20), @p253='?' (DbType = DateTime2), @p254='?' (Size = 4000), @p255='?' (Size = 500), @p256='?' (DbType = Boolean), @p257='?' (Size = 50), @p258='?' (Size = 50), @p259='?' (DbType = DateTime2), @p260='?' (Size = 4000), @p261='?' (Size = 20), @p262='?' (DbType = DateTime2), @p263='?' (Size = 4000), @p264='?' (Size = 500), @p265='?' (DbType = Boolean), @p266='?' (Size = 50), @p267='?' (Size = 50), @p268='?' (DbType = DateTime2), @p269='?' (Size = 4000), @p270='?' (Size = 20), @p271='?' (DbType = DateTime2), @p272='?' (Size = 4000), @p273='?' (Size = 500), @p274='?' (DbType = Boolean), @p275='?' (Size = 50), @p276='?' (Size = 50), @p277='?' (DbType = DateTime2), @p278='?' (Size = 4000), @p279='?' (Size = 20), @p280='?' (DbType = DateTime2), @p281='?' (Size = 4000), @p282='?' (Size = 500), @p283='?' (DbType = Boolean), @p284='?' (Size = 50), @p285='?' (Size = 50), @p286='?' (DbType = DateTime2), @p287='?' (Size = 4000), @p288='?' (Size = 20), @p289='?' (DbType = DateTime2), @p290='?' (Size = 4000), @p291='?' (Size = 500), @p292='?' (DbType = Boolean), @p293='?' (Size = 50), @p294='?' (Size = 50), @p295='?' (DbType = DateTime2), @p296='?' (Size = 4000), @p297='?' (Size = 20), @p298='?' (DbType = DateTime2), @p299='?' (Size = 4000), @p300='?' (Size = 500), @p301='?' (DbType = Boolean), @p302='?' (Size = 50), @p303='?' (Size = 50), @p304='?' (DbType = DateTime2), @p305='?' (Size = 4000), @p306='?' (DbType = DateTime2), @p307='?' (Size = 4000), @p308='?' (Size = 500), @p309='?' (Size = 100), @p310='?' (DbType = Boolean), @p311='?' (DbType = Boolean), @p312='?' (Size = 50), @p313='?' (DbType = DateTime2), @p314='?' (Size = 4000), @p315='?' (DbType = DateTime2), @p316='?' (Size = 4000), @p317='?' (Size = 500), @p318='?' (Size = 100), @p319='?' (DbType = Boolean), @p320='?' (DbType = Boolean), @p321='?' (Size = 50), @p322='?' (DbType = DateTime2), @p323='?' (Size = 4000), @p324='?' (DbType = DateTime2), @p325='?' (Size = 4000), @p326='?' (Size = 500), @p327='?' (Size = 100), @p328='?' (DbType = Boolean), @p329='?' (DbType = Boolean), @p330='?' (Size = 50), @p331='?' (DbType = DateTime2), @p332='?' (Size = 4000), @p333='?' (DbType = DateTime2), @p334='?' (Size = 4000), @p335='?' (Size = 500), @p336='?' (Size = 100), @p337='?' (DbType = Boolean), @p338='?' (DbType = Boolean), @p339='?' (Size = 50), @p340='?' (DbType = DateTime2), @p341='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [Permissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1),
(@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, 2),
(@p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, 3),
(@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, 4),
(@p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, 5),
(@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, 6),
(@p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 7),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, 8),
(@p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, 9),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, 10),
(@p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, 11),
(@p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, 12),
(@p117, @p118, @p119, @p120, @p121, @p122, @p123, @p124, @p125, 13),
(@p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, 14),
(@p135, @p136, @p137, @p138, @p139, @p140, @p141, @p142, @p143, 15),
(@p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152, 16),
(@p153, @p154, @p155, @p156, @p157, @p158, @p159, @p160, @p161, 17),
(@p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169, @p170, 18),
(@p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179, 19),
(@p180, @p181, @p182, @p183, @p184, @p185, @p186, @p187, @p188, 20),
(@p189, @p190, @p191, @p192, @p193, @p194, @p195, @p196, @p197, 21),
(@p198, @p199, @p200, @p201, @p202, @p203, @p204, @p205, @p206, 22),
(@p207, @p208, @p209, @p210, @p211, @p212, @p213, @p214, @p215, 23),
(@p216, @p217, @p218, @p219, @p220, @p221, @p222, @p223, @p224, 24),
(@p225, @p226, @p227, @p228, @p229, @p230, @p231, @p232, @p233, 25),
(@p234, @p235, @p236, @p237, @p238, @p239, @p240, @p241, @p242, 26),
(@p243, @p244, @p245, @p246, @p247, @p248, @p249, @p250, @p251, 27),
(@p252, @p253, @p254, @p255, @p256, @p257, @p258, @p259, @p260, 28),
(@p261, @p262, @p263, @p264, @p265, @p266, @p267, @p268, @p269, 29),
(@p270, @p271, @p272, @p273, @p274, @p275, @p276, @p277, @p278, 30),
(@p279, @p280, @p281, @p282, @p283, @p284, @p285, @p286, @p287, 31),
(@p288, @p289, @p290, @p291, @p292, @p293, @p294, @p295, @p296, 32),
(@p297, @p298, @p299, @p300, @p301, @p302, @p303, @p304, @p305, 33)) AS i ([Action], [CreatedAt], [CreatedBy], [Description], [IsDeleted], [Name], [Resource], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([Action], [CreatedAt], [CreatedBy], [Description], [IsDeleted], [Name], [Resource], [UpdatedAt], [UpdatedBy])
VALUES (i.[Action], i.[CreatedAt], i.[CreatedBy], i.[Description], i.[IsDeleted], i.[Name], i.[Resource], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
MERGE [Roles] USING (
VALUES (@p306, @p307, @p308, @p309, @p310, @p311, @p312, @p313, @p314, 0),
(@p315, @p316, @p317, @p318, @p319, @p320, @p321, @p322, @p323, 1),
(@p324, @p325, @p326, @p327, @p328, @p329, @p330, @p331, @p332, 2),
(@p333, @p334, @p335, @p336, @p337, @p338, @p339, @p340, @p341, 3)) AS i ([CreatedAt], [CreatedBy], [Description], [DisplayName], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [Description], [DisplayName], [IsActive], [IsDeleted], [Name], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[Description], i.[DisplayName], i.[IsActive], i.[IsDeleted], i.[Name], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-14 14:50:06.886 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:50:06.930 +03:00 [INF] Seeded admin user with username: admin, password: Admin@123
2025-08-14 14:50:06.952 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (Size = 100), @p3='?' (DbType = Int32), @p4='?' (Size = 100), @p5='?' (DbType = Boolean), @p6='?' (DbType = Boolean), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTime2), @p9='?' (Size = 100), @p10='?' (DbType = DateTime2), @p11='?' (DbType = DateTime2), @p12='?' (Size = 4000), @p13='?' (Size = 500), @p14='?' (DbType = DateTime2), @p15='?' (Size = 4000), @p16='?' (DbType = DateTime2), @p17='?' (Size = 4000), @p18='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([CreatedAt], [CreatedBy], [Email], [FailedLoginAttempts], [FirstName], [IsActive], [IsDeleted], [IsEmailConfirmed], [LastLoginAt], [LastName], [LockedOutUntil], [PasswordChangedAt], [PasswordHash], [RefreshToken], [RefreshTokenExpiryTime], [Salt], [UpdatedAt], [UpdatedBy], [Username])
OUTPUT INSERTED.[Id]
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18);
2025-08-14 14:50:06.963 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:50:06.995 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'admin'
2025-08-14 14:50:07.011 +03:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'manager'
2025-08-14 14:50:07.019 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'employee'
2025-08-14 14:50:07.026 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'viewer'
2025-08-14 14:50:07.038 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [p].[Id], [p].[Action], [p].[CreatedAt], [p].[CreatedBy], [p].[Description], [p].[IsDeleted], [p].[Name], [p].[Resource], [p].[UpdatedAt], [p].[UpdatedBy]
FROM [Permissions] AS [p]
WHERE [p].[IsDeleted] = CAST(0 AS bit)
2025-08-14 14:50:07.071 +03:00 [INF] Seeded 78 role permissions
2025-08-14 14:50:07.080 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserRoles] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:50:07.095 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin'
2025-08-14 14:50:07.104 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[CreatedAt], [r].[CreatedBy], [r].[Description], [r].[DisplayName], [r].[IsActive], [r].[IsDeleted], [r].[Name], [r].[UpdatedAt], [r].[UpdatedBy]
FROM [Roles] AS [r]
WHERE [r].[IsDeleted] = CAST(0 AS bit) AND [r].[Name] = N'admin'
2025-08-14 14:50:07.133 +03:00 [INF] Assigned admin role to admin user
2025-08-14 14:50:07.184 +03:00 [INF] Executed DbCommand (28ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (DbType = DateTime2), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32), @p13='?' (DbType = Boolean), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (Size = 4000), @p18='?' (DbType = DateTime2), @p19='?' (Size = 4000), @p20='?' (DbType = DateTime2), @p21='?' (DbType = Int32), @p22='?' (DbType = Boolean), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = DateTime2), @p26='?' (Size = 4000), @p27='?' (DbType = DateTime2), @p28='?' (Size = 4000), @p29='?' (DbType = DateTime2), @p30='?' (DbType = Int32), @p31='?' (DbType = Boolean), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (DbType = DateTime2), @p35='?' (Size = 4000), @p36='?' (DbType = DateTime2), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (DbType = Boolean), @p41='?' (DbType = Int32), @p42='?' (DbType = Int32), @p43='?' (DbType = DateTime2), @p44='?' (Size = 4000), @p45='?' (DbType = DateTime2), @p46='?' (Size = 4000), @p47='?' (DbType = DateTime2), @p48='?' (DbType = Int32), @p49='?' (DbType = Boolean), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (DbType = DateTime2), @p53='?' (Size = 4000), @p54='?' (DbType = DateTime2), @p55='?' (Size = 4000), @p56='?' (DbType = DateTime2), @p57='?' (DbType = Int32), @p58='?' (DbType = Boolean), @p59='?' (DbType = Int32), @p60='?' (DbType = Int32), @p61='?' (DbType = DateTime2), @p62='?' (Size = 4000), @p63='?' (DbType = DateTime2), @p64='?' (Size = 4000), @p65='?' (DbType = DateTime2), @p66='?' (DbType = Int32), @p67='?' (DbType = Boolean), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p70='?' (DbType = DateTime2), @p71='?' (Size = 4000), @p72='?' (DbType = DateTime2), @p73='?' (Size = 4000), @p74='?' (DbType = DateTime2), @p75='?' (DbType = Int32), @p76='?' (DbType = Boolean), @p77='?' (DbType = Int32), @p78='?' (DbType = Int32), @p79='?' (DbType = DateTime2), @p80='?' (Size = 4000), @p81='?' (DbType = DateTime2), @p82='?' (Size = 4000), @p83='?' (DbType = DateTime2), @p84='?' (DbType = Int32), @p85='?' (DbType = Boolean), @p86='?' (DbType = Int32), @p87='?' (DbType = Int32), @p88='?' (DbType = DateTime2), @p89='?' (Size = 4000), @p90='?' (DbType = DateTime2), @p91='?' (Size = 4000), @p92='?' (DbType = DateTime2), @p93='?' (DbType = Int32), @p94='?' (DbType = Boolean), @p95='?' (DbType = Int32), @p96='?' (DbType = Int32), @p97='?' (DbType = DateTime2), @p98='?' (Size = 4000), @p99='?' (DbType = DateTime2), @p100='?' (Size = 4000), @p101='?' (DbType = DateTime2), @p102='?' (DbType = Int32), @p103='?' (DbType = Boolean), @p104='?' (DbType = Int32), @p105='?' (DbType = Int32), @p106='?' (DbType = DateTime2), @p107='?' (Size = 4000), @p108='?' (DbType = DateTime2), @p109='?' (Size = 4000), @p110='?' (DbType = DateTime2), @p111='?' (DbType = Int32), @p112='?' (DbType = Boolean), @p113='?' (DbType = Int32), @p114='?' (DbType = Int32), @p115='?' (DbType = DateTime2), @p116='?' (Size = 4000), @p117='?' (DbType = DateTime2), @p118='?' (Size = 4000), @p119='?' (DbType = DateTime2), @p120='?' (DbType = Int32), @p121='?' (DbType = Boolean), @p122='?' (DbType = Int32), @p123='?' (DbType = Int32), @p124='?' (DbType = DateTime2), @p125='?' (Size = 4000), @p126='?' (DbType = DateTime2), @p127='?' (Size = 4000), @p128='?' (DbType = DateTime2), @p129='?' (DbType = Int32), @p130='?' (DbType = Boolean), @p131='?' (DbType = Int32), @p132='?' (DbType = Int32), @p133='?' (DbType = DateTime2), @p134='?' (Size = 4000), @p135='?' (DbType = DateTime2), @p136='?' (Size = 4000), @p137='?' (DbType = DateTime2), @p138='?' (DbType = Int32), @p139='?' (DbType = Boolean), @p140='?' (DbType = Int32), @p141='?' (DbType = Int32), @p142='?' (DbType = DateTime2), @p143='?' (Size = 4000), @p144='?' (DbType = DateTime2), @p145='?' (Size = 4000), @p146='?' (DbType = DateTime2), @p147='?' (DbType = Int32), @p148='?' (DbType = Boolean), @p149='?' (DbType = Int32), @p150='?' (DbType = Int32), @p151='?' (DbType = DateTime2), @p152='?' (Size = 4000), @p153='?' (DbType = DateTime2), @p154='?' (Size = 4000), @p155='?' (DbType = DateTime2), @p156='?' (DbType = Int32), @p157='?' (DbType = Boolean), @p158='?' (DbType = Int32), @p159='?' (DbType = Int32), @p160='?' (DbType = DateTime2), @p161='?' (Size = 4000), @p162='?' (DbType = DateTime2), @p163='?' (Size = 4000), @p164='?' (DbType = DateTime2), @p165='?' (DbType = Int32), @p166='?' (DbType = Boolean), @p167='?' (DbType = Int32), @p168='?' (DbType = Int32), @p169='?' (DbType = DateTime2), @p170='?' (Size = 4000), @p171='?' (DbType = DateTime2), @p172='?' (Size = 4000), @p173='?' (DbType = DateTime2), @p174='?' (DbType = Int32), @p175='?' (DbType = Boolean), @p176='?' (DbType = Int32), @p177='?' (DbType = Int32), @p178='?' (DbType = DateTime2), @p179='?' (Size = 4000), @p180='?' (DbType = DateTime2), @p181='?' (Size = 4000), @p182='?' (DbType = DateTime2), @p183='?' (DbType = Int32), @p184='?' (DbType = Boolean), @p185='?' (DbType = Int32), @p186='?' (DbType = Int32), @p187='?' (DbType = DateTime2), @p188='?' (Size = 4000), @p189='?' (DbType = DateTime2), @p190='?' (Size = 4000), @p191='?' (DbType = DateTime2), @p192='?' (DbType = Int32), @p193='?' (DbType = Boolean), @p194='?' (DbType = Int32), @p195='?' (DbType = Int32), @p196='?' (DbType = DateTime2), @p197='?' (Size = 4000), @p198='?' (DbType = DateTime2), @p199='?' (Size = 4000), @p200='?' (DbType = DateTime2), @p201='?' (DbType = Int32), @p202='?' (DbType = Boolean), @p203='?' (DbType = Int32), @p204='?' (DbType = Int32), @p205='?' (DbType = DateTime2), @p206='?' (Size = 4000), @p207='?' (DbType = DateTime2), @p208='?' (Size = 4000), @p209='?' (DbType = DateTime2), @p210='?' (DbType = Int32), @p211='?' (DbType = Boolean), @p212='?' (DbType = Int32), @p213='?' (DbType = Int32), @p214='?' (DbType = DateTime2), @p215='?' (Size = 4000), @p216='?' (DbType = DateTime2), @p217='?' (Size = 4000), @p218='?' (DbType = DateTime2), @p219='?' (DbType = Int32), @p220='?' (DbType = Boolean), @p221='?' (DbType = Int32), @p222='?' (DbType = Int32), @p223='?' (DbType = DateTime2), @p224='?' (Size = 4000), @p225='?' (DbType = DateTime2), @p226='?' (Size = 4000), @p227='?' (DbType = DateTime2), @p228='?' (DbType = Int32), @p229='?' (DbType = Boolean), @p230='?' (DbType = Int32), @p231='?' (DbType = Int32), @p232='?' (DbType = DateTime2), @p233='?' (Size = 4000), @p234='?' (DbType = DateTime2), @p235='?' (Size = 4000), @p236='?' (DbType = DateTime2), @p237='?' (DbType = Int32), @p238='?' (DbType = Boolean), @p239='?' (DbType = Int32), @p240='?' (DbType = Int32), @p241='?' (DbType = DateTime2), @p242='?' (Size = 4000), @p243='?' (DbType = DateTime2), @p244='?' (Size = 4000), @p245='?' (DbType = DateTime2), @p246='?' (DbType = Int32), @p247='?' (DbType = Boolean), @p248='?' (DbType = Int32), @p249='?' (DbType = Int32), @p250='?' (DbType = DateTime2), @p251='?' (Size = 4000), @p252='?' (DbType = DateTime2), @p253='?' (Size = 4000), @p254='?' (DbType = DateTime2), @p255='?' (DbType = Int32), @p256='?' (DbType = Boolean), @p257='?' (DbType = Int32), @p258='?' (DbType = Int32), @p259='?' (DbType = DateTime2), @p260='?' (Size = 4000), @p261='?' (DbType = DateTime2), @p262='?' (Size = 4000), @p263='?' (DbType = DateTime2), @p264='?' (DbType = Int32), @p265='?' (DbType = Boolean), @p266='?' (DbType = Int32), @p267='?' (DbType = Int32), @p268='?' (DbType = DateTime2), @p269='?' (Size = 4000), @p270='?' (DbType = DateTime2), @p271='?' (Size = 4000), @p272='?' (DbType = DateTime2), @p273='?' (DbType = Int32), @p274='?' (DbType = Boolean), @p275='?' (DbType = Int32), @p276='?' (DbType = Int32), @p277='?' (DbType = DateTime2), @p278='?' (Size = 4000), @p279='?' (DbType = DateTime2), @p280='?' (Size = 4000), @p281='?' (DbType = DateTime2), @p282='?' (DbType = Int32), @p283='?' (DbType = Boolean), @p284='?' (DbType = Int32), @p285='?' (DbType = Int32), @p286='?' (DbType = DateTime2), @p287='?' (Size = 4000), @p288='?' (DbType = DateTime2), @p289='?' (Size = 4000), @p290='?' (DbType = DateTime2), @p291='?' (DbType = Int32), @p292='?' (DbType = Boolean), @p293='?' (DbType = Int32), @p294='?' (DbType = Int32), @p295='?' (DbType = DateTime2), @p296='?' (Size = 4000), @p297='?' (DbType = DateTime2), @p298='?' (Size = 4000), @p299='?' (DbType = DateTime2), @p300='?' (DbType = Int32), @p301='?' (DbType = Boolean), @p302='?' (DbType = Int32), @p303='?' (DbType = Int32), @p304='?' (DbType = DateTime2), @p305='?' (Size = 4000), @p306='?' (DbType = DateTime2), @p307='?' (Size = 4000), @p308='?' (DbType = DateTime2), @p309='?' (DbType = Int32), @p310='?' (DbType = Boolean), @p311='?' (DbType = Int32), @p312='?' (DbType = Int32), @p313='?' (DbType = DateTime2), @p314='?' (Size = 4000), @p315='?' (DbType = DateTime2), @p316='?' (Size = 4000), @p317='?' (DbType = DateTime2), @p318='?' (DbType = Int32), @p319='?' (DbType = Boolean), @p320='?' (DbType = Int32), @p321='?' (DbType = Int32), @p322='?' (DbType = DateTime2), @p323='?' (Size = 4000), @p324='?' (DbType = DateTime2), @p325='?' (Size = 4000), @p326='?' (DbType = DateTime2), @p327='?' (DbType = Int32), @p328='?' (DbType = Boolean), @p329='?' (DbType = Int32), @p330='?' (DbType = Int32), @p331='?' (DbType = DateTime2), @p332='?' (Size = 4000), @p333='?' (DbType = DateTime2), @p334='?' (Size = 4000), @p335='?' (DbType = DateTime2), @p336='?' (DbType = Int32), @p337='?' (DbType = Boolean), @p338='?' (DbType = Int32), @p339='?' (DbType = Int32), @p340='?' (DbType = DateTime2), @p341='?' (Size = 4000), @p342='?' (DbType = DateTime2), @p343='?' (Size = 4000), @p344='?' (DbType = DateTime2), @p345='?' (DbType = Int32), @p346='?' (DbType = Boolean), @p347='?' (DbType = Int32), @p348='?' (DbType = Int32), @p349='?' (DbType = DateTime2), @p350='?' (Size = 4000), @p351='?' (DbType = DateTime2), @p352='?' (Size = 4000), @p353='?' (DbType = DateTime2), @p354='?' (DbType = Int32), @p355='?' (DbType = Boolean), @p356='?' (DbType = Int32), @p357='?' (DbType = Int32), @p358='?' (DbType = DateTime2), @p359='?' (Size = 4000), @p360='?' (DbType = DateTime2), @p361='?' (Size = 4000), @p362='?' (DbType = DateTime2), @p363='?' (DbType = Int32), @p364='?' (DbType = Boolean), @p365='?' (DbType = Int32), @p366='?' (DbType = Int32), @p367='?' (DbType = DateTime2), @p368='?' (Size = 4000), @p369='?' (DbType = DateTime2), @p370='?' (Size = 4000), @p371='?' (DbType = DateTime2), @p372='?' (DbType = Int32), @p373='?' (DbType = Boolean), @p374='?' (DbType = Int32), @p375='?' (DbType = Int32), @p376='?' (DbType = DateTime2), @p377='?' (Size = 4000)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1),
(@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, 2),
(@p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, 3),
(@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, 4),
(@p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, 5),
(@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, 6),
(@p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 7),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, 8),
(@p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, 9),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, 10),
(@p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, 11),
(@p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, 12),
(@p117, @p118, @p119, @p120, @p121, @p122, @p123, @p124, @p125, 13),
(@p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, 14),
(@p135, @p136, @p137, @p138, @p139, @p140, @p141, @p142, @p143, 15),
(@p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152, 16),
(@p153, @p154, @p155, @p156, @p157, @p158, @p159, @p160, @p161, 17),
(@p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169, @p170, 18),
(@p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179, 19),
(@p180, @p181, @p182, @p183, @p184, @p185, @p186, @p187, @p188, 20),
(@p189, @p190, @p191, @p192, @p193, @p194, @p195, @p196, @p197, 21),
(@p198, @p199, @p200, @p201, @p202, @p203, @p204, @p205, @p206, 22),
(@p207, @p208, @p209, @p210, @p211, @p212, @p213, @p214, @p215, 23),
(@p216, @p217, @p218, @p219, @p220, @p221, @p222, @p223, @p224, 24),
(@p225, @p226, @p227, @p228, @p229, @p230, @p231, @p232, @p233, 25),
(@p234, @p235, @p236, @p237, @p238, @p239, @p240, @p241, @p242, 26),
(@p243, @p244, @p245, @p246, @p247, @p248, @p249, @p250, @p251, 27),
(@p252, @p253, @p254, @p255, @p256, @p257, @p258, @p259, @p260, 28),
(@p261, @p262, @p263, @p264, @p265, @p266, @p267, @p268, @p269, 29),
(@p270, @p271, @p272, @p273, @p274, @p275, @p276, @p277, @p278, 30),
(@p279, @p280, @p281, @p282, @p283, @p284, @p285, @p286, @p287, 31),
(@p288, @p289, @p290, @p291, @p292, @p293, @p294, @p295, @p296, 32),
(@p297, @p298, @p299, @p300, @p301, @p302, @p303, @p304, @p305, 33),
(@p306, @p307, @p308, @p309, @p310, @p311, @p312, @p313, @p314, 34),
(@p315, @p316, @p317, @p318, @p319, @p320, @p321, @p322, @p323, 35),
(@p324, @p325, @p326, @p327, @p328, @p329, @p330, @p331, @p332, 36),
(@p333, @p334, @p335, @p336, @p337, @p338, @p339, @p340, @p341, 37),
(@p342, @p343, @p344, @p345, @p346, @p347, @p348, @p349, @p350, 38),
(@p351, @p352, @p353, @p354, @p355, @p356, @p357, @p358, @p359, 39),
(@p360, @p361, @p362, @p363, @p364, @p365, @p366, @p367, @p368, 40),
(@p369, @p370, @p371, @p372, @p373, @p374, @p375, @p376, @p377, 41)) AS i ([CreatedAt], [CreatedBy], [GrantedAt], [GrantedBy], [IsDeleted], [PermissionId], [RoleId], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [GrantedAt], [GrantedBy], [IsDeleted], [PermissionId], [RoleId], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[GrantedAt], i.[GrantedBy], i.[IsDeleted], i.[PermissionId], i.[RoleId], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
2025-08-14 14:50:07.215 +03:00 [INF] Executed DbCommand (19ms) [Parameters=[@p0='?' (DbType = DateTime2), @p1='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (DbType = Int32), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime2), @p8='?' (Size = 4000), @p9='?' (DbType = DateTime2), @p10='?' (Size = 4000), @p11='?' (DbType = DateTime2), @p12='?' (DbType = Int32), @p13='?' (DbType = Boolean), @p14='?' (DbType = Int32), @p15='?' (DbType = Int32), @p16='?' (DbType = DateTime2), @p17='?' (Size = 4000), @p18='?' (DbType = DateTime2), @p19='?' (Size = 4000), @p20='?' (DbType = DateTime2), @p21='?' (DbType = Int32), @p22='?' (DbType = Boolean), @p23='?' (DbType = Int32), @p24='?' (DbType = Int32), @p25='?' (DbType = DateTime2), @p26='?' (Size = 4000), @p27='?' (DbType = DateTime2), @p28='?' (Size = 4000), @p29='?' (DbType = DateTime2), @p30='?' (DbType = Int32), @p31='?' (DbType = Boolean), @p32='?' (DbType = Int32), @p33='?' (DbType = Int32), @p34='?' (DbType = DateTime2), @p35='?' (Size = 4000), @p36='?' (DbType = DateTime2), @p37='?' (Size = 4000), @p38='?' (DbType = DateTime2), @p39='?' (DbType = Int32), @p40='?' (DbType = Boolean), @p41='?' (DbType = Int32), @p42='?' (DbType = Int32), @p43='?' (DbType = DateTime2), @p44='?' (Size = 4000), @p45='?' (DbType = DateTime2), @p46='?' (Size = 4000), @p47='?' (DbType = DateTime2), @p48='?' (DbType = Int32), @p49='?' (DbType = Boolean), @p50='?' (DbType = Int32), @p51='?' (DbType = Int32), @p52='?' (DbType = DateTime2), @p53='?' (Size = 4000), @p54='?' (DbType = DateTime2), @p55='?' (Size = 4000), @p56='?' (DbType = DateTime2), @p57='?' (DbType = Int32), @p58='?' (DbType = Boolean), @p59='?' (DbType = Int32), @p60='?' (DbType = Int32), @p61='?' (DbType = DateTime2), @p62='?' (Size = 4000), @p63='?' (DbType = DateTime2), @p64='?' (Size = 4000), @p65='?' (DbType = DateTime2), @p66='?' (DbType = Int32), @p67='?' (DbType = Boolean), @p68='?' (DbType = Int32), @p69='?' (DbType = Int32), @p70='?' (DbType = DateTime2), @p71='?' (Size = 4000), @p72='?' (DbType = DateTime2), @p73='?' (Size = 4000), @p74='?' (DbType = DateTime2), @p75='?' (DbType = Int32), @p76='?' (DbType = Boolean), @p77='?' (DbType = Int32), @p78='?' (DbType = Int32), @p79='?' (DbType = DateTime2), @p80='?' (Size = 4000), @p81='?' (DbType = DateTime2), @p82='?' (Size = 4000), @p83='?' (DbType = DateTime2), @p84='?' (DbType = Int32), @p85='?' (DbType = Boolean), @p86='?' (DbType = Int32), @p87='?' (DbType = Int32), @p88='?' (DbType = DateTime2), @p89='?' (Size = 4000), @p90='?' (DbType = DateTime2), @p91='?' (Size = 4000), @p92='?' (DbType = DateTime2), @p93='?' (DbType = Int32), @p94='?' (DbType = Boolean), @p95='?' (DbType = Int32), @p96='?' (DbType = Int32), @p97='?' (DbType = DateTime2), @p98='?' (Size = 4000), @p99='?' (DbType = DateTime2), @p100='?' (Size = 4000), @p101='?' (DbType = DateTime2), @p102='?' (DbType = Int32), @p103='?' (DbType = Boolean), @p104='?' (DbType = Int32), @p105='?' (DbType = Int32), @p106='?' (DbType = DateTime2), @p107='?' (Size = 4000), @p108='?' (DbType = DateTime2), @p109='?' (Size = 4000), @p110='?' (DbType = DateTime2), @p111='?' (DbType = Int32), @p112='?' (DbType = Boolean), @p113='?' (DbType = Int32), @p114='?' (DbType = Int32), @p115='?' (DbType = DateTime2), @p116='?' (Size = 4000), @p117='?' (DbType = DateTime2), @p118='?' (Size = 4000), @p119='?' (DbType = DateTime2), @p120='?' (DbType = Int32), @p121='?' (DbType = Boolean), @p122='?' (DbType = Int32), @p123='?' (DbType = Int32), @p124='?' (DbType = DateTime2), @p125='?' (Size = 4000), @p126='?' (DbType = DateTime2), @p127='?' (Size = 4000), @p128='?' (DbType = DateTime2), @p129='?' (DbType = Int32), @p130='?' (DbType = Boolean), @p131='?' (DbType = Int32), @p132='?' (DbType = Int32), @p133='?' (DbType = DateTime2), @p134='?' (Size = 4000), @p135='?' (DbType = DateTime2), @p136='?' (Size = 4000), @p137='?' (DbType = DateTime2), @p138='?' (DbType = Int32), @p139='?' (DbType = Boolean), @p140='?' (DbType = Int32), @p141='?' (DbType = Int32), @p142='?' (DbType = DateTime2), @p143='?' (Size = 4000), @p144='?' (DbType = DateTime2), @p145='?' (Size = 4000), @p146='?' (DbType = DateTime2), @p147='?' (DbType = Int32), @p148='?' (DbType = Boolean), @p149='?' (DbType = Int32), @p150='?' (DbType = Int32), @p151='?' (DbType = DateTime2), @p152='?' (Size = 4000), @p153='?' (DbType = DateTime2), @p154='?' (Size = 4000), @p155='?' (DbType = DateTime2), @p156='?' (DbType = Int32), @p157='?' (DbType = Boolean), @p158='?' (DbType = Int32), @p159='?' (DbType = Int32), @p160='?' (DbType = DateTime2), @p161='?' (Size = 4000), @p162='?' (DbType = DateTime2), @p163='?' (Size = 4000), @p164='?' (DbType = DateTime2), @p165='?' (DbType = Int32), @p166='?' (DbType = Boolean), @p167='?' (DbType = Int32), @p168='?' (DbType = Int32), @p169='?' (DbType = DateTime2), @p170='?' (Size = 4000), @p171='?' (DbType = DateTime2), @p172='?' (Size = 4000), @p173='?' (DbType = DateTime2), @p174='?' (DbType = Int32), @p175='?' (DbType = Boolean), @p176='?' (DbType = Int32), @p177='?' (DbType = Int32), @p178='?' (DbType = DateTime2), @p179='?' (Size = 4000), @p180='?' (DbType = DateTime2), @p181='?' (Size = 4000), @p182='?' (DbType = DateTime2), @p183='?' (DbType = Int32), @p184='?' (DbType = Boolean), @p185='?' (DbType = Int32), @p186='?' (DbType = Int32), @p187='?' (DbType = DateTime2), @p188='?' (Size = 4000), @p189='?' (DbType = DateTime2), @p190='?' (Size = 4000), @p191='?' (DbType = DateTime2), @p192='?' (DbType = Int32), @p193='?' (DbType = Boolean), @p194='?' (DbType = Int32), @p195='?' (DbType = Int32), @p196='?' (DbType = DateTime2), @p197='?' (Size = 4000), @p198='?' (DbType = DateTime2), @p199='?' (Size = 4000), @p200='?' (DbType = DateTime2), @p201='?' (DbType = Int32), @p202='?' (DbType = Boolean), @p203='?' (DbType = Int32), @p204='?' (DbType = Int32), @p205='?' (DbType = DateTime2), @p206='?' (Size = 4000), @p207='?' (DbType = DateTime2), @p208='?' (Size = 4000), @p209='?' (DbType = DateTime2), @p210='?' (DbType = Int32), @p211='?' (DbType = Boolean), @p212='?' (DbType = Int32), @p213='?' (DbType = Int32), @p214='?' (DbType = DateTime2), @p215='?' (Size = 4000), @p216='?' (DbType = DateTime2), @p217='?' (Size = 4000), @p218='?' (DbType = DateTime2), @p219='?' (DbType = Int32), @p220='?' (DbType = Boolean), @p221='?' (DbType = Int32), @p222='?' (DbType = Int32), @p223='?' (DbType = DateTime2), @p224='?' (Size = 4000), @p225='?' (DbType = DateTime2), @p226='?' (Size = 4000), @p227='?' (DbType = DateTime2), @p228='?' (DbType = Int32), @p229='?' (DbType = Boolean), @p230='?' (DbType = Int32), @p231='?' (DbType = Int32), @p232='?' (DbType = DateTime2), @p233='?' (Size = 4000), @p234='?' (DbType = DateTime2), @p235='?' (Size = 4000), @p236='?' (DbType = DateTime2), @p237='?' (DbType = Int32), @p238='?' (DbType = Boolean), @p239='?' (DbType = Int32), @p240='?' (DbType = Int32), @p241='?' (DbType = DateTime2), @p242='?' (Size = 4000), @p243='?' (DbType = DateTime2), @p244='?' (Size = 4000), @p245='?' (DbType = DateTime2), @p246='?' (DbType = Int32), @p247='?' (DbType = Boolean), @p248='?' (DbType = Int32), @p249='?' (DbType = Int32), @p250='?' (DbType = DateTime2), @p251='?' (Size = 4000), @p252='?' (DbType = DateTime2), @p253='?' (Size = 4000), @p254='?' (DbType = DateTime2), @p255='?' (DbType = Int32), @p256='?' (DbType = Boolean), @p257='?' (DbType = Int32), @p258='?' (DbType = Int32), @p259='?' (DbType = DateTime2), @p260='?' (Size = 4000), @p261='?' (DbType = DateTime2), @p262='?' (Size = 4000), @p263='?' (DbType = DateTime2), @p264='?' (DbType = Int32), @p265='?' (DbType = Boolean), @p266='?' (DbType = Int32), @p267='?' (DbType = Int32), @p268='?' (DbType = DateTime2), @p269='?' (Size = 4000), @p270='?' (DbType = DateTime2), @p271='?' (Size = 4000), @p272='?' (DbType = DateTime2), @p273='?' (DbType = Int32), @p274='?' (DbType = Boolean), @p275='?' (DbType = Int32), @p276='?' (DbType = Int32), @p277='?' (DbType = DateTime2), @p278='?' (Size = 4000), @p279='?' (DbType = DateTime2), @p280='?' (Size = 4000), @p281='?' (DbType = DateTime2), @p282='?' (DbType = Int32), @p283='?' (DbType = Boolean), @p284='?' (DbType = Int32), @p285='?' (DbType = Int32), @p286='?' (DbType = DateTime2), @p287='?' (Size = 4000), @p288='?' (DbType = DateTime2), @p289='?' (Size = 4000), @p290='?' (DbType = DateTime2), @p291='?' (DbType = Int32), @p292='?' (DbType = Boolean), @p293='?' (DbType = Int32), @p294='?' (DbType = Int32), @p295='?' (DbType = DateTime2), @p296='?' (Size = 4000), @p297='?' (DbType = DateTime2), @p298='?' (Size = 4000), @p299='?' (DbType = DateTime2), @p300='?' (DbType = Int32), @p301='?' (DbType = Boolean), @p302='?' (DbType = Int32), @p303='?' (DbType = Int32), @p304='?' (DbType = DateTime2), @p305='?' (Size = 4000), @p306='?' (DbType = DateTime2), @p307='?' (Size = 4000), @p308='?' (DbType = DateTime2), @p309='?' (DbType = Int32), @p310='?' (DbType = Boolean), @p311='?' (DbType = Int32), @p312='?' (DbType = Int32), @p313='?' (DbType = DateTime2), @p314='?' (Size = 4000), @p315='?' (DbType = DateTime2), @p316='?' (Size = 4000), @p317='?' (DbType = DateTime2), @p318='?' (DbType = Int32), @p319='?' (DbType = Boolean), @p320='?' (DbType = Int32), @p321='?' (DbType = Int32), @p322='?' (DbType = DateTime2), @p323='?' (Size = 4000), @p324='?' (DbType = DateTime2), @p325='?' (DbType = Int32), @p326='?' (DbType = DateTime2), @p327='?' (Size = 4000), @p328='?' (DbType = Boolean), @p329='?' (DbType = Int32), @p330='?' (DbType = DateTime2), @p331='?' (Size = 4000), @p332='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
MERGE [RolePermissions] USING (
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, 0),
(@p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, 1),
(@p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, 2),
(@p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, 3),
(@p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, 4),
(@p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, 5),
(@p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, 6),
(@p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, 7),
(@p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, 8),
(@p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, 9),
(@p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, 10),
(@p99, @p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, 11),
(@p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, 12),
(@p117, @p118, @p119, @p120, @p121, @p122, @p123, @p124, @p125, 13),
(@p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, 14),
(@p135, @p136, @p137, @p138, @p139, @p140, @p141, @p142, @p143, 15),
(@p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152, 16),
(@p153, @p154, @p155, @p156, @p157, @p158, @p159, @p160, @p161, 17),
(@p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169, @p170, 18),
(@p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179, 19),
(@p180, @p181, @p182, @p183, @p184, @p185, @p186, @p187, @p188, 20),
(@p189, @p190, @p191, @p192, @p193, @p194, @p195, @p196, @p197, 21),
(@p198, @p199, @p200, @p201, @p202, @p203, @p204, @p205, @p206, 22),
(@p207, @p208, @p209, @p210, @p211, @p212, @p213, @p214, @p215, 23),
(@p216, @p217, @p218, @p219, @p220, @p221, @p222, @p223, @p224, 24),
(@p225, @p226, @p227, @p228, @p229, @p230, @p231, @p232, @p233, 25),
(@p234, @p235, @p236, @p237, @p238, @p239, @p240, @p241, @p242, 26),
(@p243, @p244, @p245, @p246, @p247, @p248, @p249, @p250, @p251, 27),
(@p252, @p253, @p254, @p255, @p256, @p257, @p258, @p259, @p260, 28),
(@p261, @p262, @p263, @p264, @p265, @p266, @p267, @p268, @p269, 29),
(@p270, @p271, @p272, @p273, @p274, @p275, @p276, @p277, @p278, 30),
(@p279, @p280, @p281, @p282, @p283, @p284, @p285, @p286, @p287, 31),
(@p288, @p289, @p290, @p291, @p292, @p293, @p294, @p295, @p296, 32),
(@p297, @p298, @p299, @p300, @p301, @p302, @p303, @p304, @p305, 33),
(@p306, @p307, @p308, @p309, @p310, @p311, @p312, @p313, @p314, 34),
(@p315, @p316, @p317, @p318, @p319, @p320, @p321, @p322, @p323, 35)) AS i ([CreatedAt], [CreatedBy], [GrantedAt], [GrantedBy], [IsDeleted], [PermissionId], [RoleId], [UpdatedAt], [UpdatedBy], _Position) ON 1=0
WHEN NOT MATCHED THEN
INSERT ([CreatedAt], [CreatedBy], [GrantedAt], [GrantedBy], [IsDeleted], [PermissionId], [RoleId], [UpdatedAt], [UpdatedBy])
VALUES (i.[CreatedAt], i.[CreatedBy], i.[GrantedAt], i.[GrantedBy], i.[IsDeleted], i.[PermissionId], i.[RoleId], i.[UpdatedAt], i.[UpdatedBy])
OUTPUT INSERTED.[Id], i._Position;
INSERT INTO [UserRoles] ([AssignedAt], [AssignedBy], [CreatedAt], [CreatedBy], [IsDeleted], [RoleId], [UpdatedAt], [UpdatedBy], [UserId])
OUTPUT INSERTED.[Id]
VALUES (@p324, @p325, @p326, @p327, @p328, @p329, @p330, @p331, @p332);
2025-08-14 14:50:07.227 +03:00 [INF] Authentication data seeding completed successfully
2025-08-14 14:50:07.228 +03:00 [INF] Database seeding completed successfully
2025-08-14 14:50:07.235 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:50:07.252 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:50:07.405 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:50:07.406 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:50:07.407 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:50:07.408 +03:00 [INF] Hosting environment: Development
2025-08-14 14:50:07.409 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:54:54.429 +03:00 [WRN] The foreign key property 'AccountStatement.PaymentId1' was created in shadow state because a conflicting property with the simple name 'PaymentId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-08-14 14:54:54.892 +03:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:54:54.917 +03:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:54:54.922 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-08-14 14:54:54.926 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-08-14 14:54:54.940 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-08-14 14:54:54.955 +03:00 [INF] No migrations were applied. The database is already up to date.
2025-08-14 14:54:54.957 +03:00 [INF] Database migrations applied successfully
2025-08-14 14:54:54.962 +03:00 [INF] Starting authentication data seeding...
2025-08-14 14:54:55.171 +03:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Permissions] AS [p]
        WHERE [p].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:54:55.183 +03:00 [INF] Permissions already exist, skipping seeding
2025-08-14 14:54:55.192 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Roles] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:54:55.196 +03:00 [INF] Roles already exist, skipping seeding
2025-08-14 14:54:55.217 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [Users] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = N'admin') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:54:55.220 +03:00 [INF] Admin user already exists, skipping seeding
2025-08-14 14:54:55.230 +03:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [RolePermissions] AS [r]
        WHERE [r].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:54:55.232 +03:00 [INF] Role permissions already exist, skipping seeding
2025-08-14 14:54:55.241 +03:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [UserRoles] AS [u]
        WHERE [u].[IsDeleted] = CAST(0 AS bit)) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-08-14 14:54:55.244 +03:00 [INF] User roles already exist, skipping seeding
2025-08-14 14:54:55.245 +03:00 [INF] Authentication data seeding completed successfully
2025-08-14 14:54:55.245 +03:00 [INF] Database seeding completed successfully
2025-08-14 14:54:55.249 +03:00 [INF] Starting Warehouse Management API
2025-08-14 14:54:55.267 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-14 14:54:55.358 +03:00 [INF] Now listening on: https://localhost:53878
2025-08-14 14:54:55.360 +03:00 [INF] Now listening on: http://localhost:53879
2025-08-14 14:54:55.361 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-14 14:54:55.362 +03:00 [INF] Hosting environment: Development
2025-08-14 14:54:55.363 +03:00 [INF] Content root path: G:\CodeAgumnet\StoreAugments\src\WarehouseManagement.API
2025-08-14 14:55:03.190 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 14:55:03.408 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 220.3152ms
2025-08-14 14:55:03.720 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 14:55:03.849 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 129.0045ms
2025-08-14 14:55:22.713 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/Auth/login - application/json 74
2025-08-14 14:55:22.738 +03:00 [INF] CORS policy execution failed.
2025-08-14 14:55:22.740 +03:00 [INF] Request origin https://localhost:53878 does not have permission to access the resource.
2025-08-14 14:55:22.766 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:55:22.791 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 14:55:22.904 +03:00 [INF] Executed DbCommand (12ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 14:55:23.072 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 14:55:23.083 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 286.0217ms
2025-08-14 14:55:23.085 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:55:23.091 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/Auth/login - 400 null application/json; charset=utf-8 377.9511ms
2025-08-14 14:56:48.834 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/Auth/login - application/json 74
2025-08-14 14:56:48.877 +03:00 [INF] CORS policy execution failed.
2025-08-14 14:56:48.878 +03:00 [INF] Request origin https://localhost:53878 does not have permission to access the resource.
2025-08-14 14:56:48.881 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:56:48.882 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 14:56:49.063 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 14:56:49.137 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 14:56:49.140 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 256.4948ms
2025-08-14 14:56:49.142 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:56:49.144 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/Auth/login - 400 null application/json; charset=utf-8 309.2837ms
2025-08-14 14:57:05.826 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/Auth/login - application/json 74
2025-08-14 14:57:05.830 +03:00 [INF] CORS policy execution failed.
2025-08-14 14:57:05.831 +03:00 [INF] Request origin https://localhost:53878 does not have permission to access the resource.
2025-08-14 14:57:05.835 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:05.836 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 14:57:05.846 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 14:57:05.916 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 14:57:05.919 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 79.5089ms
2025-08-14 14:57:05.920 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:05.921 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/Auth/login - 400 null application/json; charset=utf-8 94.85ms
2025-08-14 14:57:22.622 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:53878/api/auth/login - null null
2025-08-14 14:57:22.626 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:22.628 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:53878/api/auth/login - 204 null null 5.6054ms
2025-08-14 14:57:22.635 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/auth/login - application/json 62
2025-08-14 14:57:22.638 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:22.639 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:22.640 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 14:57:22.646 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 14:57:22.717 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 14:57:22.720 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 76.5977ms
2025-08-14 14:57:22.721 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:22.722 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/auth/login - 400 null application/json; charset=utf-8 87.2506ms
2025-08-14 14:57:22.731 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:53878/api/security/events - null null
2025-08-14 14:57:22.734 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:22.735 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:53878/api/security/events - 204 null null 4.1364ms
2025-08-14 14:57:22.738 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/security/events - application/json 148
2025-08-14 14:57:22.743 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:22.749 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:57:22.753 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:57:22.755 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/security/events - 401 0 null 16.9877ms
2025-08-14 14:57:48.830 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:53878/api/auth/login - null null
2025-08-14 14:57:48.833 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:48.834 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:53878/api/auth/login - 204 null null 4.421ms
2025-08-14 14:57:48.839 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/auth/login - application/json 62
2025-08-14 14:57:48.843 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:48.845 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:48.846 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 14:57:48.852 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 14:57:48.931 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 14:57:48.933 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 84.0802ms
2025-08-14 14:57:48.934 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 14:57:48.935 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/auth/login - 400 null application/json; charset=utf-8 95.9836ms
2025-08-14 14:57:48.943 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:53878/api/security/events - null null
2025-08-14 14:57:48.947 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:48.949 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:53878/api/security/events - 204 null null 5.4251ms
2025-08-14 14:57:48.952 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/security/events - application/json 148
2025-08-14 14:57:48.956 +03:00 [INF] CORS policy execution successful.
2025-08-14 14:57:48.960 +03:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-08-14 14:57:48.963 +03:00 [INF] AuthenticationScheme: Bearer was challenged.
2025-08-14 14:57:48.965 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/security/events - 401 0 null 12.7554ms
2025-08-14 14:59:59.096 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 14:59:59.100 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 4.7258ms
2025-08-14 14:59:59.313 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 14:59:59.333 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.8434ms
2025-08-14 15:00:17.076 +03:00 [INF] Request starting HTTP/2 POST https://localhost:53878/api/Auth/login - application/json 74
2025-08-14 15:00:17.079 +03:00 [INF] CORS policy execution failed.
2025-08-14 15:00:17.080 +03:00 [INF] Request origin https://localhost:53878 does not have permission to access the resource.
2025-08-14 15:00:17.081 +03:00 [INF] Executing endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 15:00:17.082 +03:00 [INF] Route matched with {action = "Login", controller = "Auth"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(WarehouseManagement.Core.Models.LoginRequest) on controller WarehouseManagement.API.Controllers.AuthController (WarehouseManagement.API).
2025-08-14 15:00:17.088 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__request_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[CreatedAt], [u].[CreatedBy], [u].[Email], [u].[FailedLoginAttempts], [u].[FirstName], [u].[IsActive], [u].[IsDeleted], [u].[IsEmailConfirmed], [u].[LastLoginAt], [u].[LastName], [u].[LockedOutUntil], [u].[PasswordChangedAt], [u].[PasswordHash], [u].[RefreshToken], [u].[RefreshTokenExpiryTime], [u].[Salt], [u].[UpdatedAt], [u].[UpdatedBy], [u].[Username]
FROM [Users] AS [u]
WHERE [u].[IsDeleted] = CAST(0 AS bit) AND [u].[Username] = @__request_Username_0
2025-08-14 15:00:17.158 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType5`3[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-08-14 15:00:17.160 +03:00 [INF] Executed action WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API) in 75.2972ms
2025-08-14 15:00:17.162 +03:00 [INF] Executed endpoint 'WarehouseManagement.API.Controllers.AuthController.Login (WarehouseManagement.API)'
2025-08-14 15:00:17.164 +03:00 [INF] Request finished HTTP/2 POST https://localhost:53878/api/Auth/login - 400 null application/json; charset=utf-8 88.1742ms
2025-08-14 15:23:59.581 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/index.html - null null
2025-08-14 15:23:59.606 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/index.html - 200 null text/html;charset=utf-8 24.5217ms
2025-08-14 15:23:59.914 +03:00 [INF] Request starting HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - null null
2025-08-14 15:23:59.933 +03:00 [INF] Request finished HTTP/2 GET https://localhost:53878/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.928ms
2025-08-14 15:24:02.860 +03:00 [INF] Application is shutting down...
