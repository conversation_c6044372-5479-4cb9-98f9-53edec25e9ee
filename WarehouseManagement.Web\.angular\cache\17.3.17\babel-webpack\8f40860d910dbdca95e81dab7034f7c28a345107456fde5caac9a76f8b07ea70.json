{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/authorization.service\";\nimport * as i2 from \"../services/auth.service\";\nexport class HasPermissionDirective {\n  set appHasPermission(permission) {\n    this.checkPermission(permission);\n  }\n  constructor(templateRef, viewContainer, authorizationService, authService) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.authorizationService = authorizationService;\n    this.authService = authService;\n    this.destroy$ = new Subject();\n    this.hasView = false;\n    this.appHasPermissionRequireAll = true;\n  }\n  ngOnInit() {\n    // Listen for authentication state changes\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateView();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  checkPermission(permission) {\n    this.updateView();\n  }\n  updateView() {\n    const hasPermission = this.evaluatePermission();\n    if (hasPermission && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasPermission && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n  evaluatePermission() {\n    // If user is not authenticated, deny access\n    if (!this.authService.isAuthenticated()) {\n      return false;\n    }\n    // Handle different input types\n    const permissionInput = this.getPermissionInput();\n    if (typeof permissionInput === 'string') {\n      // Simple string permission check (resource:action format)\n      return this.checkStringPermission(permissionInput);\n    }\n    if (Array.isArray(permissionInput)) {\n      // Multiple permissions\n      return this.checkMultiplePermissions(permissionInput);\n    }\n    if (typeof permissionInput === 'object') {\n      // Single permission object\n      return this.checkSinglePermission(permissionInput);\n    }\n    return false;\n  }\n  getPermissionInput() {\n    // Priority: direct input > resource/action combination\n    if (this.appHasPermissionResource && this.appHasPermissionAction) {\n      return {\n        resource: this.appHasPermissionResource,\n        action: this.appHasPermissionAction\n      };\n    }\n    // This will be set by the @Input setter\n    return '';\n  }\n  checkStringPermission(permission) {\n    // Handle different string formats\n    if (permission.includes(':')) {\n      // Format: \"resource:action\"\n      const [resource, action] = permission.split(':');\n      return this.authorizationService.hasPermission(resource, action);\n    }\n    if (permission.includes('.')) {\n      // Format: \"resource.action\"\n      const [resource, action] = permission.split('.');\n      return this.authorizationService.hasPermission(resource, action);\n    }\n    // Assume it's a role name\n    return this.authorizationService.hasRole(permission);\n  }\n  checkSinglePermission(permission) {\n    return this.authorizationService.hasPermission(permission.resource, permission.action);\n  }\n  checkMultiplePermissions(permissions) {\n    if (this.appHasPermissionRequireAll) {\n      // User must have ALL permissions (AND logic)\n      return this.authorizationService.hasAllPermissions(permissions);\n    } else {\n      // User must have ANY permission (OR logic)\n      return this.authorizationService.hasAnyPermission(permissions);\n    }\n  }\n  static {\n    this.ɵfac = function HasPermissionDirective_Factory(t) {\n      return new (t || HasPermissionDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.AuthorizationService), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: HasPermissionDirective,\n      selectors: [[\"\", \"appHasPermission\", \"\"]],\n      inputs: {\n        appHasPermission: \"appHasPermission\",\n        appHasPermissionResource: \"appHasPermissionResource\",\n        appHasPermissionAction: \"appHasPermissionAction\",\n        appHasPermissionRequireAll: \"appHasPermissionRequireAll\"\n      }\n    });\n  }\n}\nexport class HasRoleDirective {\n  set appHasRole(roles) {\n    this.checkRole(roles);\n  }\n  constructor(templateRef, viewContainer, authorizationService, authService) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.authorizationService = authorizationService;\n    this.authService = authService;\n    this.destroy$ = new Subject();\n    this.hasView = false;\n    this.appHasRoleRequireAll = false;\n  }\n  ngOnInit() {\n    // Listen for authentication state changes\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateView();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  checkRole(roles) {\n    this.updateView();\n  }\n  updateView() {\n    const hasRole = this.evaluateRole();\n    if (hasRole && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasRole && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n  evaluateRole() {\n    // If user is not authenticated, deny access\n    if (!this.authService.isAuthenticated()) {\n      return false;\n    }\n    const roleInput = this.getRoleInput();\n    if (typeof roleInput === 'string') {\n      return this.authorizationService.hasRole(roleInput);\n    }\n    if (Array.isArray(roleInput)) {\n      if (this.appHasRoleRequireAll) {\n        // User must have ALL roles\n        return roleInput.every(role => this.authorizationService.hasRole(role));\n      } else {\n        // User must have ANY role\n        return roleInput.some(role => this.authorizationService.hasRole(role));\n      }\n    }\n    return false;\n  }\n  getRoleInput() {\n    // This will be set by the @Input setter\n    return '';\n  }\n  static {\n    this.ɵfac = function HasRoleDirective_Factory(t) {\n      return new (t || HasRoleDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.AuthorizationService), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: HasRoleDirective,\n      selectors: [[\"\", \"appHasRole\", \"\"]],\n      inputs: {\n        appHasRole: \"appHasRole\",\n        appHasRoleRequireAll: \"appHasRoleRequireAll\"\n      }\n    });\n  }\n}\nexport class IsAuthenticatedDirective {\n  set appIsAuthenticated(show) {\n    this.showWhenAuthenticated = show;\n    this.updateView();\n  }\n  constructor(templateRef, viewContainer, authService) {\n    this.templateRef = templateRef;\n    this.viewContainer = viewContainer;\n    this.authService = authService;\n    this.destroy$ = new Subject();\n    this.hasView = false;\n    this.showWhenAuthenticated = true;\n  }\n  ngOnInit() {\n    // Listen for authentication state changes\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateView();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  updateView() {\n    const isAuthenticated = this.authService.isAuthenticated();\n    const shouldShow = this.showWhenAuthenticated ? isAuthenticated : !isAuthenticated;\n    if (shouldShow && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!shouldShow && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n  static {\n    this.ɵfac = function IsAuthenticatedDirective_Factory(t) {\n      return new (t || IsAuthenticatedDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: IsAuthenticatedDirective,\n      selectors: [[\"\", \"appIsAuthenticated\", \"\"]],\n      inputs: {\n        appIsAuthenticated: \"appIsAuthenticated\"\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "HasPermissionDirective", "appHasPermission", "permission", "checkPermission", "constructor", "templateRef", "viewContainer", "authorizationService", "authService", "destroy$", "<PERSON><PERSON><PERSON><PERSON>", "appHasPermissionRequireAll", "ngOnInit", "authState$", "pipe", "subscribe", "updateView", "ngOnDestroy", "next", "complete", "hasPermission", "evaluatePermission", "createEmbeddedView", "clear", "isAuthenticated", "permissionInput", "getPermissionInput", "checkStringPermission", "Array", "isArray", "checkMultiplePermissions", "checkSinglePermission", "appHasPermissionResource", "appHasPermissionAction", "resource", "action", "includes", "split", "hasRole", "permissions", "hasAllPermissions", "hasAnyPermission", "i0", "ɵɵdirectiveInject", "TemplateRef", "ViewContainerRef", "i1", "AuthorizationService", "i2", "AuthService", "selectors", "inputs", "HasRoleDirective", "appHasRole", "roles", "checkRole", "appHasRoleRequireAll", "evaluateRole", "roleInput", "getRoleInput", "every", "role", "some", "IsAuthenticatedDirective", "appIsAuthenticated", "show", "showWhenAuthenticated", "shouldShow"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\directives\\has-permission.directive.ts"], "sourcesContent": ["import { \n  Directive, \n  Input, \n  TemplateRef, \n  ViewContainerRef, \n  OnInit, \n  OnDestroy \n} from '@angular/core';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AuthorizationService, PermissionCheck } from '../services/authorization.service';\nimport { AuthService } from '../services/auth.service';\n\n@Directive({\n  selector: '[appHasPermission]'\n})\nexport class HasPermissionDirective implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  private hasView = false;\n\n  @Input() set appHasPermission(permission: string | PermissionCheck | PermissionCheck[]) {\n    this.checkPermission(permission);\n  }\n\n  @Input() appHasPermissionResource?: string;\n  @Input() appHasPermissionAction?: string;\n  @Input() appHasPermissionRequireAll?: boolean = true;\n\n  constructor(\n    private templateRef: TemplateRef<any>,\n    private viewContainer: ViewContainerRef,\n    private authorizationService: AuthorizationService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Listen for authentication state changes\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateView();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private checkPermission(permission: string | PermissionCheck | PermissionCheck[]): void {\n    this.updateView();\n  }\n\n  private updateView(): void {\n    const hasPermission = this.evaluatePermission();\n\n    if (hasPermission && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasPermission && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n\n  private evaluatePermission(): boolean {\n    // If user is not authenticated, deny access\n    if (!this.authService.isAuthenticated()) {\n      return false;\n    }\n\n    // Handle different input types\n    const permissionInput = this.getPermissionInput();\n\n    if (typeof permissionInput === 'string') {\n      // Simple string permission check (resource:action format)\n      return this.checkStringPermission(permissionInput);\n    }\n\n    if (Array.isArray(permissionInput)) {\n      // Multiple permissions\n      return this.checkMultiplePermissions(permissionInput);\n    }\n\n    if (typeof permissionInput === 'object') {\n      // Single permission object\n      return this.checkSinglePermission(permissionInput);\n    }\n\n    return false;\n  }\n\n  private getPermissionInput(): string | PermissionCheck | PermissionCheck[] {\n    // Priority: direct input > resource/action combination\n    if (this.appHasPermissionResource && this.appHasPermissionAction) {\n      return {\n        resource: this.appHasPermissionResource,\n        action: this.appHasPermissionAction\n      };\n    }\n\n    // This will be set by the @Input setter\n    return '';\n  }\n\n  private checkStringPermission(permission: string): boolean {\n    // Handle different string formats\n    if (permission.includes(':')) {\n      // Format: \"resource:action\"\n      const [resource, action] = permission.split(':');\n      return this.authorizationService.hasPermission(resource, action);\n    }\n\n    if (permission.includes('.')) {\n      // Format: \"resource.action\"\n      const [resource, action] = permission.split('.');\n      return this.authorizationService.hasPermission(resource, action);\n    }\n\n    // Assume it's a role name\n    return this.authorizationService.hasRole(permission);\n  }\n\n  private checkSinglePermission(permission: PermissionCheck): boolean {\n    return this.authorizationService.hasPermission(permission.resource, permission.action);\n  }\n\n  private checkMultiplePermissions(permissions: PermissionCheck[]): boolean {\n    if (this.appHasPermissionRequireAll) {\n      // User must have ALL permissions (AND logic)\n      return this.authorizationService.hasAllPermissions(permissions);\n    } else {\n      // User must have ANY permission (OR logic)\n      return this.authorizationService.hasAnyPermission(permissions);\n    }\n  }\n}\n\n@Directive({\n  selector: '[appHasRole]'\n})\nexport class HasRoleDirective implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  private hasView = false;\n\n  @Input() set appHasRole(roles: string | string[]) {\n    this.checkRole(roles);\n  }\n\n  @Input() appHasRoleRequireAll?: boolean = false;\n\n  constructor(\n    private templateRef: TemplateRef<any>,\n    private viewContainer: ViewContainerRef,\n    private authorizationService: AuthorizationService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Listen for authentication state changes\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateView();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private checkRole(roles: string | string[]): void {\n    this.updateView();\n  }\n\n  private updateView(): void {\n    const hasRole = this.evaluateRole();\n\n    if (hasRole && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!hasRole && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n\n  private evaluateRole(): boolean {\n    // If user is not authenticated, deny access\n    if (!this.authService.isAuthenticated()) {\n      return false;\n    }\n\n    const roleInput = this.getRoleInput();\n\n    if (typeof roleInput === 'string') {\n      return this.authorizationService.hasRole(roleInput);\n    }\n\n    if (Array.isArray(roleInput)) {\n      if (this.appHasRoleRequireAll) {\n        // User must have ALL roles\n        return roleInput.every(role => this.authorizationService.hasRole(role));\n      } else {\n        // User must have ANY role\n        return roleInput.some(role => this.authorizationService.hasRole(role));\n      }\n    }\n\n    return false;\n  }\n\n  private getRoleInput(): string | string[] {\n    // This will be set by the @Input setter\n    return '';\n  }\n}\n\n@Directive({\n  selector: '[appIsAuthenticated]'\n})\nexport class IsAuthenticatedDirective implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  private hasView = false;\n\n  @Input() set appIsAuthenticated(show: boolean) {\n    this.showWhenAuthenticated = show;\n    this.updateView();\n  }\n\n  private showWhenAuthenticated = true;\n\n  constructor(\n    private templateRef: TemplateRef<any>,\n    private viewContainer: ViewContainerRef,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Listen for authentication state changes\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        this.updateView();\n      });\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private updateView(): void {\n    const isAuthenticated = this.authService.isAuthenticated();\n    const shouldShow = this.showWhenAuthenticated ? isAuthenticated : !isAuthenticated;\n\n    if (shouldShow && !this.hasView) {\n      this.viewContainer.createEmbeddedView(this.templateRef);\n      this.hasView = true;\n    } else if (!shouldShow && this.hasView) {\n      this.viewContainer.clear();\n      this.hasView = false;\n    }\n  }\n}\n"], "mappings": "AAQA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;AAOzC,OAAM,MAAOC,sBAAsB;EAIjC,IAAaC,gBAAgBA,CAACC,UAAwD;IACpF,IAAI,CAACC,eAAe,CAACD,UAAU,CAAC;EAClC;EAMAE,YACUC,WAA6B,EAC7BC,aAA+B,EAC/BC,oBAA0C,EAC1CC,WAAwB;IAHxB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,WAAW,GAAXA,WAAW;IAfb,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;IAC9B,KAAAY,OAAO,GAAG,KAAK;IAQd,KAAAC,0BAA0B,GAAa,IAAI;EAOjD;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,UAAU,CACxBC,IAAI,CAACf,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEQhB,eAAeA,CAACD,UAAwD;IAC9E,IAAI,CAACc,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB,MAAMI,aAAa,GAAG,IAAI,CAACC,kBAAkB,EAAE;IAE/C,IAAID,aAAa,IAAI,CAAC,IAAI,CAACV,OAAO,EAAE;MAClC,IAAI,CAACJ,aAAa,CAACgB,kBAAkB,CAAC,IAAI,CAACjB,WAAW,CAAC;MACvD,IAAI,CAACK,OAAO,GAAG,IAAI;KACpB,MAAM,IAAI,CAACU,aAAa,IAAI,IAAI,CAACV,OAAO,EAAE;MACzC,IAAI,CAACJ,aAAa,CAACiB,KAAK,EAAE;MAC1B,IAAI,CAACb,OAAO,GAAG,KAAK;;EAExB;EAEQW,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAAC,IAAI,CAACb,WAAW,CAACgB,eAAe,EAAE,EAAE;MACvC,OAAO,KAAK;;IAGd;IACA,MAAMC,eAAe,GAAG,IAAI,CAACC,kBAAkB,EAAE;IAEjD,IAAI,OAAOD,eAAe,KAAK,QAAQ,EAAE;MACvC;MACA,OAAO,IAAI,CAACE,qBAAqB,CAACF,eAAe,CAAC;;IAGpD,IAAIG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE;MAClC;MACA,OAAO,IAAI,CAACK,wBAAwB,CAACL,eAAe,CAAC;;IAGvD,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;MACvC;MACA,OAAO,IAAI,CAACM,qBAAqB,CAACN,eAAe,CAAC;;IAGpD,OAAO,KAAK;EACd;EAEQC,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACM,wBAAwB,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAChE,OAAO;QACLC,QAAQ,EAAE,IAAI,CAACF,wBAAwB;QACvCG,MAAM,EAAE,IAAI,CAACF;OACd;;IAGH;IACA,OAAO,EAAE;EACX;EAEQN,qBAAqBA,CAACzB,UAAkB;IAC9C;IACA,IAAIA,UAAU,CAACkC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B;MACA,MAAM,CAACF,QAAQ,EAAEC,MAAM,CAAC,GAAGjC,UAAU,CAACmC,KAAK,CAAC,GAAG,CAAC;MAChD,OAAO,IAAI,CAAC9B,oBAAoB,CAACa,aAAa,CAACc,QAAQ,EAAEC,MAAM,CAAC;;IAGlE,IAAIjC,UAAU,CAACkC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC5B;MACA,MAAM,CAACF,QAAQ,EAAEC,MAAM,CAAC,GAAGjC,UAAU,CAACmC,KAAK,CAAC,GAAG,CAAC;MAChD,OAAO,IAAI,CAAC9B,oBAAoB,CAACa,aAAa,CAACc,QAAQ,EAAEC,MAAM,CAAC;;IAGlE;IACA,OAAO,IAAI,CAAC5B,oBAAoB,CAAC+B,OAAO,CAACpC,UAAU,CAAC;EACtD;EAEQ6B,qBAAqBA,CAAC7B,UAA2B;IACvD,OAAO,IAAI,CAACK,oBAAoB,CAACa,aAAa,CAAClB,UAAU,CAACgC,QAAQ,EAAEhC,UAAU,CAACiC,MAAM,CAAC;EACxF;EAEQL,wBAAwBA,CAACS,WAA8B;IAC7D,IAAI,IAAI,CAAC5B,0BAA0B,EAAE;MACnC;MACA,OAAO,IAAI,CAACJ,oBAAoB,CAACiC,iBAAiB,CAACD,WAAW,CAAC;KAChE,MAAM;MACL;MACA,OAAO,IAAI,CAAChC,oBAAoB,CAACkC,gBAAgB,CAACF,WAAW,CAAC;;EAElE;;;uBAvHWvC,sBAAsB,EAAA0C,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,WAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAtBjD,sBAAsB;MAAAkD,SAAA;MAAAC,MAAA;QAAAlD,gBAAA;QAAA+B,wBAAA;QAAAC,sBAAA;QAAAtB,0BAAA;MAAA;IAAA;EAAA;;AA6HnC,OAAM,MAAOyC,gBAAgB;EAI3B,IAAaC,UAAUA,CAACC,KAAwB;IAC9C,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;EACvB;EAIAlD,YACUC,WAA6B,EAC7BC,aAA+B,EAC/BC,oBAA0C,EAC1CC,WAAwB;IAHxB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,WAAW,GAAXA,WAAW;IAbb,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;IAC9B,KAAAY,OAAO,GAAG,KAAK;IAMd,KAAA8C,oBAAoB,GAAa,KAAK;EAO5C;EAEH5C,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,UAAU,CACxBC,IAAI,CAACf,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEQoC,SAASA,CAACD,KAAwB;IACxC,IAAI,CAACtC,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB,MAAMsB,OAAO,GAAG,IAAI,CAACmB,YAAY,EAAE;IAEnC,IAAInB,OAAO,IAAI,CAAC,IAAI,CAAC5B,OAAO,EAAE;MAC5B,IAAI,CAACJ,aAAa,CAACgB,kBAAkB,CAAC,IAAI,CAACjB,WAAW,CAAC;MACvD,IAAI,CAACK,OAAO,GAAG,IAAI;KACpB,MAAM,IAAI,CAAC4B,OAAO,IAAI,IAAI,CAAC5B,OAAO,EAAE;MACnC,IAAI,CAACJ,aAAa,CAACiB,KAAK,EAAE;MAC1B,IAAI,CAACb,OAAO,GAAG,KAAK;;EAExB;EAEQ+C,YAAYA,CAAA;IAClB;IACA,IAAI,CAAC,IAAI,CAACjD,WAAW,CAACgB,eAAe,EAAE,EAAE;MACvC,OAAO,KAAK;;IAGd,MAAMkC,SAAS,GAAG,IAAI,CAACC,YAAY,EAAE;IAErC,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAO,IAAI,CAACnD,oBAAoB,CAAC+B,OAAO,CAACoB,SAAS,CAAC;;IAGrD,IAAI9B,KAAK,CAACC,OAAO,CAAC6B,SAAS,CAAC,EAAE;MAC5B,IAAI,IAAI,CAACF,oBAAoB,EAAE;QAC7B;QACA,OAAOE,SAAS,CAACE,KAAK,CAACC,IAAI,IAAI,IAAI,CAACtD,oBAAoB,CAAC+B,OAAO,CAACuB,IAAI,CAAC,CAAC;OACxE,MAAM;QACL;QACA,OAAOH,SAAS,CAACI,IAAI,CAACD,IAAI,IAAI,IAAI,CAACtD,oBAAoB,CAAC+B,OAAO,CAACuB,IAAI,CAAC,CAAC;;;IAI1E,OAAO,KAAK;EACd;EAEQF,YAAYA,CAAA;IAClB;IACA,OAAO,EAAE;EACX;;;uBA3EWP,gBAAgB,EAAAV,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,WAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,oBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBG,gBAAgB;MAAAF,SAAA;MAAAC,MAAA;QAAAE,UAAA;QAAAG,oBAAA;MAAA;IAAA;EAAA;;AAiF7B,OAAM,MAAOO,wBAAwB;EAInC,IAAaC,kBAAkBA,CAACC,IAAa;IAC3C,IAAI,CAACC,qBAAqB,GAAGD,IAAI;IACjC,IAAI,CAACjD,UAAU,EAAE;EACnB;EAIAZ,YACUC,WAA6B,EAC7BC,aAA+B,EAC/BE,WAAwB;IAFxB,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAE,WAAW,GAAXA,WAAW;IAbb,KAAAC,QAAQ,GAAG,IAAIX,OAAO,EAAQ;IAC9B,KAAAY,OAAO,GAAG,KAAK;IAOf,KAAAwD,qBAAqB,GAAG,IAAI;EAMjC;EAEHtD,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,WAAW,CAACK,UAAU,CACxBC,IAAI,CAACf,SAAS,CAAC,IAAI,CAACU,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,MAAK;MACd,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,QAAQ,CAACS,IAAI,EAAE;IACpB,IAAI,CAACT,QAAQ,CAACU,QAAQ,EAAE;EAC1B;EAEQH,UAAUA,CAAA;IAChB,MAAMQ,eAAe,GAAG,IAAI,CAAChB,WAAW,CAACgB,eAAe,EAAE;IAC1D,MAAM2C,UAAU,GAAG,IAAI,CAACD,qBAAqB,GAAG1C,eAAe,GAAG,CAACA,eAAe;IAElF,IAAI2C,UAAU,IAAI,CAAC,IAAI,CAACzD,OAAO,EAAE;MAC/B,IAAI,CAACJ,aAAa,CAACgB,kBAAkB,CAAC,IAAI,CAACjB,WAAW,CAAC;MACvD,IAAI,CAACK,OAAO,GAAG,IAAI;KACpB,MAAM,IAAI,CAACyD,UAAU,IAAI,IAAI,CAACzD,OAAO,EAAE;MACtC,IAAI,CAACJ,aAAa,CAACiB,KAAK,EAAE;MAC1B,IAAI,CAACb,OAAO,GAAG,KAAK;;EAExB;;;uBA1CWqD,wBAAwB,EAAArB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,WAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAxBc,wBAAwB;MAAAb,SAAA;MAAAC,MAAA;QAAAa,kBAAA;MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}