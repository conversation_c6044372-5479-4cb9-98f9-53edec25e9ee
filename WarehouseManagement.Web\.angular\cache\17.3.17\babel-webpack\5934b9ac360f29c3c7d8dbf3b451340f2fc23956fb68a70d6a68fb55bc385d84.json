{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { TableModule } from 'primeng/table';\nimport { DialogModule } from 'primeng/dialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { MenubarModule } from 'primeng/menubar';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { CardModule } from 'primeng/card';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ChartModule } from 'primeng/chart';\nimport { TreeModule } from 'primeng/tree';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { ImageModule } from 'primeng/image';\nimport { TagModule } from 'primeng/tag';\nimport { BadgeModule } from 'primeng/badge';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { PaginatorModule } from 'primeng/paginator';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TabViewModule } from 'primeng/tabview';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), provideAnimations(), importProvidersFrom([BrowserModule, BrowserAnimationsModule, FormsModule, ReactiveFormsModule,\n  // PrimeNG Modules\n  ButtonModule, InputTextModule, TableModule, DialogModule, DropdownModule, CalendarModule, InputNumberModule, InputTextareaModule, CheckboxModule, RadioButtonModule, MenubarModule, SidebarModule, PanelMenuModule, CardModule, ToastModule, ConfirmDialogModule, ProgressSpinnerModule, ChartModule, TreeModule, FileUploadModule, ImageModule, TagModule, BadgeModule, ToolbarModule, SplitButtonModule, PaginatorModule, InputSwitchModule, TooltipModule, TabViewModule])]\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}