[{"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.Core.Models.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "ConfirmResetPassword", "RelativePath": "api/Auth/confirm-reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.Core.Models.ConfirmResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.Core.Models.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.Core.Models.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.AuthController", "Method": "ValidateSession", "RelativePath": "api/Auth/validate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "GetCategories", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "CreateCategory", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "WarehouseManagement.Application.DTOs.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "GetCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "UpdateCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "WarehouseManagement.Application.DTOs.UpdateCategoryDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "DeleteCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.CategoriesController", "Method": "GetCategoryTree", "RelativePath": "api/Categories/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetItems", "RelativePath": "api/Items", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "WarehouseId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "CreateItem", "RelativePath": "api/Items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "WarehouseManagement.Application.DTOs.CreateItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetItem", "RelativePath": "api/Items/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "UpdateItem", "RelativePath": "api/Items/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "WarehouseManagement.Application.DTOs.UpdateItemDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "DeleteItem", "RelativePath": "api/Items/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetAlternativeItems", "RelativePath": "api/Items/{id}/alternatives", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "AddAlternativeItem", "RelativePath": "api/Items/{id}/alternatives", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WarehouseManagement.API.Controllers.AddAlternativeItemRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "RemoveAlternativeItem", "RelativePath": "api/Items/{id}/alternatives/{alternativeItemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "alternativeItemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetItemsByCategory", "RelativePath": "api/Items/by-category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetItemByCode", "RelativePath": "api/Items/by-code/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.ItemsController", "Method": "GetItemsByWarehouse", "RelativePath": "api/Items/by-warehouse/{warehouseId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "warehouseId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "GetSecurityConfig", "RelativePath": "api/Security/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "UpdateSecurityConfig", "RelativePath": "api/Security/config", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "config", "Type": "WarehouseManagement.Core.Models.SecurityConfigDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "GetCsrfToken", "RelativePath": "api/Security/csrf-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "LogSecurityEvent", "RelativePath": "api/Security/events", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.API.Controllers.LogSecurityEventRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "GetSecurityEvents", "RelativePath": "api/Security/events", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "eventType", "Type": "System.String", "IsRequired": false}, {"Name": "severity", "Type": "System.String", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "LogLoginAttempt", "RelativePath": "api/Security/login-attempts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WarehouseManagement.API.Controllers.LogLoginAttemptRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "GetLoginAttempts", "RelativePath": "api/Security/login-attempts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "isSuccessful", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WarehouseManagement.API.Controllers.SecurityController", "Method": "GetRateLimitInfo", "RelativePath": "api/Security/rate-limit/{action}/{identifier}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "action", "Type": "System.String", "IsRequired": true}, {"Name": "identifier", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}]