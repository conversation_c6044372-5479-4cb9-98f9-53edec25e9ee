{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport { MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { MessageModule } from 'primeng/message';\nimport { ToastModule } from 'primeng/toast';\nimport { InputGroupModule } from 'primeng/inputgroup';\nimport { InputGroupAddonModule } from 'primeng/inputgroupaddon';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/language.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/message\";\nimport * as i11 from \"primeng/toast\";\nconst _c0 = a0 => ({\n  time: a0\n});\nconst _c1 = a0 => ({\n  attempts: a0\n});\nfunction LoginComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"p-message\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", ctx_r0.languageService.translate(\"auth.login.lockout.active\", i0.ɵɵpureFunction1(1, _c0, ctx_r0.remainingLockoutTime)));\n  }\n}\nfunction LoginComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"username\"), \" \");\n  }\n}\nfunction LoginComponent_small_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"p-message\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", ctx_r0.languageService.translate(\"auth.login.attemptsRemaining\", i0.ɵɵpureFunction1(1, _c1, ctx_r0.remainingAttempts)));\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, languageService, router, route, messageService) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.languageService = languageService;\n    this.router = router;\n    this.route = route;\n    this.messageService = messageService;\n    this.loading = false;\n    this.returnUrl = '/dashboard';\n    this.showPassword = false;\n    this.loginAttempts = 0;\n    this.maxLoginAttempts = 5;\n    this.lockoutTime = 15; // minutes\n    this.isLockedOut = false;\n    this.destroy$ = new Subject();\n    this.loginForm = this.createLoginForm();\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    // Check if user is already authenticated\n    this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(authState => {\n      if (authState.isAuthenticated) {\n        this.router.navigate([this.returnUrl]);\n      }\n      if (authState.error) {\n        this.handleLoginError(authState.error);\n      }\n      this.loading = authState.loading;\n    });\n    // Check for lockout status\n    this.checkLockoutStatus();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createLoginForm() {\n    return this.formBuilder.group({\n      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50)]],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.invalid || this.loading || this.isLockedOut) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.loading = true;\n    const loginRequest = {\n      username: this.loginForm.value.username.trim(),\n      password: this.loginForm.value.password,\n      rememberMe: this.loginForm.value.rememberMe\n    };\n    this.authService.login(loginRequest).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        this.loginAttempts = 0;\n        this.clearLockoutStatus();\n        this.messageService.add({\n          severity: 'success',\n          summary: this.languageService.translate('auth.login.success'),\n          detail: this.languageService.translate('auth.login.welcomeBack', {\n            name: response.user.firstName\n          })\n        });\n        // Navigate to return URL or dashboard\n        this.router.navigate([this.returnUrl]);\n      },\n      error: error => {\n        this.handleLoginError(error);\n      }\n    });\n  }\n  handleLoginError(error) {\n    this.loading = false;\n    this.loginAttempts++;\n    let errorMessage = this.languageService.translate('auth.login.error.generic');\n    if (error?.status === 401) {\n      errorMessage = this.languageService.translate('auth.login.error.invalidCredentials');\n    } else if (error?.status === 423) {\n      errorMessage = this.languageService.translate('auth.login.error.accountLocked');\n      this.handleAccountLockout();\n    } else if (error?.status === 429) {\n      errorMessage = this.languageService.translate('auth.login.error.tooManyAttempts');\n      this.handleRateLimitExceeded();\n    } else if (error?.message) {\n      errorMessage = error.message;\n    }\n    this.messageService.add({\n      severity: 'error',\n      summary: this.languageService.translate('auth.login.error.title'),\n      detail: errorMessage\n    });\n    // Check if we should lock out the user\n    if (this.loginAttempts >= this.maxLoginAttempts) {\n      this.handleAccountLockout();\n    }\n    // Clear password field on error\n    this.loginForm.patchValue({\n      password: ''\n    });\n  }\n  handleAccountLockout() {\n    this.isLockedOut = true;\n    this.lockoutEndTime = new Date(Date.now() + this.lockoutTime * 60 * 1000);\n    // Store lockout info in localStorage\n    localStorage.setItem('lockoutEndTime', this.lockoutEndTime.toISOString());\n    localStorage.setItem('loginAttempts', this.loginAttempts.toString());\n    this.messageService.add({\n      severity: 'warn',\n      summary: this.languageService.translate('auth.login.lockout.title'),\n      detail: this.languageService.translate('auth.login.lockout.message', {\n        minutes: this.lockoutTime\n      }),\n      life: 10000\n    });\n    // Start countdown timer\n    this.startLockoutTimer();\n  }\n  handleRateLimitExceeded() {\n    this.messageService.add({\n      severity: 'warn',\n      summary: this.languageService.translate('auth.login.rateLimit.title'),\n      detail: this.languageService.translate('auth.login.rateLimit.message'),\n      life: 8000\n    });\n  }\n  checkLockoutStatus() {\n    const lockoutEndTimeStr = localStorage.getItem('lockoutEndTime');\n    const storedAttempts = localStorage.getItem('loginAttempts');\n    if (lockoutEndTimeStr && storedAttempts) {\n      const lockoutEndTime = new Date(lockoutEndTimeStr);\n      const now = new Date();\n      if (now < lockoutEndTime) {\n        this.isLockedOut = true;\n        this.lockoutEndTime = lockoutEndTime;\n        this.loginAttempts = parseInt(storedAttempts, 10);\n        this.startLockoutTimer();\n      } else {\n        this.clearLockoutStatus();\n      }\n    }\n  }\n  startLockoutTimer() {\n    if (!this.lockoutEndTime) return;\n    const checkLockout = () => {\n      const now = new Date();\n      if (this.lockoutEndTime && now >= this.lockoutEndTime) {\n        this.clearLockoutStatus();\n        this.messageService.add({\n          severity: 'info',\n          summary: this.languageService.translate('auth.login.lockout.expired'),\n          detail: this.languageService.translate('auth.login.lockout.canTryAgain')\n        });\n      } else {\n        setTimeout(checkLockout, 1000);\n      }\n    };\n    setTimeout(checkLockout, 1000);\n  }\n  clearLockoutStatus() {\n    this.isLockedOut = false;\n    this.lockoutEndTime = undefined;\n    this.loginAttempts = 0;\n    localStorage.removeItem('lockoutEndTime');\n    localStorage.removeItem('loginAttempts');\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  switchLanguage() {\n    this.languageService.toggleLanguage();\n  }\n  // Getter methods for template\n  get username() {\n    return this.loginForm.get('username');\n  }\n  get password() {\n    return this.loginForm.get('password');\n  }\n  get rememberMe() {\n    return this.loginForm.get('rememberMe');\n  }\n  get remainingLockoutTime() {\n    if (!this.lockoutEndTime) return '';\n    const now = new Date();\n    const remaining = Math.max(0, this.lockoutEndTime.getTime() - now.getTime());\n    const minutes = Math.floor(remaining / 60000);\n    const seconds = Math.floor(remaining % 60000 / 1000);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  get remainingAttempts() {\n    return Math.max(0, this.maxLoginAttempts - this.loginAttempts);\n  }\n  // Form validation helpers\n  isFieldInvalid(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldError(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (!field || !field.errors) return '';\n    const errors = field.errors;\n    if (errors['required']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.required`);\n    }\n    if (errors['minlength']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.minLength`, {\n        min: errors['minlength'].requiredLength\n      });\n    }\n    if (errors['maxlength']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.maxLength`, {\n        max: errors['maxlength'].requiredLength\n      });\n    }\n    return this.languageService.translate('auth.login.validation.invalid');\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.LanguageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MessageService]), i0.ɵɵStandaloneFeature],\n      decls: 64,\n      vars: 37,\n      consts: [[1, \"login-container\"], [1, \"login-wrapper\"], [1, \"language-switcher\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-text\", \"p-button-sm\", 3, \"click\", \"label\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"pi\", \"pi-box\", \"text-4xl\", \"text-primary\"], [1, \"login-title\"], [1, \"login-subtitle\"], [\"class\", \"lockout-warning\", 4, \"ngIf\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"field\"], [\"for\", \"username\", 1, \"field-label\"], [1, \"required-asterisk\"], [1, \"p-inputgroup\"], [1, \"p-inputgroup-addon\"], [1, \"pi\", \"pi-user\"], [\"id\", \"username\", \"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\", 3, \"placeholder\", \"disabled\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"field-label\"], [1, \"pi\", \"pi-lock\"], [\"id\", \"password\", \"pInputText\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\", \"placeholder\", \"disabled\"], [1, \"p-inputgroup-addon\", \"cursor-pointer\", 3, \"click\"], [1, \"field-checkbox\"], [\"formControlName\", \"rememberMe\", \"inputId\", \"rememberMe\", 3, \"binary\", \"disabled\"], [\"for\", \"rememberMe\", 1, \"checkbox-label\"], [\"class\", \"attempts-warning\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", 1, \"login-button\", \"w-full\", 3, \"label\", \"loading\", \"disabled\"], [1, \"forgot-password\"], [\"href\", \"#\", 1, \"forgot-password-link\", 3, \"click\"], [1, \"login-footer\"], [1, \"footer-text\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"footer-separator\"], [1, \"security-notice\"], [\"severity\", \"info\", 3, \"text\"], [1, \"login-background\"], [1, \"background-pattern\"], [\"position\", \"top-right\"], [1, \"lockout-warning\"], [\"severity\", \"warn\", 3, \"text\"], [1, \"p-error\"], [1, \"attempts-warning\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_3_listener() {\n            return ctx.switchLanguage();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"h1\", 8);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 9);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, LoginComponent_div_12_Template, 2, 3, \"div\", 10);\n          i0.ɵɵelementStart(13, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementStart(17, \"span\", 14);\n          i0.ɵɵtext(18, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 15)(20, \"span\", 16);\n          i0.ɵɵelement(21, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, LoginComponent_small_23_Template, 2, 1, \"small\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 20);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementStart(27, \"span\", 14);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 15)(30, \"span\", 16);\n          i0.ɵɵelement(31, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 22);\n          i0.ɵɵelementStart(33, \"span\", 23);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_33_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelement(34, \"i\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(35, LoginComponent_small_35_Template, 2, 1, \"small\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 24);\n          i0.ɵɵelement(37, \"p-checkbox\", 25);\n          i0.ɵɵelementStart(38, \"label\", 26);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(40, LoginComponent_div_40_Template, 2, 3, \"div\", 27);\n          i0.ɵɵelement(41, \"button\", 28);\n          i0.ɵɵelementStart(42, \"div\", 29)(43, \"a\", 30);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_43_listener($event) {\n            return $event.preventDefault();\n          });\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 31)(46, \"p\", 32);\n          i0.ɵɵtext(47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 33)(49, \"a\", 34);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 35);\n          i0.ɵɵtext(52, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"a\", 34);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 35);\n          i0.ɵɵtext(56, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"a\", 34);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 36);\n          i0.ɵɵelement(60, \"p-message\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 38);\n          i0.ɵɵelement(62, \"div\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(63, \"p-toast\", 40);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"rtl\", ctx.languageService.isArabic());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.languageService.isArabic() ? \"English\" : \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.title\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.subtitle\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLockedOut);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.username\"), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"ng-invalid\", ctx.isFieldInvalid(\"username\"));\n          i0.ɵɵproperty(\"placeholder\", ctx.languageService.translate(\"auth.login.usernamePlaceholder\"))(\"disabled\", ctx.loading || ctx.isLockedOut);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.password\"), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"ng-invalid\", ctx.isFieldInvalid(\"password\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"placeholder\", ctx.languageService.translate(\"auth.login.passwordPlaceholder\"))(\"disabled\", ctx.loading || ctx.isLockedOut);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMap(ctx.showPassword ? \"pi pi-eye-slash\" : \"pi pi-eye\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"password\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"binary\", true)(\"disabled\", ctx.loading || ctx.isLockedOut);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.rememberMe\"), \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loginAttempts > 0 && !ctx.isLockedOut);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.languageService.translate(\"auth.login.signIn\"))(\"loading\", ctx.loading)(\"disabled\", ctx.loginForm.invalid || ctx.isLockedOut);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.forgotPassword\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.footer.copyright\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.privacy\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.terms\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.support\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"text\", ctx.languageService.translate(\"auth.login.securityNotice\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.languageService.isArabic() ? \"rtl-toast\" : \"\");\n        }\n      },\n      dependencies: [CommonModule, i6.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, ButtonModule, i7.ButtonDirective, InputTextModule, i8.InputText, PasswordModule, CheckboxModule, i9.Checkbox, MessageModule, i10.UIMessage, ToastModule, i11.Toast, InputGroupModule, InputGroupAddonModule],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 2rem;\\n}\\n.login-container.rtl[_ngcontent-%COMP%] {\\n  direction: rtl;\\n}\\n\\n.login-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n.language-switcher[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -60px;\\n  right: 0;\\n  z-index: 3;\\n}\\n.rtl[_ngcontent-%COMP%]   .language-switcher[_ngcontent-%COMP%] {\\n  right: auto;\\n  left: 0;\\n}\\n.language-switcher[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.language-switcher[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-radius: 20px;\\n  padding: 3rem 2.5rem;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.login-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2.5rem;\\n}\\n.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n  margin: 0 0 0.5rem 0;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.login-header[_ngcontent-%COMP%]   .login-subtitle[_ngcontent-%COMP%] {\\n  color: var(--text-color-secondary);\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  font-size: 0.9rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   .required-asterisk[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 0.25rem;\\n}\\n.rtl[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   .required-asterisk[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 0.25rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon[_ngcontent-%COMP%] {\\n  background: var(--surface-100);\\n  border-color: var(--surface-300);\\n  color: var(--text-color-secondary);\\n  min-width: 3rem;\\n  justify-content: center;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon.cursor-pointer[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon.cursor-pointer[_ngcontent-%COMP%]:hover {\\n  background: var(--surface-200);\\n  color: var(--primary-color);\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-color: var(--surface-300);\\n  transition: all 0.2s ease;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.2);\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input.ng-invalid.ng-touched[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input.ng-invalid.ng-touched[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.2);\\n}\\n.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-error[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-top: 0.25rem;\\n  font-size: 0.8rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  font-size: 0.9rem;\\n  color: var(--text-color-secondary);\\n  cursor: pointer;\\n}\\n.rtl[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 0.5rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .attempts-warning[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .attempts-warning[_ngcontent-%COMP%]     .p-message {\\n  margin: 0;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border: none;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:not(:disabled):hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s ease;\\n}\\n.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:not(:disabled):hover::before {\\n  left: 100%;\\n}\\n.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 1.5rem;\\n}\\n.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  transition: all 0.2s ease;\\n}\\n.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: var(--primary-color-text);\\n}\\n\\n.lockout-warning[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.lockout-warning[_ngcontent-%COMP%]     .p-message {\\n  margin: 0;\\n}\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  text-align: center;\\n  padding-top: 1.5rem;\\n  border-top: 1px solid var(--surface-200);\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%] {\\n  color: var(--text-color-secondary);\\n  font-size: 0.8rem;\\n  margin: 0 0 0.5rem 0;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%] {\\n  color: var(--text-color-secondary);\\n  text-decoration: none;\\n  font-size: 0.8rem;\\n  transition: color 0.2s ease;\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-separator[_ngcontent-%COMP%] {\\n  color: var(--text-color-secondary);\\n  font-size: 0.8rem;\\n}\\n\\n.security-notice[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.security-notice[_ngcontent-%COMP%]     .p-message {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n}\\n\\n.login-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1;\\n  overflow: hidden;\\n}\\n.login-background[_ngcontent-%COMP%]   .background-pattern[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  animation: _ngcontent-%COMP%_float 20s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px) rotate(0deg);\\n  }\\n  50% {\\n    transform: translateY(-20px) rotate(180deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n  }\\n  .login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .language-switcher[_ngcontent-%COMP%] {\\n    position: static;\\n    margin-bottom: 1rem;\\n    text-align: center;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  .login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%] {\\n    height: 2.5rem;\\n  }\\n}\\n.dark-theme[_nghost-%COMP%]   .login-card[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .login-card[_ngcontent-%COMP%] {\\n  background: rgba(30, 30, 30, 0.95);\\n  border-color: rgba(255, 255, 255, 0.1);\\n}\\n.dark-theme[_nghost-%COMP%]   .login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n@media (prefers-contrast: high) {\\n  .login-card[_ngcontent-%COMP%] {\\n    background: white;\\n    border: 2px solid black;\\n  }\\n  .login-button[_ngcontent-%COMP%] {\\n    background: black;\\n    color: white;\\n  }\\n}\\n  .rtl-toast .p-toast-message {\\n  direction: rtl;\\n  text-align: right;\\n}\\n  .rtl-toast .p-toast-message .p-toast-message-content {\\n  flex-direction: row-reverse;\\n}\\n  .rtl-toast .p-toast-message .p-toast-message-text {\\n  text-align: right;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYXV0aC9sb2dpbi9sb2dpbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLDZEQUFBO0VBQ0EsYUFBQTtBQUNGO0FBQ0U7RUFDRSxjQUFBO0FBQ0o7O0FBR0E7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7QUFBRjs7QUFHQTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLFFBQUE7RUFDQSxVQUFBO0FBQUY7QUFFRTtFQUNFLFdBQUE7RUFDQSxPQUFBO0FBQUo7QUFHRTtFQUNFLG9DQUFBO0VBQ0EsMENBQUE7RUFDQSxZQUFBO0VBQ0EsbUNBQUE7VUFBQSwyQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7QUFESjtBQUdJO0VBQ0Usb0NBQUE7RUFDQSwyQkFBQTtBQUROOztBQU1BO0VBQ0UscUNBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLDhFQUNFO0VBRUYsMENBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBTEY7QUFPRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtBQUxKOztBQVNBO0VBQ0Usa0JBQUE7RUFDQSxxQkFBQTtBQU5GO0FBUUU7RUFDRSxtQkFBQTtBQU5KO0FBU0U7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3QkFBQTtFQUNBLG9CQUFBO0VBQ0EscURBQUE7RUFDQSw2QkFBQTtFQUNBLG9DQUFBO0VBQ0EscUJBQUE7QUFQSjtBQVVFO0VBQ0Usa0NBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtBQVJKOztBQWFFO0VBQ0UscUJBQUE7QUFWSjtBQVlJO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3QkFBQTtFQUNBLGlCQUFBO0FBVk47QUFZTTtFQUNFLGNBQUE7RUFDQSxvQkFBQTtBQVZSO0FBWVE7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUFWVjtBQWdCTTtFQUNFLDhCQUFBO0VBQ0EsZ0NBQUE7RUFDQSxrQ0FBQTtFQUNBLGVBQUE7RUFDQSx1QkFBQTtBQWRSO0FBZ0JRO0VBQ0UsZUFBQTtFQUNBLHlCQUFBO0FBZFY7QUFnQlU7RUFDRSw4QkFBQTtFQUNBLDJCQUFBO0FBZFo7QUFtQk07RUFDRSxnQ0FBQTtFQUNBLHlCQUFBO0FBakJSO0FBbUJRO0VBQ0Usa0NBQUE7RUFDQSw0REFBQTtBQWpCVjtBQW9CUTtFQUNFLHFCQUFBO0FBbEJWO0FBb0JVO0VBQ0UsK0NBQUE7QUFsQlo7QUF3Qkk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtBQXRCTjtBQTBCRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBeEJKO0FBMEJJO0VBQ0UsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGtDQUFBO0VBQ0EsZUFBQTtBQXhCTjtBQTBCTTtFQUNFLGNBQUE7RUFDQSxvQkFBQTtBQXhCUjtBQTZCRTtFQUNFLHFCQUFBO0FBM0JKO0FBNkJJO0VBQ0UsU0FBQTtBQTNCTjtBQStCRTtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxREFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQTdCSjtBQStCSTtFQUNFLDJCQUFBO0VBQ0EsZ0RBQUE7QUE3Qk47QUFnQ0k7RUFDRSxZQUFBO0VBQ0EsbUJBQUE7QUE5Qk47QUFpQ0k7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0Esc0ZBQUE7RUFDQSwwQkFBQTtBQS9CTjtBQWtDSTtFQUNFLFVBQUE7QUFoQ047QUFvQ0U7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0FBbENKO0FBb0NJO0VBQ0UsMkJBQUE7RUFDQSxxQkFBQTtFQUNBLGlCQUFBO0VBQ0EseUJBQUE7QUFsQ047QUFvQ007RUFDRSwwQkFBQTtFQUNBLGdDQUFBO0FBbENSOztBQXdDQTtFQUNFLHFCQUFBO0FBckNGO0FBdUNFO0VBQ0UsU0FBQTtBQXJDSjs7QUF5Q0E7RUFDRSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSx3Q0FBQTtBQXRDRjtBQXdDRTtFQUNFLGtDQUFBO0VBQ0EsaUJBQUE7RUFDQSxvQkFBQTtBQXRDSjtBQXlDRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUF2Q0o7QUF5Q0k7RUFDRSxrQ0FBQTtFQUNBLHFCQUFBO0VBQ0EsaUJBQUE7RUFDQSwyQkFBQTtBQXZDTjtBQXlDTTtFQUNFLDJCQUFBO0FBdkNSO0FBMkNJO0VBQ0Usa0NBQUE7RUFDQSxpQkFBQTtBQXpDTjs7QUE4Q0E7RUFDRSxrQkFBQTtBQTNDRjtBQTZDRTtFQUNFLG9DQUFBO0VBQ0EsMENBQUE7RUFDQSxtQ0FBQTtVQUFBLDJCQUFBO0VBQ0EsbUJBQUE7QUEzQ0o7O0FBK0NBO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsVUFBQTtFQUNBLGdCQUFBO0FBNUNGO0FBOENFO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0Esb0xBQ0U7RUFFRix5Q0FBQTtBQTlDSjs7QUFrREE7RUFDRTtJQUNFLHVDQUFBO0VBL0NGO0VBaURBO0lBQ0UsMkNBQUE7RUEvQ0Y7QUFDRjtBQW1EQTtFQUNFO0lBQ0UsYUFBQTtFQWpERjtFQW9EQTtJQUNFLG9CQUFBO0VBbERGO0VBc0RFO0lBQ0UsaUJBQUE7RUFwREo7RUF3REE7SUFDRSxnQkFBQTtJQUNBLG1CQUFBO0lBQ0Esa0JBQUE7RUF0REY7QUFDRjtBQXlEQTtFQUNFO0lBQ0Usb0JBQUE7RUF2REY7RUEyREU7SUFDRSxtQkFBQTtFQXpESjtFQTRERTtJQUNFLGNBQUE7RUExREo7QUFDRjtBQWdFRTtFQUNFLGtDQUFBO0VBQ0Esc0NBQUE7QUE5REo7QUFrRUk7RUFDRSxZQUFBO0FBaEVOOztBQXNFQTtFQUNFO0lBQ0UsaUJBQUE7SUFDQSx1QkFBQTtFQW5FRjtFQXNFQTtJQUNFLGlCQUFBO0lBQ0EsWUFBQTtFQXBFRjtBQUNGO0FBeUVFO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0FBdkVKO0FBeUVJO0VBQ0UsMkJBQUE7QUF2RU47QUEwRUk7RUFDRSxpQkFBQTtBQXhFTiIsInNvdXJjZXNDb250ZW50IjpbIi5sb2dpbi1jb250YWluZXIge1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgcGFkZGluZzogMnJlbTtcblxuICAmLnJ0bCB7XG4gICAgZGlyZWN0aW9uOiBydGw7XG4gIH1cbn1cblxuLmxvZ2luLXdyYXBwZXIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHotaW5kZXg6IDI7XG4gIHdpZHRoOiAxMDAlO1xuICBtYXgtd2lkdGg6IDQwMHB4O1xufVxuXG4ubGFuZ3VhZ2Utc3dpdGNoZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogLTYwcHg7XG4gIHJpZ2h0OiAwO1xuICB6LWluZGV4OiAzO1xuXG4gIC5ydGwgJiB7XG4gICAgcmlnaHQ6IGF1dG87XG4gICAgbGVmdDogMDtcbiAgfVxuXG4gIGJ1dHRvbiB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICAgJjpob3ZlciB7XG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gICAgfVxuICB9XG59XG5cbi5sb2dpbi1jYXJkIHtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBwYWRkaW5nOiAzcmVtIDIuNXJlbTtcbiAgYm94LXNoYWRvdzogXG4gICAgMCAyMHB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjEpLFxuICAgIDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gICY6OmJlZm9yZSB7XG4gICAgY29udGVudDogJyc7XG4gICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgIHRvcDogMDtcbiAgICBsZWZ0OiAwO1xuICAgIHJpZ2h0OiAwO1xuICAgIGhlaWdodDogNHB4O1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzY2N2VlYSwgIzc2NGJhMik7XG4gIH1cbn1cblxuLmxvZ2luLWhlYWRlciB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMi41cmVtO1xuXG4gIC5sb2dvIHtcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xuICB9XG5cbiAgLmxvZ2luLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XG4gICAgbWFyZ2luOiAwIDAgMC41cmVtIDA7XG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7XG4gICAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG4gICAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgfVxuXG4gIC5sb2dpbi1zdWJ0aXRsZSB7XG4gICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICBtYXJnaW46IDA7XG4gICAgZm9udC1zaXplOiAxcmVtO1xuICB9XG59XG5cbi5sb2dpbi1mb3JtIHtcbiAgLmZpZWxkIHtcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAuZmllbGQtbGFiZWwge1xuICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3IpO1xuICAgICAgZm9udC1zaXplOiAwLjlyZW07XG5cbiAgICAgIC5yZXF1aXJlZC1hc3RlcmlzayB7XG4gICAgICAgIGNvbG9yOiAjZWY0NDQ0O1xuICAgICAgICBtYXJnaW4tbGVmdDogMC4yNXJlbTtcblxuICAgICAgICAucnRsICYge1xuICAgICAgICAgIG1hcmdpbi1sZWZ0OiAwO1xuICAgICAgICAgIG1hcmdpbi1yaWdodDogMC4yNXJlbTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5wLWlucHV0Z3JvdXAge1xuICAgICAgLnAtaW5wdXRncm91cC1hZGRvbiB7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMTAwKTtcbiAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1zdXJmYWNlLTMwMCk7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLXNlY29uZGFyeSk7XG4gICAgICAgIG1pbi13aWR0aDogM3JlbTtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG5cbiAgICAgICAgJi5jdXJzb3ItcG9pbnRlciB7XG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG5cbiAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtMjAwKTtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaW5wdXQge1xuICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXN1cmZhY2UtMzAwKTtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcblxuICAgICAgICAmOmZvY3VzIHtcbiAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKHZhcigtLXByaW1hcnktY29sb3ItcmdiKSwgMC4yKTtcbiAgICAgICAgfVxuXG4gICAgICAgICYubmctaW52YWxpZC5uZy10b3VjaGVkIHtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICNlZjQ0NDQ7XG4gICAgICAgICAgXG4gICAgICAgICAgJjpmb2N1cyB7XG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAwLjJyZW0gcmdiYSgyMzksIDY4LCA2OCwgMC4yKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICAucC1lcnJvciB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIG1hcmdpbi10b3A6IDAuMjVyZW07XG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICB9XG4gIH1cblxuICAuZmllbGQtY2hlY2tib3gge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICAuY2hlY2tib3gtbGFiZWwge1xuICAgICAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xuICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICAgLnJ0bCAmIHtcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDA7XG4gICAgICAgIG1hcmdpbi1yaWdodDogMC41cmVtO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5hdHRlbXB0cy13YXJuaW5nIHtcbiAgICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgICA6Om5nLWRlZXAgLnAtbWVzc2FnZSB7XG4gICAgICBtYXJnaW46IDA7XG4gICAgfVxuICB9XG5cbiAgLmxvZ2luLWJ1dHRvbiB7XG4gICAgaGVpZ2h0OiAzcmVtO1xuICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG5cbiAgICAmOm5vdCg6ZGlzYWJsZWQpOmhvdmVyIHtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgMTBweCAyNXB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKTtcbiAgICB9XG5cbiAgICAmOmRpc2FibGVkIHtcbiAgICAgIG9wYWNpdHk6IDAuNjtcbiAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XG4gICAgfVxuXG4gICAgJjo6YmVmb3JlIHtcbiAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgdG9wOiAwO1xuICAgICAgbGVmdDogLTEwMCU7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgdHJhbnNwYXJlbnQsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSwgdHJhbnNwYXJlbnQpO1xuICAgICAgdHJhbnNpdGlvbjogbGVmdCAwLjVzIGVhc2U7XG4gICAgfVxuXG4gICAgJjpub3QoOmRpc2FibGVkKTpob3Zlcjo6YmVmb3JlIHtcbiAgICAgIGxlZnQ6IDEwMCU7XG4gICAgfVxuICB9XG5cbiAgLmZvcmdvdC1wYXNzd29yZCB7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIG1hcmdpbi10b3A6IDEuNXJlbTtcblxuICAgIC5mb3Jnb3QtcGFzc3dvcmQtbGluayB7XG4gICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICAgICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3ItdGV4dCk7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi5sb2Nrb3V0LXdhcm5pbmcge1xuICBtYXJnaW4tYm90dG9tOiAxLjVyZW07XG5cbiAgOjpuZy1kZWVwIC5wLW1lc3NhZ2Uge1xuICAgIG1hcmdpbjogMDtcbiAgfVxufVxuXG4ubG9naW4tZm9vdGVyIHtcbiAgbWFyZ2luLXRvcDogMnJlbTtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nLXRvcDogMS41cmVtO1xuICBib3JkZXItdG9wOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS0yMDApO1xuXG4gIC5mb290ZXItdGV4dCB7XG4gICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcbiAgfVxuXG4gIC5mb290ZXItbGlua3Mge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDAuNXJlbTtcbiAgICBmbGV4LXdyYXA6IHdyYXA7XG5cbiAgICAuZm9vdGVyLWxpbmsge1xuICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgIGZvbnQtc2l6ZTogMC44cmVtO1xuICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC5mb290ZXItc2VwYXJhdG9yIHtcbiAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLXNlY29uZGFyeSk7XG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICB9XG4gIH1cbn1cblxuLnNlY3VyaXR5LW5vdGljZSB7XG4gIG1hcmdpbi10b3A6IDEuNXJlbTtcblxuICA6Om5nLWRlZXAgLnAtbWVzc2FnZSB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgfVxufVxuXG4ubG9naW4tYmFja2dyb3VuZCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICB6LWluZGV4OiAxO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gIC5iYWNrZ3JvdW5kLXBhdHRlcm4ge1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogMDtcbiAgICByaWdodDogMDtcbiAgICBib3R0b206IDA7XG4gICAgYmFja2dyb3VuZC1pbWFnZTogXG4gICAgICByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDI1JSAyNSUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKSAwJSwgdHJhbnNwYXJlbnQgNTAlKSxcbiAgICAgIHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgNzUlIDc1JSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDAlLCB0cmFuc3BhcmVudCA1MCUpO1xuICAgIGFuaW1hdGlvbjogZmxvYXQgMjBzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgZmxvYXQge1xuICAwJSwgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDBweCkgcm90YXRlKDBkZWcpO1xuICB9XG4gIDUwJSB7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0yMHB4KSByb3RhdGUoMTgwZGVnKTtcbiAgfVxufVxuXG4vLyBSZXNwb25zaXZlIERlc2lnblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5sb2dpbi1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IDFyZW07XG4gIH1cblxuICAubG9naW4tY2FyZCB7XG4gICAgcGFkZGluZzogMnJlbSAxLjVyZW07XG4gIH1cblxuICAubG9naW4taGVhZGVyIHtcbiAgICAubG9naW4tdGl0bGUge1xuICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgfVxuICB9XG5cbiAgLmxhbmd1YWdlLXN3aXRjaGVyIHtcbiAgICBwb3NpdGlvbjogc3RhdGljO1xuICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAubG9naW4tY2FyZCB7XG4gICAgcGFkZGluZzogMS41cmVtIDFyZW07XG4gIH1cblxuICAubG9naW4tZm9ybSB7XG4gICAgLmZpZWxkIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgfVxuXG4gICAgLmxvZ2luLWJ1dHRvbiB7XG4gICAgICBoZWlnaHQ6IDIuNXJlbTtcbiAgICB9XG4gIH1cbn1cblxuLy8gRGFyayB0aGVtZSBzdXBwb3J0XG46aG9zdC1jb250ZXh0KC5kYXJrLXRoZW1lKSB7XG4gIC5sb2dpbi1jYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDMwLCAzMCwgMzAsIDAuOTUpO1xuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xuICB9XG5cbiAgLmxvZ2luLWhlYWRlciB7XG4gICAgLmxvZ2luLXRpdGxlIHtcbiAgICAgIGNvbG9yOiB3aGl0ZTtcbiAgICB9XG4gIH1cbn1cblxuLy8gSGlnaCBjb250cmFzdCBtb2RlXG5AbWVkaWEgKHByZWZlcnMtY29udHJhc3Q6IGhpZ2gpIHtcbiAgLmxvZ2luLWNhcmQge1xuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIGJvcmRlcjogMnB4IHNvbGlkIGJsYWNrO1xuICB9XG5cbiAgLmxvZ2luLWJ1dHRvbiB7XG4gICAgYmFja2dyb3VuZDogYmxhY2s7XG4gICAgY29sb3I6IHdoaXRlO1xuICB9XG59XG5cbi8vIFJUTCBUb2FzdCBzdHlsaW5nXG46Om5nLWRlZXAgLnJ0bC10b2FzdCB7XG4gIC5wLXRvYXN0LW1lc3NhZ2Uge1xuICAgIGRpcmVjdGlvbjogcnRsO1xuICAgIHRleHQtYWxpZ246IHJpZ2h0O1xuXG4gICAgLnAtdG9hc3QtbWVzc2FnZS1jb250ZW50IHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3ctcmV2ZXJzZTtcbiAgICB9XG5cbiAgICAucC10b2FzdC1tZXNzYWdlLXRleHQge1xuICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7XG4gICAgfVxuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Validators", "ReactiveFormsModule", "Subject", "takeUntil", "MessageService", "ButtonModule", "InputTextModule", "PasswordModule", "CheckboxModule", "MessageModule", "ToastModule", "InputGroupModule", "InputGroupAddonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "languageService", "translate", "ɵɵpureFunction1", "_c0", "remainingLockoutTime", "ɵɵtext", "ɵɵtextInterpolate1", "getFieldError", "_c1", "remainingAttempts", "LoginComponent", "constructor", "formBuilder", "authService", "router", "route", "messageService", "loading", "returnUrl", "showPassword", "loginAttempts", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutTime", "isLockedOut", "destroy$", "loginForm", "createLoginForm", "ngOnInit", "snapshot", "queryParams", "authState$", "pipe", "subscribe", "authState", "isAuthenticated", "navigate", "error", "handleLoginError", "checkLockoutStatus", "ngOnDestroy", "next", "complete", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "password", "rememberMe", "onSubmit", "invalid", "markFormGroupTouched", "loginRequest", "value", "trim", "login", "response", "clearLockoutStatus", "add", "severity", "summary", "detail", "name", "user", "firstName", "errorMessage", "status", "handleAccountLockout", "handleRateLimitExceeded", "message", "patchValue", "lockoutEndTime", "Date", "now", "localStorage", "setItem", "toISOString", "toString", "minutes", "life", "startLockoutTimer", "lockoutEndTimeStr", "getItem", "storedAttempts", "parseInt", "checkLockout", "setTimeout", "undefined", "removeItem", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "togglePasswordVisibility", "switchLanguage", "toggleLanguage", "remaining", "Math", "max", "getTime", "floor", "seconds", "padStart", "isFieldInvalid", "fieldName", "field", "dirty", "touched", "errors", "min", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "LanguageService", "i4", "Router", "ActivatedRoute", "i5", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_button_click_3_listener", "ɵɵtemplate", "LoginComponent_div_12_Template", "LoginComponent_Template_form_ngSubmit_13_listener", "LoginComponent_small_23_Template", "LoginComponent_Template_span_click_33_listener", "LoginComponent_small_35_Template", "LoginComponent_div_40_Template", "LoginComponent_Template_a_click_43_listener", "$event", "preventDefault", "ɵɵclassProp", "isArabic", "ɵɵtextInterpolate", "ɵɵclassMap", "i6", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "i7", "ButtonDirective", "i8", "InputText", "i9", "Checkbox", "i10", "UIMessage", "i11", "Toast", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\auth\\login\\login.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { LanguageService } from '../../../core/services/language.service';\nimport { LoginRequest } from '../../../core/models/auth.models';\nimport { MessageService } from 'primeng/api';\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { PasswordModule } from 'primeng/password';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { MessageModule } from 'primeng/message';\nimport { ToastModule } from 'primeng/toast';\nimport { InputGroupModule } from 'primeng/inputgroup';\nimport { InputGroupAddonModule } from 'primeng/inputgroupaddon';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    ButtonModule,\n    InputTextModule,\n    PasswordModule,\n    CheckboxModule,\n    MessageModule,\n    ToastModule,\n    InputGroupModule,\n    InputGroupAddonModule\n  ],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss'],\n  providers: [MessageService]\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  loginForm: FormGroup;\n  loading = false;\n  returnUrl = '/dashboard';\n  showPassword = false;\n  loginAttempts = 0;\n  maxLoginAttempts = 5;\n  lockoutTime = 15; // minutes\n  isLockedOut = false;\n  lockoutEndTime?: Date;\n  \n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    public languageService: LanguageService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private messageService: MessageService\n  ) {\n    this.loginForm = this.createLoginForm();\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    \n    // Check if user is already authenticated\n    this.authService.authState$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(authState => {\n        if (authState.isAuthenticated) {\n          this.router.navigate([this.returnUrl]);\n        }\n        \n        if (authState.error) {\n          this.handleLoginError(authState.error);\n        }\n        \n        this.loading = authState.loading;\n      });\n\n    // Check for lockout status\n    this.checkLockoutStatus();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private createLoginForm(): FormGroup {\n    return this.formBuilder.group({\n      username: ['', [\n        Validators.required,\n        Validators.minLength(3),\n        Validators.maxLength(50)\n      ]],\n      password: ['', [\n        Validators.required,\n        Validators.minLength(6)\n      ]],\n      rememberMe: [false]\n    });\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.invalid || this.loading || this.isLockedOut) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.loading = true;\n    const loginRequest: LoginRequest = {\n      username: this.loginForm.value.username.trim(),\n      password: this.loginForm.value.password,\n      rememberMe: this.loginForm.value.rememberMe\n    };\n\n    this.authService.login(loginRequest)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          this.loginAttempts = 0;\n          this.clearLockoutStatus();\n          \n          this.messageService.add({\n            severity: 'success',\n            summary: this.languageService.translate('auth.login.success'),\n            detail: this.languageService.translate('auth.login.welcomeBack', { \n              name: response.user.firstName \n            })\n          });\n\n          // Navigate to return URL or dashboard\n          this.router.navigate([this.returnUrl]);\n        },\n        error: (error) => {\n          this.handleLoginError(error);\n        }\n      });\n  }\n\n  private handleLoginError(error: any): void {\n    this.loading = false;\n    this.loginAttempts++;\n\n    let errorMessage = this.languageService.translate('auth.login.error.generic');\n    \n    if (error?.status === 401) {\n      errorMessage = this.languageService.translate('auth.login.error.invalidCredentials');\n    } else if (error?.status === 423) {\n      errorMessage = this.languageService.translate('auth.login.error.accountLocked');\n      this.handleAccountLockout();\n    } else if (error?.status === 429) {\n      errorMessage = this.languageService.translate('auth.login.error.tooManyAttempts');\n      this.handleRateLimitExceeded();\n    } else if (error?.message) {\n      errorMessage = error.message;\n    }\n\n    this.messageService.add({\n      severity: 'error',\n      summary: this.languageService.translate('auth.login.error.title'),\n      detail: errorMessage\n    });\n\n    // Check if we should lock out the user\n    if (this.loginAttempts >= this.maxLoginAttempts) {\n      this.handleAccountLockout();\n    }\n\n    // Clear password field on error\n    this.loginForm.patchValue({ password: '' });\n  }\n\n  private handleAccountLockout(): void {\n    this.isLockedOut = true;\n    this.lockoutEndTime = new Date(Date.now() + this.lockoutTime * 60 * 1000);\n    \n    // Store lockout info in localStorage\n    localStorage.setItem('lockoutEndTime', this.lockoutEndTime.toISOString());\n    localStorage.setItem('loginAttempts', this.loginAttempts.toString());\n\n    this.messageService.add({\n      severity: 'warn',\n      summary: this.languageService.translate('auth.login.lockout.title'),\n      detail: this.languageService.translate('auth.login.lockout.message', { \n        minutes: this.lockoutTime \n      }),\n      life: 10000\n    });\n\n    // Start countdown timer\n    this.startLockoutTimer();\n  }\n\n  private handleRateLimitExceeded(): void {\n    this.messageService.add({\n      severity: 'warn',\n      summary: this.languageService.translate('auth.login.rateLimit.title'),\n      detail: this.languageService.translate('auth.login.rateLimit.message'),\n      life: 8000\n    });\n  }\n\n  private checkLockoutStatus(): void {\n    const lockoutEndTimeStr = localStorage.getItem('lockoutEndTime');\n    const storedAttempts = localStorage.getItem('loginAttempts');\n\n    if (lockoutEndTimeStr && storedAttempts) {\n      const lockoutEndTime = new Date(lockoutEndTimeStr);\n      const now = new Date();\n\n      if (now < lockoutEndTime) {\n        this.isLockedOut = true;\n        this.lockoutEndTime = lockoutEndTime;\n        this.loginAttempts = parseInt(storedAttempts, 10);\n        this.startLockoutTimer();\n      } else {\n        this.clearLockoutStatus();\n      }\n    }\n  }\n\n  private startLockoutTimer(): void {\n    if (!this.lockoutEndTime) return;\n\n    const checkLockout = () => {\n      const now = new Date();\n      if (this.lockoutEndTime && now >= this.lockoutEndTime) {\n        this.clearLockoutStatus();\n        this.messageService.add({\n          severity: 'info',\n          summary: this.languageService.translate('auth.login.lockout.expired'),\n          detail: this.languageService.translate('auth.login.lockout.canTryAgain')\n        });\n      } else {\n        setTimeout(checkLockout, 1000);\n      }\n    };\n\n    setTimeout(checkLockout, 1000);\n  }\n\n  private clearLockoutStatus(): void {\n    this.isLockedOut = false;\n    this.lockoutEndTime = undefined;\n    this.loginAttempts = 0;\n    localStorage.removeItem('lockoutEndTime');\n    localStorage.removeItem('loginAttempts');\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  switchLanguage(): void {\n    this.languageService.toggleLanguage();\n  }\n\n  // Getter methods for template\n  get username() { return this.loginForm.get('username'); }\n  get password() { return this.loginForm.get('password'); }\n  get rememberMe() { return this.loginForm.get('rememberMe'); }\n\n  get remainingLockoutTime(): string {\n    if (!this.lockoutEndTime) return '';\n    \n    const now = new Date();\n    const remaining = Math.max(0, this.lockoutEndTime.getTime() - now.getTime());\n    const minutes = Math.floor(remaining / 60000);\n    const seconds = Math.floor((remaining % 60000) / 1000);\n    \n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  get remainingAttempts(): number {\n    return Math.max(0, this.maxLoginAttempts - this.loginAttempts);\n  }\n\n  // Form validation helpers\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.loginForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    if (!field || !field.errors) return '';\n\n    const errors = field.errors;\n    \n    if (errors['required']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.required`);\n    }\n    if (errors['minlength']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.minLength`, {\n        min: errors['minlength'].requiredLength\n      });\n    }\n    if (errors['maxlength']) {\n      return this.languageService.translate(`auth.login.validation.${fieldName}.maxLength`, {\n        max: errors['maxlength'].requiredLength\n      });\n    }\n\n    return this.languageService.translate('auth.login.validation.invalid');\n  }\n}\n", "<div class=\"login-container\" [class.rtl]=\"languageService.isArabic()\">\n  <div class=\"login-wrapper\">\n    <!-- Language Switcher -->\n    <div class=\"language-switcher\">\n      <button \n        pButton \n        type=\"button\"\n        [label]=\"languageService.isArabic() ? 'English' : 'العربية'\"\n        class=\"p-button-text p-button-sm\"\n        (click)=\"switchLanguage()\">\n      </button>\n    </div>\n\n    <!-- Login Card -->\n    <div class=\"login-card\">\n      <!-- Header -->\n      <div class=\"login-header\">\n        <div class=\"logo\">\n          <i class=\"pi pi-box text-4xl text-primary\"></i>\n        </div>\n        <h1 class=\"login-title\">{{ languageService.translate('auth.login.title') }}</h1>\n        <p class=\"login-subtitle\">{{ languageService.translate('auth.login.subtitle') }}</p>\n      </div>\n\n      <!-- Lockout Warning -->\n      <div *ngIf=\"isLockedOut\" class=\"lockout-warning\">\n        <p-message\n          severity=\"warn\"\n          [text]=\"languageService.translate('auth.login.lockout.active', { time: remainingLockoutTime })\">\n        </p-message>\n      </div>\n\n      <!-- Login Form -->\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n        <!-- Username Field -->\n        <div class=\"field\">\n          <label for=\"username\" class=\"field-label\">\n            {{ languageService.translate('auth.login.username') }}\n            <span class=\"required-asterisk\">*</span>\n          </label>\n          <div class=\"p-inputgroup\">\n            <span class=\"p-inputgroup-addon\">\n              <i class=\"pi pi-user\"></i>\n            </span>\n            <input\n              id=\"username\"\n              type=\"text\"\n              pInputText\n              formControlName=\"username\"\n              [placeholder]=\"languageService.translate('auth.login.usernamePlaceholder')\"\n              [class.ng-invalid]=\"isFieldInvalid('username')\"\n              [disabled]=\"loading || isLockedOut\"\n              autocomplete=\"username\">\n          </div>\n          <small \n            *ngIf=\"isFieldInvalid('username')\" \n            class=\"p-error\">\n            {{ getFieldError('username') }}\n          </small>\n        </div>\n\n        <!-- Password Field -->\n        <div class=\"field\">\n          <label for=\"password\" class=\"field-label\">\n            {{ languageService.translate('auth.login.password') }}\n            <span class=\"required-asterisk\">*</span>\n          </label>\n          <div class=\"p-inputgroup\">\n            <span class=\"p-inputgroup-addon\">\n              <i class=\"pi pi-lock\"></i>\n            </span>\n            <input\n              id=\"password\"\n              [type]=\"showPassword ? 'text' : 'password'\"\n              pInputText\n              formControlName=\"password\"\n              [placeholder]=\"languageService.translate('auth.login.passwordPlaceholder')\"\n              [class.ng-invalid]=\"isFieldInvalid('password')\"\n              [disabled]=\"loading || isLockedOut\"\n              autocomplete=\"current-password\">\n            <span class=\"p-inputgroup-addon cursor-pointer\" (click)=\"togglePasswordVisibility()\">\n              <i [class]=\"showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'\"></i>\n            </span>\n          </div>\n          <small \n            *ngIf=\"isFieldInvalid('password')\" \n            class=\"p-error\">\n            {{ getFieldError('password') }}\n          </small>\n        </div>\n\n        <!-- Remember Me -->\n        <div class=\"field-checkbox\">\n          <p-checkbox\n            formControlName=\"rememberMe\"\n            [binary]=\"true\"\n            inputId=\"rememberMe\"\n            [disabled]=\"loading || isLockedOut\">\n          </p-checkbox>\n          <label for=\"rememberMe\" class=\"checkbox-label\">\n            {{ languageService.translate('auth.login.rememberMe') }}\n          </label>\n        </div>\n\n        <!-- Login Attempts Warning -->\n        <div *ngIf=\"loginAttempts > 0 && !isLockedOut\" class=\"attempts-warning\">\n          <p-message\n            severity=\"warn\"\n            [text]=\"languageService.translate('auth.login.attemptsRemaining', { attempts: remainingAttempts })\">\n          </p-message>\n        </div>\n\n        <!-- Submit Button -->\n        <button\n          type=\"submit\"\n          pButton\n          [label]=\"languageService.translate('auth.login.signIn')\"\n          [loading]=\"loading\"\n          [disabled]=\"loginForm.invalid || isLockedOut\"\n          class=\"login-button w-full\">\n        </button>\n\n        <!-- Forgot Password Link -->\n        <div class=\"forgot-password\">\n          <a \n            href=\"#\" \n            class=\"forgot-password-link\"\n            (click)=\"$event.preventDefault()\">\n            {{ languageService.translate('auth.login.forgotPassword') }}\n          </a>\n        </div>\n      </form>\n\n      <!-- Footer -->\n      <div class=\"login-footer\">\n        <p class=\"footer-text\">\n          {{ languageService.translate('auth.login.footer.copyright') }}\n        </p>\n        <div class=\"footer-links\">\n          <a href=\"#\" class=\"footer-link\">{{ languageService.translate('auth.login.footer.privacy') }}</a>\n          <span class=\"footer-separator\">•</span>\n          <a href=\"#\" class=\"footer-link\">{{ languageService.translate('auth.login.footer.terms') }}</a>\n          <span class=\"footer-separator\">•</span>\n          <a href=\"#\" class=\"footer-link\">{{ languageService.translate('auth.login.footer.support') }}</a>\n        </div>\n      </div>\n    </div>\n\n    <!-- Security Notice -->\n    <div class=\"security-notice\">\n      <p-message\n        severity=\"info\"\n        [text]=\"languageService.translate('auth.login.securityNotice')\">\n      </p-message>\n    </div>\n  </div>\n\n  <!-- Background -->\n  <div class=\"login-background\">\n    <div class=\"background-pattern\"></div>\n  </div>\n</div>\n\n<!-- Toast Messages -->\n<p-toast position=\"top-right\" [class]=\"languageService.isArabic() ? 'rtl-toast' : ''\"></p-toast>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AAExF,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAIzC,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;;;;ICSzDC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,oBAGY;IACdF,EAAA,CAAAG,YAAA,EAAM;;;;IAFFH,EAAA,CAAAI,SAAA,EAA+F;IAA/FJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAC,eAAA,CAAAC,SAAA,8BAAAR,EAAA,CAAAS,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAK,oBAAA,GAA+F;;;;;IA0B/FX,EAAA,CAAAC,cAAA,gBAEkB;IAChBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IA0BAd,EAAA,CAAAC,cAAA,gBAEkB;IAChBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAa,kBAAA,MAAAP,MAAA,CAAAQ,aAAA,kBACF;;;;;IAiBFd,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,SAAA,oBAGY;IACdF,EAAA,CAAAG,YAAA,EAAM;;;;IAFFH,EAAA,CAAAI,SAAA,EAAmG;IAAnGJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAC,eAAA,CAAAC,SAAA,iCAAAR,EAAA,CAAAS,eAAA,IAAAM,GAAA,EAAAT,MAAA,CAAAU,iBAAA,GAAmG;;;ADvE/G,OAAM,MAAOC,cAAc;EAazBC,YACUC,WAAwB,EACxBC,WAAwB,EACzBb,eAAgC,EAC/Bc,MAAc,EACdC,KAAqB,EACrBC,cAA8B;IAL9B,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAb,eAAe,GAAfA,eAAe;IACd,KAAAc,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAjBxB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,SAAS,GAAG,YAAY;IACxB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,WAAW,GAAG,EAAE,CAAC,CAAC;IAClB,KAAAC,WAAW,GAAG,KAAK;IAGX,KAAAC,QAAQ,GAAG,IAAI1C,OAAO,EAAQ;IAUpC,IAAI,CAAC2C,SAAS,GAAG,IAAI,CAACC,eAAe,EAAE;EACzC;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,SAAS,GAAG,IAAI,CAACH,KAAK,CAACa,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAE7E;IACA,IAAI,CAAChB,WAAW,CAACiB,UAAU,CACxBC,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACyC,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAACC,SAAS,IAAG;MACrB,IAAIA,SAAS,CAACC,eAAe,EAAE;QAC7B,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;;MAGxC,IAAIe,SAAS,CAACG,KAAK,EAAE;QACnB,IAAI,CAACC,gBAAgB,CAACJ,SAAS,CAACG,KAAK,CAAC;;MAGxC,IAAI,CAACnB,OAAO,GAAGgB,SAAS,CAAChB,OAAO;IAClC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACqB,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,QAAQ,CAACgB,IAAI,EAAE;IACpB,IAAI,CAAChB,QAAQ,CAACiB,QAAQ,EAAE;EAC1B;EAEQf,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACd,WAAW,CAAC8B,KAAK,CAAC;MAC5BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACb/D,UAAU,CAACgE,QAAQ,EACnBhE,UAAU,CAACiE,SAAS,CAAC,CAAC,CAAC,EACvBjE,UAAU,CAACkE,SAAS,CAAC,EAAE,CAAC,CACzB,CAAC;MACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbnE,UAAU,CAACgE,QAAQ,EACnBhE,UAAU,CAACiE,SAAS,CAAC,CAAC,CAAC,CACxB,CAAC;MACFG,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxB,SAAS,CAACyB,OAAO,IAAI,IAAI,CAACjC,OAAO,IAAI,IAAI,CAACM,WAAW,EAAE;MAC9D,IAAI,CAAC4B,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAClC,OAAO,GAAG,IAAI;IACnB,MAAMmC,YAAY,GAAiB;MACjCT,QAAQ,EAAE,IAAI,CAAClB,SAAS,CAAC4B,KAAK,CAACV,QAAQ,CAACW,IAAI,EAAE;MAC9CP,QAAQ,EAAE,IAAI,CAACtB,SAAS,CAAC4B,KAAK,CAACN,QAAQ;MACvCC,UAAU,EAAE,IAAI,CAACvB,SAAS,CAAC4B,KAAK,CAACL;KAClC;IAED,IAAI,CAACnC,WAAW,CAAC0C,KAAK,CAACH,YAAY,CAAC,CACjCrB,IAAI,CAAChD,SAAS,CAAC,IAAI,CAACyC,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC;MACTQ,IAAI,EAAGgB,QAAQ,IAAI;QACjB,IAAI,CAACpC,aAAa,GAAG,CAAC;QACtB,IAAI,CAACqC,kBAAkB,EAAE;QAEzB,IAAI,CAACzC,cAAc,CAAC0C,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACC,SAAS,CAAC,oBAAoB,CAAC;UAC7D4D,MAAM,EAAE,IAAI,CAAC7D,eAAe,CAACC,SAAS,CAAC,wBAAwB,EAAE;YAC/D6D,IAAI,EAAEN,QAAQ,CAACO,IAAI,CAACC;WACrB;SACF,CAAC;QAEF;QACA,IAAI,CAAClD,MAAM,CAACqB,QAAQ,CAAC,CAAC,IAAI,CAACjB,SAAS,CAAC,CAAC;MACxC,CAAC;MACDkB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAAC;MAC9B;KACD,CAAC;EACN;EAEQC,gBAAgBA,CAACD,KAAU;IACjC,IAAI,CAACnB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACG,aAAa,EAAE;IAEpB,IAAI6C,YAAY,GAAG,IAAI,CAACjE,eAAe,CAACC,SAAS,CAAC,0BAA0B,CAAC;IAE7E,IAAImC,KAAK,EAAE8B,MAAM,KAAK,GAAG,EAAE;MACzBD,YAAY,GAAG,IAAI,CAACjE,eAAe,CAACC,SAAS,CAAC,qCAAqC,CAAC;KACrF,MAAM,IAAImC,KAAK,EAAE8B,MAAM,KAAK,GAAG,EAAE;MAChCD,YAAY,GAAG,IAAI,CAACjE,eAAe,CAACC,SAAS,CAAC,gCAAgC,CAAC;MAC/E,IAAI,CAACkE,oBAAoB,EAAE;KAC5B,MAAM,IAAI/B,KAAK,EAAE8B,MAAM,KAAK,GAAG,EAAE;MAChCD,YAAY,GAAG,IAAI,CAACjE,eAAe,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACjF,IAAI,CAACmE,uBAAuB,EAAE;KAC/B,MAAM,IAAIhC,KAAK,EAAEiC,OAAO,EAAE;MACzBJ,YAAY,GAAG7B,KAAK,CAACiC,OAAO;;IAG9B,IAAI,CAACrD,cAAc,CAAC0C,GAAG,CAAC;MACtBC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACjE4D,MAAM,EAAEI;KACT,CAAC;IAEF;IACA,IAAI,IAAI,CAAC7C,aAAa,IAAI,IAAI,CAACC,gBAAgB,EAAE;MAC/C,IAAI,CAAC8C,oBAAoB,EAAE;;IAG7B;IACA,IAAI,CAAC1C,SAAS,CAAC6C,UAAU,CAAC;MAAEvB,QAAQ,EAAE;IAAE,CAAE,CAAC;EAC7C;EAEQoB,oBAAoBA,CAAA;IAC1B,IAAI,CAAC5C,WAAW,GAAG,IAAI;IACvB,IAAI,CAACgD,cAAc,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACnD,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC;IAEzE;IACAoD,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAACJ,cAAc,CAACK,WAAW,EAAE,CAAC;IACzEF,YAAY,CAACC,OAAO,CAAC,eAAe,EAAE,IAAI,CAACvD,aAAa,CAACyD,QAAQ,EAAE,CAAC;IAEpE,IAAI,CAAC7D,cAAc,CAAC0C,GAAG,CAAC;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACC,SAAS,CAAC,0BAA0B,CAAC;MACnE4D,MAAM,EAAE,IAAI,CAAC7D,eAAe,CAACC,SAAS,CAAC,4BAA4B,EAAE;QACnE6E,OAAO,EAAE,IAAI,CAACxD;OACf,CAAC;MACFyD,IAAI,EAAE;KACP,CAAC;IAEF;IACA,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEQZ,uBAAuBA,CAAA;IAC7B,IAAI,CAACpD,cAAc,CAAC0C,GAAG,CAAC;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACC,SAAS,CAAC,4BAA4B,CAAC;MACrE4D,MAAM,EAAE,IAAI,CAAC7D,eAAe,CAACC,SAAS,CAAC,8BAA8B,CAAC;MACtE8E,IAAI,EAAE;KACP,CAAC;EACJ;EAEQzC,kBAAkBA,CAAA;IACxB,MAAM2C,iBAAiB,GAAGP,YAAY,CAACQ,OAAO,CAAC,gBAAgB,CAAC;IAChE,MAAMC,cAAc,GAAGT,YAAY,CAACQ,OAAO,CAAC,eAAe,CAAC;IAE5D,IAAID,iBAAiB,IAAIE,cAAc,EAAE;MACvC,MAAMZ,cAAc,GAAG,IAAIC,IAAI,CAACS,iBAAiB,CAAC;MAClD,MAAMR,GAAG,GAAG,IAAID,IAAI,EAAE;MAEtB,IAAIC,GAAG,GAAGF,cAAc,EAAE;QACxB,IAAI,CAAChD,WAAW,GAAG,IAAI;QACvB,IAAI,CAACgD,cAAc,GAAGA,cAAc;QACpC,IAAI,CAACnD,aAAa,GAAGgE,QAAQ,CAACD,cAAc,EAAE,EAAE,CAAC;QACjD,IAAI,CAACH,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAACvB,kBAAkB,EAAE;;;EAG/B;EAEQuB,iBAAiBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACT,cAAc,EAAE;IAE1B,MAAMc,YAAY,GAAGA,CAAA,KAAK;MACxB,MAAMZ,GAAG,GAAG,IAAID,IAAI,EAAE;MACtB,IAAI,IAAI,CAACD,cAAc,IAAIE,GAAG,IAAI,IAAI,CAACF,cAAc,EAAE;QACrD,IAAI,CAACd,kBAAkB,EAAE;QACzB,IAAI,CAACzC,cAAc,CAAC0C,GAAG,CAAC;UACtBC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,IAAI,CAAC5D,eAAe,CAACC,SAAS,CAAC,4BAA4B,CAAC;UACrE4D,MAAM,EAAE,IAAI,CAAC7D,eAAe,CAACC,SAAS,CAAC,gCAAgC;SACxE,CAAC;OACH,MAAM;QACLqF,UAAU,CAACD,YAAY,EAAE,IAAI,CAAC;;IAElC,CAAC;IAEDC,UAAU,CAACD,YAAY,EAAE,IAAI,CAAC;EAChC;EAEQ5B,kBAAkBA,CAAA;IACxB,IAAI,CAAClC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACgD,cAAc,GAAGgB,SAAS;IAC/B,IAAI,CAACnE,aAAa,GAAG,CAAC;IACtBsD,YAAY,CAACc,UAAU,CAAC,gBAAgB,CAAC;IACzCd,YAAY,CAACc,UAAU,CAAC,eAAe,CAAC;EAC1C;EAEQrC,oBAAoBA,CAAA;IAC1BsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,SAAS,CAACkE,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACrE,SAAS,CAACsE,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,wBAAwBA,CAAA;IACtB,IAAI,CAAC9E,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA+E,cAAcA,CAAA;IACZ,IAAI,CAAClG,eAAe,CAACmG,cAAc,EAAE;EACvC;EAEA;EACA,IAAIxD,QAAQA,CAAA;IAAK,OAAO,IAAI,CAAClB,SAAS,CAACsE,GAAG,CAAC,UAAU,CAAC;EAAE;EACxD,IAAIhD,QAAQA,CAAA;IAAK,OAAO,IAAI,CAACtB,SAAS,CAACsE,GAAG,CAAC,UAAU,CAAC;EAAE;EACxD,IAAI/C,UAAUA,CAAA;IAAK,OAAO,IAAI,CAACvB,SAAS,CAACsE,GAAG,CAAC,YAAY,CAAC;EAAE;EAE5D,IAAI3F,oBAAoBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACmE,cAAc,EAAE,OAAO,EAAE;IAEnC,MAAME,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM4B,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/B,cAAc,CAACgC,OAAO,EAAE,GAAG9B,GAAG,CAAC8B,OAAO,EAAE,CAAC;IAC5E,MAAMzB,OAAO,GAAGuB,IAAI,CAACG,KAAK,CAACJ,SAAS,GAAG,KAAK,CAAC;IAC7C,MAAMK,OAAO,GAAGJ,IAAI,CAACG,KAAK,CAAEJ,SAAS,GAAG,KAAK,GAAI,IAAI,CAAC;IAEtD,OAAO,GAAGtB,OAAO,IAAI2B,OAAO,CAAC5B,QAAQ,EAAE,CAAC6B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA,IAAIjG,iBAAiBA,CAAA;IACnB,OAAO4F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjF,gBAAgB,GAAG,IAAI,CAACD,aAAa,CAAC;EAChE;EAEA;EACAuF,cAAcA,CAACC,SAAiB;IAC9B,MAAMC,KAAK,GAAG,IAAI,CAACpF,SAAS,CAACsE,GAAG,CAACa,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEC,KAAK,IAAIA,KAAK,CAAC3D,OAAO,KAAK2D,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACrE;EAEAxG,aAAaA,CAACqG,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACpF,SAAS,CAACsE,GAAG,CAACa,SAAS,CAAC;IAC3C,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACG,MAAM,EAAE,OAAO,EAAE;IAEtC,MAAMA,MAAM,GAAGH,KAAK,CAACG,MAAM;IAE3B,IAAIA,MAAM,CAAC,UAAU,CAAC,EAAE;MACtB,OAAO,IAAI,CAAChH,eAAe,CAACC,SAAS,CAAC,yBAAyB2G,SAAS,WAAW,CAAC;;IAEtF,IAAII,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,OAAO,IAAI,CAAChH,eAAe,CAACC,SAAS,CAAC,yBAAyB2G,SAAS,YAAY,EAAE;QACpFK,GAAG,EAAED,MAAM,CAAC,WAAW,CAAC,CAACE;OAC1B,CAAC;;IAEJ,IAAIF,MAAM,CAAC,WAAW,CAAC,EAAE;MACvB,OAAO,IAAI,CAAChH,eAAe,CAACC,SAAS,CAAC,yBAAyB2G,SAAS,YAAY,EAAE;QACpFN,GAAG,EAAEU,MAAM,CAAC,WAAW,CAAC,CAACE;OAC1B,CAAC;;IAGJ,OAAO,IAAI,CAAClH,eAAe,CAACC,SAAS,CAAC,+BAA+B,CAAC;EACxE;;;uBApRWS,cAAc,EAAAjB,EAAA,CAAA0H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5H,EAAA,CAAA0H,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAA0H,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAhI,EAAA,CAAA0H,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAlI,EAAA,CAAA0H,iBAAA,CAAAO,EAAA,CAAAE,cAAA,GAAAnI,EAAA,CAAA0H,iBAAA,CAAAU,EAAA,CAAA7I,cAAA;IAAA;EAAA;;;YAAd0B,cAAc;MAAAoH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvI,EAAA,CAAAwI,kBAAA,CAFd,CAACjJ,cAAc,CAAC,GAAAS,EAAA,CAAAyI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BvB/I,EAJN,CAAAC,cAAA,aAAsE,aACzC,aAEM,gBAMA;UAA3BD,EAAA,CAAAiJ,UAAA,mBAAAC,gDAAA;YAAA,OAASF,GAAA,CAAAvC,cAAA,EAAgB;UAAA,EAAC;UAE9BzG,EADE,CAAAG,YAAA,EAAS,EACL;UAMFH,EAHJ,CAAAC,cAAA,aAAwB,aAEI,aACN;UAChBD,EAAA,CAAAE,SAAA,WAA+C;UACjDF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAY,MAAA,GAAmD;UAAAZ,EAAA,CAAAG,YAAA,EAAK;UAChFH,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAY,MAAA,IAAsD;UAClFZ,EADkF,CAAAG,YAAA,EAAI,EAChF;UAGNH,EAAA,CAAAmJ,UAAA,KAAAC,8BAAA,kBAAiD;UAQjDpJ,EAAA,CAAAC,cAAA,gBAAyE;UAA3CD,EAAA,CAAAiJ,UAAA,sBAAAI,kDAAA;YAAA,OAAYL,GAAA,CAAAxF,QAAA,EAAU;UAAA,EAAC;UAGjDxD,EADF,CAAAC,cAAA,eAAmB,iBACyB;UACxCD,EAAA,CAAAY,MAAA,IACA;UAAAZ,EAAA,CAAAC,cAAA,gBAAgC;UAAAD,EAAA,CAAAY,MAAA,SAAC;UACnCZ,EADmC,CAAAG,YAAA,EAAO,EAClC;UAENH,EADF,CAAAC,cAAA,eAA0B,gBACS;UAC/BD,EAAA,CAAAE,SAAA,aAA0B;UAC5BF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAE,SAAA,iBAQ0B;UAC5BF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAmJ,UAAA,KAAAG,gCAAA,oBAEkB;UAGpBtJ,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,eAAmB,iBACyB;UACxCD,EAAA,CAAAY,MAAA,IACA;UAAAZ,EAAA,CAAAC,cAAA,gBAAgC;UAAAD,EAAA,CAAAY,MAAA,SAAC;UACnCZ,EADmC,CAAAG,YAAA,EAAO,EAClC;UAENH,EADF,CAAAC,cAAA,eAA0B,gBACS;UAC/BD,EAAA,CAAAE,SAAA,aAA0B;UAC5BF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAE,SAAA,iBAQkC;UAClCF,EAAA,CAAAC,cAAA,gBAAqF;UAArCD,EAAA,CAAAiJ,UAAA,mBAAAM,+CAAA;YAAA,OAASP,GAAA,CAAAxC,wBAAA,EAA0B;UAAA,EAAC;UAClFxG,EAAA,CAAAE,SAAA,SAAgE;UAEpEF,EADE,CAAAG,YAAA,EAAO,EACH;UACNH,EAAA,CAAAmJ,UAAA,KAAAK,gCAAA,oBAEkB;UAGpBxJ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAE,SAAA,sBAKa;UACbF,EAAA,CAAAC,cAAA,iBAA+C;UAC7CD,EAAA,CAAAY,MAAA,IACF;UACFZ,EADE,CAAAG,YAAA,EAAQ,EACJ;UAGNH,EAAA,CAAAmJ,UAAA,KAAAM,8BAAA,kBAAwE;UAQxEzJ,EAAA,CAAAE,SAAA,kBAOS;UAIPF,EADF,CAAAC,cAAA,eAA6B,aAIS;UAAlCD,EAAA,CAAAiJ,UAAA,mBAAAS,4CAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,cAAA,EAAuB;UAAA,EAAC;UACjC5J,EAAA,CAAAY,MAAA,IACF;UAEJZ,EAFI,CAAAG,YAAA,EAAI,EACA,EACD;UAILH,EADF,CAAAC,cAAA,eAA0B,aACD;UACrBD,EAAA,CAAAY,MAAA,IACF;UAAAZ,EAAA,CAAAG,YAAA,EAAI;UAEFH,EADF,CAAAC,cAAA,eAA0B,aACQ;UAAAD,EAAA,CAAAY,MAAA,IAA4D;UAAAZ,EAAA,CAAAG,YAAA,EAAI;UAChGH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAY,MAAA,cAAC;UAAAZ,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAY,MAAA,IAA0D;UAAAZ,EAAA,CAAAG,YAAA,EAAI;UAC9FH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAY,MAAA,cAAC;UAAAZ,EAAA,CAAAG,YAAA,EAAO;UACvCH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAY,MAAA,IAA4D;UAGlGZ,EAHkG,CAAAG,YAAA,EAAI,EAC5F,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,SAAA,qBAGY;UAEhBF,EADE,CAAAG,YAAA,EAAM,EACF;UAGNH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAE,SAAA,eAAsC;UAE1CF,EADE,CAAAG,YAAA,EAAM,EACF;UAGNH,EAAA,CAAAE,SAAA,mBAAgG;;;UApKnEF,EAAA,CAAA6J,WAAA,QAAAb,GAAA,CAAAzI,eAAA,CAAAuJ,QAAA,GAAwC;UAO7D9J,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAK,UAAA,UAAA2I,GAAA,CAAAzI,eAAA,CAAAuJ,QAAA,8DAA4D;UAapC9J,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAA+J,iBAAA,CAAAf,GAAA,CAAAzI,eAAA,CAAAC,SAAA,qBAAmD;UACjDR,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAA+J,iBAAA,CAAAf,GAAA,CAAAzI,eAAA,CAAAC,SAAA,wBAAsD;UAI5ER,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAAlH,WAAA,CAAiB;UAQjB9B,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAK,UAAA,cAAA2I,GAAA,CAAAhH,SAAA,CAAuB;UAIvBhC,EAAA,CAAAI,SAAA,GACA;UADAJ,EAAA,CAAAa,kBAAA,MAAAmI,GAAA,CAAAzI,eAAA,CAAAC,SAAA,6BACA;UAYER,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAA6J,WAAA,eAAAb,GAAA,CAAA9B,cAAA,aAA+C;UAC/ClH,EAFA,CAAAK,UAAA,gBAAA2I,GAAA,CAAAzI,eAAA,CAAAC,SAAA,mCAA2E,aAAAwI,GAAA,CAAAxH,OAAA,IAAAwH,GAAA,CAAAlH,WAAA,CAExC;UAIpC9B,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAA9B,cAAA,aAAgC;UASjClH,EAAA,CAAAI,SAAA,GACA;UADAJ,EAAA,CAAAa,kBAAA,MAAAmI,GAAA,CAAAzI,eAAA,CAAAC,SAAA,6BACA;UAYER,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAA6J,WAAA,eAAAb,GAAA,CAAA9B,cAAA,aAA+C;UAC/ClH,EALA,CAAAK,UAAA,SAAA2I,GAAA,CAAAtH,YAAA,uBAA2C,gBAAAsH,GAAA,CAAAzI,eAAA,CAAAC,SAAA,mCAGgC,aAAAwI,GAAA,CAAAxH,OAAA,IAAAwH,GAAA,CAAAlH,WAAA,CAExC;UAGhC9B,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAgK,UAAA,CAAAhB,GAAA,CAAAtH,YAAA,mCAAwD;UAI5D1B,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAA9B,cAAA,aAAgC;UAUjClH,EAAA,CAAAI,SAAA,GAAe;UAEfJ,EAFA,CAAAK,UAAA,gBAAe,aAAA2I,GAAA,CAAAxH,OAAA,IAAAwH,GAAA,CAAAlH,WAAA,CAEoB;UAGnC9B,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAa,kBAAA,MAAAmI,GAAA,CAAAzI,eAAA,CAAAC,SAAA,+BACF;UAIIR,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAArH,aAAA,SAAAqH,GAAA,CAAAlH,WAAA,CAAuC;UAW3C9B,EAAA,CAAAI,SAAA,EAAwD;UAExDJ,EAFA,CAAAK,UAAA,UAAA2I,GAAA,CAAAzI,eAAA,CAAAC,SAAA,sBAAwD,YAAAwI,GAAA,CAAAxH,OAAA,CACrC,aAAAwH,GAAA,CAAAhH,SAAA,CAAAyB,OAAA,IAAAuF,GAAA,CAAAlH,WAAA,CAC0B;UAU3C9B,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAa,kBAAA,MAAAmI,GAAA,CAAAzI,eAAA,CAAAC,SAAA,mCACF;UAOAR,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAa,kBAAA,MAAAmI,GAAA,CAAAzI,eAAA,CAAAC,SAAA,qCACF;UAEkCR,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAA+J,iBAAA,CAAAf,GAAA,CAAAzI,eAAA,CAAAC,SAAA,8BAA4D;UAE5DR,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAA+J,iBAAA,CAAAf,GAAA,CAAAzI,eAAA,CAAAC,SAAA,4BAA0D;UAE1DR,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAA+J,iBAAA,CAAAf,GAAA,CAAAzI,eAAA,CAAAC,SAAA,8BAA4D;UAS9FR,EAAA,CAAAI,SAAA,GAA+D;UAA/DJ,EAAA,CAAAK,UAAA,SAAA2I,GAAA,CAAAzI,eAAA,CAAAC,SAAA,8BAA+D;UAYzCR,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAgK,UAAA,CAAAhB,GAAA,CAAAzI,eAAA,CAAAuJ,QAAA,sBAAuD;;;qBD9IjF5K,YAAY,EAAA+K,EAAA,CAAAC,IAAA,EACZ9K,mBAAmB,EAAAuI,EAAA,CAAAwC,aAAA,EAAAxC,EAAA,CAAAyC,oBAAA,EAAAzC,EAAA,CAAA0C,eAAA,EAAA1C,EAAA,CAAA2C,oBAAA,EAAA3C,EAAA,CAAA4C,kBAAA,EAAA5C,EAAA,CAAA6C,eAAA,EACnBhL,YAAY,EAAAiL,EAAA,CAAAC,eAAA,EACZjL,eAAe,EAAAkL,EAAA,CAAAC,SAAA,EACflL,cAAc,EACdC,cAAc,EAAAkL,EAAA,CAAAC,QAAA,EACdlL,aAAa,EAAAmL,GAAA,CAAAC,SAAA,EACbnL,WAAW,EAAAoL,GAAA,CAAAC,KAAA,EACXpL,gBAAgB,EAChBC,qBAAqB;MAAAoL,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}