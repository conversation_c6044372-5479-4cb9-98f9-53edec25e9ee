using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using WarehouseManagement.Core.Interfaces;

namespace WarehouseManagement.Application.Services;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // AutoMapper
        services.AddAutoMapper(Assembly.GetExecutingAssembly());

        // MediatR
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

        // FluentValidation
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

        // Application Services
        services.AddScoped<IAuthService, AuthService>();
        services.AddScoped<IJwtService, JwtService>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<ISecurityService, SecurityService>();
        services.AddScoped<ICategoryAppService, CategoryService>();
        services.AddScoped<IItemAppService, ItemService>();

        return services;
    }
}
