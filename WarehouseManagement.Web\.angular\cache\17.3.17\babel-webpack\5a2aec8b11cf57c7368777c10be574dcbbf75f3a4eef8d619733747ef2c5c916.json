{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, Input, NgModule } from '@angular/core';\nimport { SharedModule } from 'primeng/api';\n\n/**\n * InputGroup displays text, icon, buttons and other content can be grouped next to an input.\n * @group Components\n */\nconst _c0 = [\"*\"];\nlet InputGroup = /*#__PURE__*/(() => {\n  class InputGroup {\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    static ɵfac = function InputGroup_Factory(t) {\n      return new (t || InputGroup)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: InputGroup,\n      selectors: [[\"p-inputGroup\"]],\n      hostAttrs: [1, \"p-element\", \"p-inputgroup\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\"\n      },\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 3,\n      consts: [[1, \"p-inputgroup\", 3, \"ngClass\", \"ngStyle\"]],\n      template: function InputGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.styleClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"inputgroup\");\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      encapsulation: 2\n    });\n  }\n  return InputGroup;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputGroupModule = /*#__PURE__*/(() => {\n  class InputGroupModule {\n    static ɵfac = function InputGroupModule_Factory(t) {\n      return new (t || InputGroupModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputGroupModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return InputGroupModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputGroup, InputGroupModule };\n//# sourceMappingURL=primeng-inputgroup.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}