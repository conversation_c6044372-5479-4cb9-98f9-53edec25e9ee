{"ast": null, "code": "import _asyncToGenerator from \"G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, interval, takeUntil } from 'rxjs';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ChartModule } from 'primeng/chart';\nimport { CardModule } from 'primeng/card';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { CoreModule } from '../../core/core.module';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../core/services/language.service\";\nimport * as i3 from \"../../core/services/authorization.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../core/directives/has-permission.directive\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/inputswitch\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/calendar\";\nimport * as i14 from \"primeng/chart\";\nconst _c0 = () => ({\n  \"width\": \"100%\"\n});\nconst _c1 = () => ({\n  label: \"All\",\n  value: \"\"\n});\nconst _c2 = () => ({\n  label: \"Login Success\",\n  value: \"LoginSuccess\"\n});\nconst _c3 = () => ({\n  label: \"Login Failure\",\n  value: \"LoginFailure\"\n});\nconst _c4 = () => ({\n  label: \"Logout\",\n  value: \"Logout\"\n});\nconst _c5 = () => ({\n  label: \"Account Locked\",\n  value: \"AccountLocked\"\n});\nconst _c6 = () => ({\n  label: \"Suspicious Activity\",\n  value: \"SuspiciousActivity\"\n});\nconst _c7 = (a0, a1, a2, a3, a4, a5) => [a0, a1, a2, a3, a4, a5];\nconst _c8 = () => ({\n  label: \"Critical\",\n  value: \"Critical\"\n});\nconst _c9 = () => ({\n  label: \"High\",\n  value: \"High\"\n});\nconst _c10 = () => ({\n  label: \"Medium\",\n  value: \"Medium\"\n});\nconst _c11 = () => ({\n  label: \"Low\",\n  value: \"Low\"\n});\nconst _c12 = (a0, a1, a2, a3, a4) => [a0, a1, a2, a3, a4];\nconst _c13 = () => ({\n  position: \"bottom\"\n});\nconst _c14 = a0 => ({\n  legend: a0\n});\nconst _c15 = a0 => ({\n  plugins: a0\n});\nfunction SecurityMonitoringComponent_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.exportData());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.export\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.timestamp\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.eventType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.severity\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.ipAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.actions\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 54);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵelement(15, \"button\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(event_r4.occurredAt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"pi \", ctx_r1.getEventTypeIcon(event_r4.eventType), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", event_r4.eventType, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"severity-badge \", ctx_r1.getSeverityClass(event_r4.severity), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", event_r4.severity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r4.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.viewDetails\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.noEvents\"), \" \");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.timestamp\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.username\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.ipAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.failureReason\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 57);\n    i0.ɵɵelement(7, \"i\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attempt_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(attempt_r5.attemptedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"success\", attempt_r5.isSuccessful)(\"failure\", !attempt_r5.isSuccessful);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"pi-check\", attempt_r5.isSuccessful)(\"pi-times\", !attempt_r5.isSuccessful);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attempt_r5.isSuccessful ? ctx_r1.languageService.translate(\"security.monitoring.success\") : ctx_r1.languageService.translate(\"security.monitoring.failure\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.failureReason || \"-\");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.noAttempts\"), \" \");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h2\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"p-inputSwitch\", 6);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_inputSwitch_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.autoRefresh, $event) || (ctx_r1.autoRefresh = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function SecurityMonitoringComponent_div_0_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAutoRefresh());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SecurityMonitoringComponent_div_0_button_7_Template, 1, 1, \"button\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"div\", 11)(11, \"div\", 12);\n    i0.ɵɵelement(12, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 14);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 11)(19, \"div\", 16);\n    i0.ɵɵelement(20, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 18);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 10)(26, \"div\", 11)(27, \"div\", 19);\n    i0.ɵɵelement(28, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 15);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 10)(34, \"div\", 11)(35, \"div\", 22);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 24);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 15);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 10)(42, \"div\", 11)(43, \"div\", 25);\n    i0.ɵɵelement(44, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 27);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 15);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 10)(50, \"div\", 11)(51, \"div\", 28);\n    i0.ɵɵelement(52, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 30);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 15);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 31)(58, \"h5\");\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 32)(61, \"div\", 33)(62, \"label\", 34);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"p-dropdown\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_64_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.eventTypeFilter, $event) || (ctx_r1.eventTypeFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 33)(66, \"label\", 36);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p-dropdown\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_68_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.severityFilter, $event) || (ctx_r1.severityFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 33)(70, \"label\", 38);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"p-calendar\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_calendar_ngModelChange_72_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dateRangeFilter.start, $event) || (ctx_r1.dateRangeFilter.start = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 40)(74, \"div\", 41)(75, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_75_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(77, \"div\", 9)(78, \"div\", 44)(79, \"div\", 45)(80, \"h5\");\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(82, \"p-chart\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 44)(84, \"div\", 45)(85, \"h5\");\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"p-chart\", 46);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(88, \"p-tabView\", 47);\n    i0.ɵɵlistener(\"onChange\", function SecurityMonitoringComponent_div_0_Template_p_tabView_onChange_88_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTabChange($event));\n    });\n    i0.ɵɵelementStart(89, \"p-tabPanel\", 48)(90, \"p-table\", 49);\n    i0.ɵɵtemplate(91, SecurityMonitoringComponent_div_0_ng_template_91_Template, 13, 6, \"ng-template\", 50)(92, SecurityMonitoringComponent_div_0_ng_template_92_Template, 16, 12, \"ng-template\", 51)(93, SecurityMonitoringComponent_div_0_ng_template_93_Template, 3, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"p-tabPanel\", 48)(95, \"p-table\", 49);\n    i0.ɵɵtemplate(96, SecurityMonitoringComponent_div_0_ng_template_96_Template, 11, 5, \"ng-template\", 50)(97, SecurityMonitoringComponent_div_0_ng_template_97_Template, 13, 13, \"ng-template\", 51)(98, SecurityMonitoringComponent_div_0_ng_template_98_Template, 3, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.title\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.autoRefresh);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.autoRefresh\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"loading\", ctx_r1.loading)(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.refresh\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"appHasRole\", \"admin\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.totalEvents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.totalEvents\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.criticalEvents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.criticalEvents\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.successfulLogins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.successfulLogins\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.failedLogins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.failedLogins\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.uniqueIPs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.uniqueIPs\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.lockedAccounts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.lockedAccounts\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.filters\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.eventType\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(54, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.eventTypeFilter);\n    i0.ɵɵproperty(\"options\", i0.ɵɵpureFunction6(61, _c7, i0.ɵɵpureFunction0(55, _c1), i0.ɵɵpureFunction0(56, _c2), i0.ɵɵpureFunction0(57, _c3), i0.ɵɵpureFunction0(58, _c4), i0.ɵɵpureFunction0(59, _c5), i0.ɵɵpureFunction0(60, _c6)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.severity\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(68, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.severityFilter);\n    i0.ɵɵproperty(\"options\", i0.ɵɵpureFunction5(74, _c12, i0.ɵɵpureFunction0(69, _c1), i0.ɵɵpureFunction0(70, _c8), i0.ɵɵpureFunction0(71, _c9), i0.ɵɵpureFunction0(72, _c10), i0.ɵɵpureFunction0(73, _c11)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.dateRange\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(80, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.dateRangeFilter.start);\n    i0.ɵɵproperty(\"showTime\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.languageService.translate(\"security.monitoring.apply\"));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.languageService.translate(\"security.monitoring.clear\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.eventsBySeverity\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r1.eventChartData)(\"options\", i0.ɵɵpureFunction1(84, _c15, i0.ɵɵpureFunction1(82, _c14, i0.ɵɵpureFunction0(81, _c13))));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.loginAttempts\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r1.loginChartData)(\"options\", i0.ɵɵpureFunction1(89, _c15, i0.ɵɵpureFunction1(87, _c14, i0.ɵɵpureFunction0(86, _c13))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.languageService.translate(\"security.monitoring.securityEvents\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.securityEvents)(\"paginator\", true)(\"rows\", 20)(\"loading\", ctx_r1.loading)(\"responsive\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.languageService.translate(\"security.monitoring.loginAttempts\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.loginAttempts)(\"paginator\", true)(\"rows\", 20)(\"loading\", ctx_r1.loading)(\"responsive\", true);\n  }\n}\nfunction SecurityMonitoringComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.accessDenied\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.accessDeniedMessage\"), \" \");\n  }\n}\nexport let SecurityMonitoringComponent = /*#__PURE__*/(() => {\n  class SecurityMonitoringComponent {\n    constructor(http, languageService, authorizationService) {\n      this.http = http;\n      this.languageService = languageService;\n      this.authorizationService = authorizationService;\n      this.destroy$ = new Subject();\n      this.API_URL = environment.apiUrl;\n      // Data\n      this.securityEvents = [];\n      this.loginAttempts = [];\n      this.metrics = {\n        totalEvents: 0,\n        criticalEvents: 0,\n        failedLogins: 0,\n        successfulLogins: 0,\n        uniqueIPs: 0,\n        lockedAccounts: 0\n      };\n      // UI State\n      this.loading = false;\n      this.selectedTab = 0;\n      this.refreshInterval = 30000; // 30 seconds\n      this.autoRefresh = true;\n      // Filters\n      this.eventTypeFilter = '';\n      this.severityFilter = '';\n      this.dateRangeFilter = {\n        start: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        end: new Date()\n      };\n    }\n    ngOnInit() {\n      this.loadData();\n      this.setupAutoRefresh();\n      this.initializeCharts();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    setupAutoRefresh() {\n      if (this.autoRefresh) {\n        interval(this.refreshInterval).pipe(takeUntil(this.destroy$)).subscribe(() => {\n          this.loadData();\n        });\n      }\n    }\n    loadData() {\n      this.loading = true;\n      Promise.all([this.loadSecurityEvents(), this.loadLoginAttempts(), this.loadMetrics()]).finally(() => {\n        this.loading = false;\n        this.updateCharts();\n      });\n    }\n    loadSecurityEvents() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const params = new URLSearchParams({\n            page: '1',\n            pageSize: '50',\n            ...(_this.eventTypeFilter && {\n              eventType: _this.eventTypeFilter\n            }),\n            ...(_this.severityFilter && {\n              severity: _this.severityFilter\n            }),\n            fromDate: _this.dateRangeFilter.start.toISOString(),\n            toDate: _this.dateRangeFilter.end.toISOString()\n          });\n          const response = yield _this.http.get(`${_this.API_URL}/security/events?${params}`).toPromise();\n          if (response.success) {\n            _this.securityEvents = response.data || [];\n          }\n        } catch (error) {\n          console.error('Error loading security events:', error);\n        }\n      })();\n    }\n    loadLoginAttempts() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const params = new URLSearchParams({\n            page: '1',\n            pageSize: '50',\n            fromDate: _this2.dateRangeFilter.start.toISOString(),\n            toDate: _this2.dateRangeFilter.end.toISOString()\n          });\n          const response = yield _this2.http.get(`${_this2.API_URL}/security/login-attempts?${params}`).toPromise();\n          if (response.success) {\n            _this2.loginAttempts = response.data || [];\n          }\n        } catch (error) {\n          console.error('Error loading login attempts:', error);\n        }\n      })();\n    }\n    loadMetrics() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        // Calculate metrics from loaded data\n        _this3.metrics = {\n          totalEvents: _this3.securityEvents.length,\n          criticalEvents: _this3.securityEvents.filter(e => e.severity === 'Critical').length,\n          failedLogins: _this3.loginAttempts.filter(a => !a.isSuccessful).length,\n          successfulLogins: _this3.loginAttempts.filter(a => a.isSuccessful).length,\n          uniqueIPs: new Set(_this3.loginAttempts.map(a => a.ipAddress)).size,\n          lockedAccounts: _this3.securityEvents.filter(e => e.eventType === 'AccountLocked').length\n        };\n      })();\n    }\n    initializeCharts() {\n      this.eventChartData = {\n        labels: ['Critical', 'High', 'Medium', 'Low'],\n        datasets: [{\n          data: [0, 0, 0, 0],\n          backgroundColor: ['#ef4444', '#f97316', '#eab308', '#22c55e']\n        }]\n      };\n      this.loginChartData = {\n        labels: ['Successful', 'Failed'],\n        datasets: [{\n          data: [0, 0],\n          backgroundColor: ['#22c55e', '#ef4444']\n        }]\n      };\n    }\n    updateCharts() {\n      // Update event severity chart\n      const severityCounts = {\n        Critical: this.securityEvents.filter(e => e.severity === 'Critical').length,\n        High: this.securityEvents.filter(e => e.severity === 'High').length,\n        Medium: this.securityEvents.filter(e => e.severity === 'Medium').length,\n        Low: this.securityEvents.filter(e => e.severity === 'Low').length\n      };\n      this.eventChartData = {\n        ...this.eventChartData,\n        datasets: [{\n          ...this.eventChartData.datasets[0],\n          data: [severityCounts.Critical, severityCounts.High, severityCounts.Medium, severityCounts.Low]\n        }]\n      };\n      // Update login attempts chart\n      this.loginChartData = {\n        ...this.loginChartData,\n        datasets: [{\n          ...this.loginChartData.datasets[0],\n          data: [this.metrics.successfulLogins, this.metrics.failedLogins]\n        }]\n      };\n    }\n    onTabChange(event) {\n      this.selectedTab = event.index;\n    }\n    onRefreshIntervalChange(interval) {\n      this.refreshInterval = interval * 1000;\n      this.setupAutoRefresh();\n    }\n    toggleAutoRefresh() {\n      this.autoRefresh = !this.autoRefresh;\n      if (this.autoRefresh) {\n        this.setupAutoRefresh();\n      }\n    }\n    applyFilters() {\n      this.loadData();\n    }\n    clearFilters() {\n      this.eventTypeFilter = '';\n      this.severityFilter = '';\n      this.dateRangeFilter = {\n        start: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        end: new Date()\n      };\n      this.loadData();\n    }\n    exportData() {\n      const data = {\n        securityEvents: this.securityEvents,\n        loginAttempts: this.loginAttempts,\n        metrics: this.metrics,\n        exportedAt: new Date().toISOString()\n      };\n      const blob = new Blob([JSON.stringify(data, null, 2)], {\n        type: 'application/json'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;\n      link.click();\n      window.URL.revokeObjectURL(url);\n    }\n    getSeverityClass(severity) {\n      switch (severity.toLowerCase()) {\n        case 'critical':\n          return 'severity-critical';\n        case 'high':\n          return 'severity-high';\n        case 'medium':\n          return 'severity-medium';\n        case 'low':\n          return 'severity-low';\n        default:\n          return '';\n      }\n    }\n    getEventTypeIcon(eventType) {\n      switch (eventType.toLowerCase()) {\n        case 'loginsuccess':\n          return 'pi-check-circle';\n        case 'loginfailure':\n          return 'pi-times-circle';\n        case 'logout':\n          return 'pi-sign-out';\n        case 'accountlocked':\n          return 'pi-lock';\n        case 'suspiciousactivity':\n          return 'pi-exclamation-triangle';\n        case 'permissiondenied':\n          return 'pi-ban';\n        default:\n          return 'pi-info-circle';\n      }\n    }\n    formatDate(date) {\n      const d = new Date(date);\n      return d.toLocaleString(this.languageService.isArabic() ? 'ar-SA' : 'en-US');\n    }\n    // Permission checks\n    canViewSecurityEvents() {\n      return this.authorizationService.hasPermission('audit_logs', 'read');\n    }\n    canExportData() {\n      return this.authorizationService.hasRole('admin');\n    }\n    static {\n      this.ɵfac = function SecurityMonitoringComponent_Factory(t) {\n        return new (t || SecurityMonitoringComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.LanguageService), i0.ɵɵdirectiveInject(i3.AuthorizationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityMonitoringComponent,\n        selectors: [[\"app-security-monitoring\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 2,\n        vars: 2,\n        consts: [[\"class\", \"security-monitoring\", 4, \"appHasPermission\"], [\"class\", \"text-center p-6\", 4, \"appIsAuthenticated\"], [1, \"security-monitoring\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"m-0\"], [1, \"flex\", \"gap-2\"], [3, \"ngModelChange\", \"onChange\", \"ngModel\", \"pTooltip\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-refresh\", 1, \"p-button-outlined\", 3, \"click\", \"loading\", \"pTooltip\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-download\", \"class\", \"p-button-outlined\", 3, \"pTooltip\", \"click\", 4, \"appHasRole\"], [1, \"grid\", \"mb-4\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [1, \"card\", \"text-center\"], [1, \"text-blue-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-shield\"], [1, \"text-2xl\", \"font-bold\", \"text-blue-600\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"text-red-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-exclamation-triangle\"], [1, \"text-2xl\", \"font-bold\", \"text-red-600\"], [1, \"text-green-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-check-circle\"], [1, \"text-2xl\", \"font-bold\", \"text-green-600\"], [1, \"text-orange-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-times-circle\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-600\"], [1, \"text-purple-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-globe\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"text-yellow-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-lock\"], [1, \"text-2xl\", \"font-bold\", \"text-yellow-600\"], [1, \"card\", \"mb-4\"], [1, \"grid\"], [1, \"col-12\", \"md:col-3\"], [\"for\", \"eventType\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"eventType\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"ngModel\", \"options\"], [\"for\", \"severity\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"severity\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"ngModel\", \"options\"], [\"for\", \"dateRange\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"dateRange\", 3, \"ngModelChange\", \"ngModel\", \"showTime\"], [1, \"col-12\", \"md:col-3\", \"flex\", \"align-items-end\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-search\", 1, \"p-button-primary\", \"flex-1\", 3, \"click\", \"label\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times\", 1, \"p-button-outlined\", \"flex-1\", 3, \"click\", \"label\"], [1, \"col-12\", \"md:col-6\"], [1, \"card\"], [\"type\", \"doughnut\", 3, \"data\", \"options\"], [3, \"onChange\"], [3, \"header\"], [3, \"value\", \"paginator\", \"rows\", \"loading\", \"responsive\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-download\", 1, \"p-button-outlined\", 3, \"click\", \"pTooltip\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-eye\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"pTooltip\"], [\"colspan\", \"6\", 1, \"text-center\"], [1, \"status-badge\"], [1, \"pi\"], [\"colspan\", \"5\", 1, \"text-center\"], [1, \"text-center\", \"p-6\"], [1, \"pi\", \"pi-lock\", \"text-6xl\", \"text-gray-400\", \"mb-3\"], [1, \"text-gray-600\"]],\n        template: function SecurityMonitoringComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, SecurityMonitoringComponent_div_0_Template, 99, 91, \"div\", 0)(1, SecurityMonitoringComponent_div_1_Template, 6, 2, \"div\", 1);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"appHasPermission\", \"audit_logs:read\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"appIsAuthenticated\", false);\n          }\n        },\n        dependencies: [CommonModule, FormsModule, i4.NgControlStatus, i4.NgModel, CoreModule, i5.HasPermissionDirective, i5.HasRoleDirective, i5.IsAuthenticatedDirective, ButtonModule, i6.ButtonDirective, i7.PrimeTemplate, InputSwitchModule, i8.InputSwitch, TooltipModule, i9.Tooltip, TableModule, i10.Table, TabViewModule, i11.TabView, i11.TabPanel, DropdownModule, i12.Dropdown, CalendarModule, i13.Calendar, ChartModule, i14.UIChart, CardModule, ProgressSpinnerModule],\n        styles: [\".security-monitoring[_ngcontent-%COMP%]{padding:1rem}.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background:#fff;border-radius:8px;box-shadow:0 2px 4px #0000001a;padding:1.5rem;margin-bottom:1rem;border:1px solid #e5e7eb}.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin-top:0;margin-bottom:1rem;color:#374151;font-weight:600}.security-monitoring[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%]{padding:.25rem .75rem;border-radius:9999px;font-size:.75rem;font-weight:600;text-transform:uppercase;letter-spacing:.05em}.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-critical[_ngcontent-%COMP%]{background-color:#fee2e2;color:#dc2626;border:1px solid #fecaca}.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-high[_ngcontent-%COMP%]{background-color:#fed7aa;color:#ea580c;border:1px solid #fdba74}.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-medium[_ngcontent-%COMP%]{background-color:#fef3c7;color:#d97706;border:1px solid #fde68a}.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-low[_ngcontent-%COMP%]{background-color:#dcfce7;color:#16a34a;border:1px solid #bbf7d0}.security-monitoring[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.25rem;padding:.25rem .75rem;border-radius:9999px;font-size:.75rem;font-weight:600}.security-monitoring[_ngcontent-%COMP%]   .status-badge.success[_ngcontent-%COMP%]{background-color:#dcfce7;color:#16a34a;border:1px solid #bbf7d0}.security-monitoring[_ngcontent-%COMP%]   .status-badge.failure[_ngcontent-%COMP%]{background-color:#fee2e2;color:#dc2626;border:1px solid #fecaca}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-3xl[_ngcontent-%COMP%]{margin-bottom:.5rem}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-2xl[_ngcontent-%COMP%]{margin-bottom:.25rem}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%]{opacity:.7}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-header{background:#f9fafb;border:1px solid #e5e7eb;padding:1rem}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-thead>tr>th{background:#f9fafb;color:#374151;font-weight:600;border-bottom:1px solid #e5e7eb;padding:.75rem}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr>td{padding:.75rem;border-bottom:1px solid #f3f4f6}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr:hover{background:#f9fafb}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-emptymessage>td{text-align:center;padding:2rem;color:#6b7280;font-style:italic}.security-monitoring   [_nghost-%COMP%]     .p-paginator{background:#f9fafb;border:1px solid #e5e7eb;border-top:none;padding:.75rem 1rem}.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page{color:#374151;border:1px solid #d1d5db;margin:0 .125rem}.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page.p-highlight{background:#3b82f6;border-color:#3b82f6;color:#fff}.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page:hover:not(.p-highlight){background:#f3f4f6}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav{background:#f9fafb;border-bottom:1px solid #e5e7eb}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link{color:#6b7280;border:none;padding:1rem 1.5rem;font-weight:500}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover{background:#f3f4f6;color:#374151}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{color:#3b82f6;border-bottom:2px solid #3b82f6;background:#fff}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-panels{background:#fff;padding:1.5rem}.security-monitoring   [_nghost-%COMP%]     .p-chart canvas{max-height:300px}.security-monitoring   [_nghost-%COMP%]     .p-dropdown{border:1px solid #d1d5db}.security-monitoring   [_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border-color:#9ca3af}.security-monitoring   [_nghost-%COMP%]     .p-dropdown.p-focus{border-color:#3b82f6;box-shadow:0 0 0 1px #3b82f6}.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext{border:1px solid #d1d5db}.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext:hover{border-color:#9ca3af}.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext:focus{border-color:#3b82f6;box-shadow:0 0 0 1px #3b82f6}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-outlined{border:1px solid #d1d5db;color:#374151}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-outlined:hover{background:#f3f4f6;border-color:#9ca3af}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-primary{background:#3b82f6;border-color:#3b82f6}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-primary:hover{background:#2563eb;border-color:#2563eb}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-rounded{border-radius:50%;width:2.5rem;height:2.5rem}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-text{color:#6b7280}.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-text:hover{background:#f3f4f6;color:#374151}.security-monitoring   [_nghost-%COMP%]     .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider{background:#3b82f6}.security-monitoring   [_nghost-%COMP%]     .p-inputswitch .p-inputswitch-slider{background:#d1d5db}@media (max-width: 768px){.security-monitoring[_ngcontent-%COMP%]{padding:.5rem}.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{padding:1rem}.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .col-12[_ngcontent-%COMP%]{padding:.25rem}.security-monitoring[_ngcontent-%COMP%]   .flex.gap-2[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem}.security-monitoring[_ngcontent-%COMP%]   .flex.gap-2[_ngcontent-%COMP%]   .flex-1[_ngcontent-%COMP%]{width:100%}}.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%]{flex-direction:row-reverse}.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%]{text-align:right}.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .text-right[_ngcontent-%COMP%]{text-align:left}.security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-datatable-thead>tr>th, .security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-datatable-tbody>tr>td{text-align:right}.security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-tabview-nav li{margin-left:0;margin-right:.25rem}@media (prefers-color-scheme: dark){.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background:#1f2937;border-color:#374151;color:#f9fafb}.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#f9fafb}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-header, .security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-thead>tr>th{background:#374151;color:#f9fafb;border-color:#4b5563}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr>td{border-color:#4b5563;color:#f9fafb}.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr:hover{background:#374151}.security-monitoring   [_nghost-%COMP%]     .p-paginator{background:#374151;border-color:#4b5563;color:#f9fafb}.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav{background:#374151;border-color:#4b5563}}\"]\n      });\n    }\n  }\n  return SecurityMonitoringComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}