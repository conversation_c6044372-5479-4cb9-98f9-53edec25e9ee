{"ast": null, "code": "import { PermissionAction, UserRoleType } from '../models/auth.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport class AuthorizationService {\n  // Define resource constants\n  static {\n    this.RESOURCES = {\n      DASHBOARD: 'dashboard',\n      INVENTORY: 'inventory',\n      PRODUCTS: 'products',\n      CATEGORIES: 'categories',\n      SUPPLIERS: 'suppliers',\n      CUSTOMERS: 'customers',\n      ORDERS: 'orders',\n      REPORTS: 'reports',\n      USERS: 'users',\n      ROLES: 'roles',\n      SETTINGS: 'settings',\n      AUDIT_LOGS: 'audit_logs'\n    };\n  }\n  constructor(authService) {\n    this.authService = authService;\n    // Define default role permissions\n    this.DEFAULT_ROLE_PERMISSIONS = {\n      [UserRoleType.ADMIN]: [\n      // Admin has full access to everything\n      {\n        resource: '*',\n        action: '*'\n      }],\n      [UserRoleType.MANAGER]: [\n      // Dashboard access\n      {\n        resource: AuthorizationService.RESOURCES.DASHBOARD,\n        action: PermissionAction.READ\n      },\n      // Inventory management\n      {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.UPDATE\n      }, {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.CREATE\n      },\n      // Product management\n      {\n        resource: AuthorizationService.RESOURCES.PRODUCTS,\n        action: PermissionAction.MANAGE\n      },\n      // Category management\n      {\n        resource: AuthorizationService.RESOURCES.CATEGORIES,\n        action: PermissionAction.MANAGE\n      },\n      // Supplier management\n      {\n        resource: AuthorizationService.RESOURCES.SUPPLIERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.SUPPLIERS,\n        action: PermissionAction.UPDATE\n      },\n      // Customer management\n      {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.UPDATE\n      },\n      // Order management\n      {\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.MANAGE\n      },\n      // Reports access\n      {\n        resource: AuthorizationService.RESOURCES.REPORTS,\n        action: PermissionAction.READ\n      },\n      // User management (limited)\n      {\n        resource: AuthorizationService.RESOURCES.USERS,\n        action: PermissionAction.READ\n      },\n      // Settings access (limited)\n      {\n        resource: AuthorizationService.RESOURCES.SETTINGS,\n        action: PermissionAction.READ\n      }],\n      [UserRoleType.EMPLOYEE]: [\n      // Dashboard access\n      {\n        resource: AuthorizationService.RESOURCES.DASHBOARD,\n        action: PermissionAction.READ\n      },\n      // Inventory access\n      {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.UPDATE\n      },\n      // Product access\n      {\n        resource: AuthorizationService.RESOURCES.PRODUCTS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.PRODUCTS,\n        action: PermissionAction.UPDATE\n      },\n      // Category access\n      {\n        resource: AuthorizationService.RESOURCES.CATEGORIES,\n        action: PermissionAction.READ\n      },\n      // Supplier access\n      {\n        resource: AuthorizationService.RESOURCES.SUPPLIERS,\n        action: PermissionAction.READ\n      },\n      // Customer access\n      {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.CREATE\n      }, {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.UPDATE\n      },\n      // Order access\n      {\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.CREATE\n      }, {\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.UPDATE\n      }],\n      [UserRoleType.VIEWER]: [\n      // Dashboard access\n      {\n        resource: AuthorizationService.RESOURCES.DASHBOARD,\n        action: PermissionAction.READ\n      },\n      // Read-only access to most resources\n      {\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.PRODUCTS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.CATEGORIES,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.SUPPLIERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.READ\n      }, {\n        resource: AuthorizationService.RESOURCES.REPORTS,\n        action: PermissionAction.READ\n      }]\n    };\n  }\n  /**\n   * Check if current user has permission for a specific resource and action\n   */\n  hasPermission(resource, action) {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return false;\n    }\n    return this.userHasPermission(user, resource, action);\n  }\n  /**\n   * Check if user has permission for a specific resource and action\n   */\n  userHasPermission(user, resource, action) {\n    // Admin role has all permissions\n    if (this.userHasRole(user, UserRoleType.ADMIN)) {\n      return true;\n    }\n    // Check explicit permissions first\n    const hasExplicitPermission = user.permissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n    if (hasExplicitPermission) {\n      return true;\n    }\n    // Check role-based permissions\n    return user.roles.some(role => this.roleHasPermission(role, resource, action));\n  }\n  /**\n   * Check if current user has specific role\n   */\n  hasRole(roleName) {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return false;\n    }\n    return this.userHasRole(user, roleName);\n  }\n  /**\n   * Check if user has specific role\n   */\n  userHasRole(user, roleName) {\n    return user.roles.some(role => role.name === roleName);\n  }\n  /**\n   * Check if role has permission for a specific resource and action\n   */\n  roleHasPermission(role, resource, action) {\n    // Check explicit role permissions\n    const hasExplicitPermission = role.permissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n    if (hasExplicitPermission) {\n      return true;\n    }\n    // Check default role permissions\n    const roleType = role.name;\n    const defaultPermissions = this.DEFAULT_ROLE_PERMISSIONS[roleType];\n    if (!defaultPermissions) {\n      return false;\n    }\n    return defaultPermissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n  }\n  /**\n   * Check multiple permissions (AND logic)\n   */\n  hasAllPermissions(permissions) {\n    return permissions.every(permission => this.hasPermission(permission.resource, permission.action));\n  }\n  /**\n   * Check multiple permissions (OR logic)\n   */\n  hasAnyPermission(permissions) {\n    return permissions.some(permission => this.hasPermission(permission.resource, permission.action));\n  }\n  /**\n   * Get all permissions for current user\n   */\n  getCurrentUserPermissions() {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return {};\n    }\n    return this.getUserPermissions(user);\n  }\n  /**\n   * Get all permissions for a specific user\n   */\n  getUserPermissions(user) {\n    const permissions = {};\n    const resources = Object.values(AuthorizationService.RESOURCES);\n    const actions = Object.values(PermissionAction);\n    resources.forEach(resource => {\n      permissions[resource] = {};\n      actions.forEach(action => {\n        permissions[resource][action] = this.userHasPermission(user, resource, action);\n      });\n    });\n    return permissions;\n  }\n  /**\n   * Check if current user can access a specific route\n   */\n  canAccessRoute(route) {\n    // Map routes to required permissions\n    const routePermissions = {\n      '/dashboard': [{\n        resource: AuthorizationService.RESOURCES.DASHBOARD,\n        action: PermissionAction.READ\n      }],\n      '/inventory': [{\n        resource: AuthorizationService.RESOURCES.INVENTORY,\n        action: PermissionAction.READ\n      }],\n      '/products': [{\n        resource: AuthorizationService.RESOURCES.PRODUCTS,\n        action: PermissionAction.READ\n      }],\n      '/categories': [{\n        resource: AuthorizationService.RESOURCES.CATEGORIES,\n        action: PermissionAction.READ\n      }],\n      '/suppliers': [{\n        resource: AuthorizationService.RESOURCES.SUPPLIERS,\n        action: PermissionAction.READ\n      }],\n      '/customers': [{\n        resource: AuthorizationService.RESOURCES.CUSTOMERS,\n        action: PermissionAction.READ\n      }],\n      '/orders': [{\n        resource: AuthorizationService.RESOURCES.ORDERS,\n        action: PermissionAction.READ\n      }],\n      '/reports': [{\n        resource: AuthorizationService.RESOURCES.REPORTS,\n        action: PermissionAction.READ\n      }],\n      '/users': [{\n        resource: AuthorizationService.RESOURCES.USERS,\n        action: PermissionAction.READ\n      }],\n      '/settings': [{\n        resource: AuthorizationService.RESOURCES.SETTINGS,\n        action: PermissionAction.READ\n      }]\n    };\n    const requiredPermissions = routePermissions[route];\n    if (!requiredPermissions) {\n      return true; // Allow access to routes without specific permissions\n    }\n    return this.hasAllPermissions(requiredPermissions);\n  }\n  /**\n   * Log permission denied event\n   */\n  logPermissionDenied(resource, action, route) {\n    const user = this.authService.getCurrentUserSync();\n    const description = `Permission denied for ${action} on ${resource}${route ? ` (route: ${route})` : ''}`;\n    console.warn(`Authorization: ${description}`, {\n      user: user?.username,\n      resource,\n      action,\n      route\n    });\n    // This would typically send to a logging service\n    // For now, we'll just log to console\n  }\n  /**\n   * Get user-friendly permission description\n   */\n  getPermissionDescription(resource, action) {\n    const actionDescriptions = {\n      [PermissionAction.CREATE]: 'create',\n      [PermissionAction.READ]: 'view',\n      [PermissionAction.UPDATE]: 'edit',\n      [PermissionAction.DELETE]: 'delete',\n      [PermissionAction.MANAGE]: 'manage'\n    };\n    const resourceDescriptions = {\n      [AuthorizationService.RESOURCES.DASHBOARD]: 'Dashboard',\n      [AuthorizationService.RESOURCES.INVENTORY]: 'Inventory',\n      [AuthorizationService.RESOURCES.PRODUCTS]: 'Products',\n      [AuthorizationService.RESOURCES.CATEGORIES]: 'Categories',\n      [AuthorizationService.RESOURCES.SUPPLIERS]: 'Suppliers',\n      [AuthorizationService.RESOURCES.CUSTOMERS]: 'Customers',\n      [AuthorizationService.RESOURCES.ORDERS]: 'Orders',\n      [AuthorizationService.RESOURCES.REPORTS]: 'Reports',\n      [AuthorizationService.RESOURCES.USERS]: 'Users',\n      [AuthorizationService.RESOURCES.ROLES]: 'Roles',\n      [AuthorizationService.RESOURCES.SETTINGS]: 'Settings',\n      [AuthorizationService.RESOURCES.AUDIT_LOGS]: 'Audit Logs'\n    };\n    const actionDesc = actionDescriptions[action] || action;\n    const resourceDesc = resourceDescriptions[resource] || resource;\n    return `${actionDesc} ${resourceDesc}`;\n  }\n  static {\n    this.ɵfac = function AuthorizationService_Factory(t) {\n      return new (t || AuthorizationService)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthorizationService,\n      factory: AuthorizationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["PermissionAction", "UserRoleType", "AuthorizationService", "RESOURCES", "DASHBOARD", "INVENTORY", "PRODUCTS", "CATEGORIES", "SUPPLIERS", "CUSTOMERS", "ORDERS", "REPORTS", "USERS", "ROLES", "SETTINGS", "AUDIT_LOGS", "constructor", "authService", "DEFAULT_ROLE_PERMISSIONS", "ADMIN", "resource", "action", "MANAGER", "READ", "UPDATE", "CREATE", "MANAGE", "EMPLOYEE", "VIEWER", "hasPermission", "user", "getCurrentUserSync", "userHasPermission", "userHasRole", "hasExplicitPermission", "permissions", "some", "permission", "roles", "role", "roleHasPermission", "hasRole", "<PERSON><PERSON><PERSON>", "name", "roleType", "defaultPermissions", "hasAllPermissions", "every", "hasAnyPermission", "getCurrentUserPermissions", "getUserPermissions", "resources", "Object", "values", "actions", "for<PERSON>ach", "canAccessRoute", "route", "routePermissions", "requiredPermissions", "logPermissionDenied", "description", "console", "warn", "username", "getPermissionDescription", "actionDescriptions", "DELETE", "resourceDescriptions", "actionDesc", "resourceDesc", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\services\\authorization.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { AuthService } from './auth.service';\nimport { \n  User, \n  Permission, \n  UserRole, \n  PermissionAction, \n  UserRoleType,\n  SecurityEventType,\n  SecurityEventSeverity \n} from '../models/auth.models';\n\nexport interface ResourcePermissions {\n  [resource: string]: {\n    [action: string]: boolean;\n  };\n}\n\nexport interface PermissionCheck {\n  resource: string;\n  action: PermissionAction | string;\n  requireAll?: boolean; // If true, user must have ALL specified permissions\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthorizationService {\n  \n  // Define resource constants\n  public static readonly RESOURCES = {\n    DASHBOARD: 'dashboard',\n    INVENTORY: 'inventory',\n    PRODUCTS: 'products',\n    CATEGORIES: 'categories',\n    SUPPLIERS: 'suppliers',\n    CUSTOMERS: 'customers',\n    ORDERS: 'orders',\n    REPORTS: 'reports',\n    USERS: 'users',\n    ROLES: 'roles',\n    SETTINGS: 'settings',\n    AUDIT_LOGS: 'audit_logs'\n  } as const;\n\n  // Define default role permissions\n  private readonly DEFAULT_ROLE_PERMISSIONS: Record<UserRoleType, PermissionCheck[]> = {\n    [UserRoleType.ADMIN]: [\n      // Admin has full access to everything\n      { resource: '*', action: '*' }\n    ],\n    [UserRoleType.MANAGER]: [\n      // Dashboard access\n      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },\n      \n      // Inventory management\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.UPDATE },\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.CREATE },\n      \n      // Product management\n      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.MANAGE },\n      \n      // Category management\n      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.MANAGE },\n      \n      // Supplier management\n      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.UPDATE },\n      \n      // Customer management\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.UPDATE },\n      \n      // Order management\n      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.MANAGE },\n      \n      // Reports access\n      { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ },\n      \n      // User management (limited)\n      { resource: AuthorizationService.RESOURCES.USERS, action: PermissionAction.READ },\n      \n      // Settings access (limited)\n      { resource: AuthorizationService.RESOURCES.SETTINGS, action: PermissionAction.READ }\n    ],\n    [UserRoleType.EMPLOYEE]: [\n      // Dashboard access\n      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },\n      \n      // Inventory access\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.UPDATE },\n      \n      // Product access\n      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.UPDATE },\n      \n      // Category access\n      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ },\n      \n      // Supplier access\n      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },\n      \n      // Customer access\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.CREATE },\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.UPDATE },\n      \n      // Order access\n      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.CREATE },\n      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.UPDATE }\n    ],\n    [UserRoleType.VIEWER]: [\n      // Dashboard access\n      { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ },\n      \n      // Read-only access to most resources\n      { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ },\n      { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ }\n    ]\n  };\n\n  constructor(private authService: AuthService) {}\n\n  /**\n   * Check if current user has permission for a specific resource and action\n   */\n  hasPermission(resource: string, action: PermissionAction | string): boolean {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return false;\n    }\n\n    return this.userHasPermission(user, resource, action);\n  }\n\n  /**\n   * Check if user has permission for a specific resource and action\n   */\n  userHasPermission(user: User, resource: string, action: PermissionAction | string): boolean {\n    // Admin role has all permissions\n    if (this.userHasRole(user, UserRoleType.ADMIN)) {\n      return true;\n    }\n\n    // Check explicit permissions first\n    const hasExplicitPermission = user.permissions.some(permission => \n      (permission.resource === resource || permission.resource === '*') &&\n      (permission.action === action || permission.action === '*' as PermissionAction)\n    );\n\n    if (hasExplicitPermission) {\n      return true;\n    }\n\n    // Check role-based permissions\n    return user.roles.some(role => \n      this.roleHasPermission(role, resource, action)\n    );\n  }\n\n  /**\n   * Check if current user has specific role\n   */\n  hasRole(roleName: UserRoleType | string): boolean {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return false;\n    }\n\n    return this.userHasRole(user, roleName);\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  userHasRole(user: User, roleName: UserRoleType | string): boolean {\n    return user.roles.some(role => role.name === roleName);\n  }\n\n  /**\n   * Check if role has permission for a specific resource and action\n   */\n  roleHasPermission(role: UserRole, resource: string, action: PermissionAction | string): boolean {\n    // Check explicit role permissions\n    const hasExplicitPermission = role.permissions.some(permission => \n      (permission.resource === resource || permission.resource === '*') &&\n      (permission.action === action || permission.action === '*' as PermissionAction)\n    );\n\n    if (hasExplicitPermission) {\n      return true;\n    }\n\n    // Check default role permissions\n    const roleType = role.name as UserRoleType;\n    const defaultPermissions = this.DEFAULT_ROLE_PERMISSIONS[roleType];\n    \n    if (!defaultPermissions) {\n      return false;\n    }\n\n    return defaultPermissions.some(permission => \n      (permission.resource === resource || permission.resource === '*') &&\n      (permission.action === action || permission.action === '*')\n    );\n  }\n\n  /**\n   * Check multiple permissions (AND logic)\n   */\n  hasAllPermissions(permissions: PermissionCheck[]): boolean {\n    return permissions.every(permission => \n      this.hasPermission(permission.resource, permission.action)\n    );\n  }\n\n  /**\n   * Check multiple permissions (OR logic)\n   */\n  hasAnyPermission(permissions: PermissionCheck[]): boolean {\n    return permissions.some(permission => \n      this.hasPermission(permission.resource, permission.action)\n    );\n  }\n\n  /**\n   * Get all permissions for current user\n   */\n  getCurrentUserPermissions(): ResourcePermissions {\n    const user = this.authService.getCurrentUserSync();\n    if (!user) {\n      return {};\n    }\n\n    return this.getUserPermissions(user);\n  }\n\n  /**\n   * Get all permissions for a specific user\n   */\n  getUserPermissions(user: User): ResourcePermissions {\n    const permissions: ResourcePermissions = {};\n    const resources = Object.values(AuthorizationService.RESOURCES);\n    const actions = Object.values(PermissionAction);\n\n    resources.forEach(resource => {\n      permissions[resource] = {};\n      actions.forEach(action => {\n        permissions[resource][action] = this.userHasPermission(user, resource, action);\n      });\n    });\n\n    return permissions;\n  }\n\n  /**\n   * Check if current user can access a specific route\n   */\n  canAccessRoute(route: string): boolean {\n    // Map routes to required permissions\n    const routePermissions: Record<string, PermissionCheck[]> = {\n      '/dashboard': [\n        { resource: AuthorizationService.RESOURCES.DASHBOARD, action: PermissionAction.READ }\n      ],\n      '/inventory': [\n        { resource: AuthorizationService.RESOURCES.INVENTORY, action: PermissionAction.READ }\n      ],\n      '/products': [\n        { resource: AuthorizationService.RESOURCES.PRODUCTS, action: PermissionAction.READ }\n      ],\n      '/categories': [\n        { resource: AuthorizationService.RESOURCES.CATEGORIES, action: PermissionAction.READ }\n      ],\n      '/suppliers': [\n        { resource: AuthorizationService.RESOURCES.SUPPLIERS, action: PermissionAction.READ }\n      ],\n      '/customers': [\n        { resource: AuthorizationService.RESOURCES.CUSTOMERS, action: PermissionAction.READ }\n      ],\n      '/orders': [\n        { resource: AuthorizationService.RESOURCES.ORDERS, action: PermissionAction.READ }\n      ],\n      '/reports': [\n        { resource: AuthorizationService.RESOURCES.REPORTS, action: PermissionAction.READ }\n      ],\n      '/users': [\n        { resource: AuthorizationService.RESOURCES.USERS, action: PermissionAction.READ }\n      ],\n      '/settings': [\n        { resource: AuthorizationService.RESOURCES.SETTINGS, action: PermissionAction.READ }\n      ]\n    };\n\n    const requiredPermissions = routePermissions[route];\n    if (!requiredPermissions) {\n      return true; // Allow access to routes without specific permissions\n    }\n\n    return this.hasAllPermissions(requiredPermissions);\n  }\n\n  /**\n   * Log permission denied event\n   */\n  logPermissionDenied(resource: string, action: string, route?: string): void {\n    const user = this.authService.getCurrentUserSync();\n    const description = `Permission denied for ${action} on ${resource}${route ? ` (route: ${route})` : ''}`;\n    \n    console.warn(`Authorization: ${description}`, {\n      user: user?.username,\n      resource,\n      action,\n      route\n    });\n\n    // This would typically send to a logging service\n    // For now, we'll just log to console\n  }\n\n  /**\n   * Get user-friendly permission description\n   */\n  getPermissionDescription(resource: string, action: PermissionAction): string {\n    const actionDescriptions = {\n      [PermissionAction.CREATE]: 'create',\n      [PermissionAction.READ]: 'view',\n      [PermissionAction.UPDATE]: 'edit',\n      [PermissionAction.DELETE]: 'delete',\n      [PermissionAction.MANAGE]: 'manage'\n    };\n\n    const resourceDescriptions = {\n      [AuthorizationService.RESOURCES.DASHBOARD]: 'Dashboard',\n      [AuthorizationService.RESOURCES.INVENTORY]: 'Inventory',\n      [AuthorizationService.RESOURCES.PRODUCTS]: 'Products',\n      [AuthorizationService.RESOURCES.CATEGORIES]: 'Categories',\n      [AuthorizationService.RESOURCES.SUPPLIERS]: 'Suppliers',\n      [AuthorizationService.RESOURCES.CUSTOMERS]: 'Customers',\n      [AuthorizationService.RESOURCES.ORDERS]: 'Orders',\n      [AuthorizationService.RESOURCES.REPORTS]: 'Reports',\n      [AuthorizationService.RESOURCES.USERS]: 'Users',\n      [AuthorizationService.RESOURCES.ROLES]: 'Roles',\n      [AuthorizationService.RESOURCES.SETTINGS]: 'Settings',\n      [AuthorizationService.RESOURCES.AUDIT_LOGS]: 'Audit Logs'\n    };\n\n    const actionDesc = actionDescriptions[action as PermissionAction] || action;\n    const resourceDesc = resourceDescriptions[resource as keyof typeof resourceDescriptions] || resource;\n\n    return `${actionDesc} ${resourceDesc}`;\n  }\n}\n"], "mappings": "AAGA,SAIEA,gBAAgB,EAChBC,YAAY,QAGP,uBAAuB;;;AAiB9B,OAAM,MAAOC,oBAAoB;EAE/B;;IACuB,KAAAC,SAAS,GAAG;MACjCC,SAAS,EAAE,WAAW;MACtBC,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,YAAY;MACxBC,SAAS,EAAE,WAAW;MACtBC,SAAS,EAAE,WAAW;MACtBC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE;KACJ;EAAC;EAsFXC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IApF/B;IACiB,KAAAC,wBAAwB,GAA4C;MACnF,CAACjB,YAAY,CAACkB,KAAK,GAAG;MACpB;MACA;QAAEC,QAAQ,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAG,CAAE,CAC/B;MACD,CAACpB,YAAY,CAACqB,OAAO,GAAG;MACtB;MACA;QAAEF,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACC,SAAS;QAAEiB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAErF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE,EACvF;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACyB;MAAM,CAAE;MAEvF;MACA;QAAEL,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACG,QAAQ;QAAEe,MAAM,EAAErB,gBAAgB,CAAC0B;MAAM,CAAE;MAEtF;MACA;QAAEN,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACI,UAAU;QAAEc,MAAM,EAAErB,gBAAgB,CAAC0B;MAAM,CAAE;MAExF;MACA;QAAEN,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACK,SAAS;QAAEa,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACK,SAAS;QAAEa,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE;MAEvF;MACA;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE;MAEvF;MACA;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAAC0B;MAAM,CAAE;MAEpF;MACA;QAAEN,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACQ,OAAO;QAAEU,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAEnF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACS,KAAK;QAAES,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAEjF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACW,QAAQ;QAAEO,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACrF;MACD,CAACtB,YAAY,CAAC0B,QAAQ,GAAG;MACvB;MACA;QAAEP,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACC,SAAS;QAAEiB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAErF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE;MAEvF;MACA;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACG,QAAQ;QAAEe,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACpF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACG,QAAQ;QAAEe,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE;MAEtF;MACA;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACI,UAAU;QAAEc,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAEtF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACK,SAAS;QAAEa,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAErF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACyB;MAAM,CAAE,EACvF;QAAEL,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE;MAEvF;MACA;QAAEJ,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EAClF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAACyB;MAAM,CAAE,EACpF;QAAEL,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAACwB;MAAM,CAAE,CACrF;MACD,CAACvB,YAAY,CAAC2B,MAAM,GAAG;MACrB;MACA;QAAER,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACC,SAAS;QAAEiB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;MAErF;MACA;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACG,QAAQ;QAAEe,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACpF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACI,UAAU;QAAEc,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACtF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACK,SAAS;QAAEa,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EACrF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,EAClF;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACQ,OAAO;QAAEU,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;KAEtF;EAE8C;EAE/C;;;EAGAM,aAAaA,CAACT,QAAgB,EAAEC,MAAiC;IAC/D,MAAMS,IAAI,GAAG,IAAI,CAACb,WAAW,CAACc,kBAAkB,EAAE;IAClD,IAAI,CAACD,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,OAAO,IAAI,CAACE,iBAAiB,CAACF,IAAI,EAAEV,QAAQ,EAAEC,MAAM,CAAC;EACvD;EAEA;;;EAGAW,iBAAiBA,CAACF,IAAU,EAAEV,QAAgB,EAAEC,MAAiC;IAC/E;IACA,IAAI,IAAI,CAACY,WAAW,CAACH,IAAI,EAAE7B,YAAY,CAACkB,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;;IAGb;IACA,MAAMe,qBAAqB,GAAGJ,IAAI,CAACK,WAAW,CAACC,IAAI,CAACC,UAAU,IAC5D,CAACA,UAAU,CAACjB,QAAQ,KAAKA,QAAQ,IAAIiB,UAAU,CAACjB,QAAQ,KAAK,GAAG,MAC/DiB,UAAU,CAAChB,MAAM,KAAKA,MAAM,IAAIgB,UAAU,CAAChB,MAAM,KAAK,GAAuB,CAAC,CAChF;IAED,IAAIa,qBAAqB,EAAE;MACzB,OAAO,IAAI;;IAGb;IACA,OAAOJ,IAAI,CAACQ,KAAK,CAACF,IAAI,CAACG,IAAI,IACzB,IAAI,CAACC,iBAAiB,CAACD,IAAI,EAAEnB,QAAQ,EAAEC,MAAM,CAAC,CAC/C;EACH;EAEA;;;EAGAoB,OAAOA,CAACC,QAA+B;IACrC,MAAMZ,IAAI,GAAG,IAAI,CAACb,WAAW,CAACc,kBAAkB,EAAE;IAClD,IAAI,CAACD,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,OAAO,IAAI,CAACG,WAAW,CAACH,IAAI,EAAEY,QAAQ,CAAC;EACzC;EAEA;;;EAGAT,WAAWA,CAACH,IAAU,EAAEY,QAA+B;IACrD,OAAOZ,IAAI,CAACQ,KAAK,CAACF,IAAI,CAACG,IAAI,IAAIA,IAAI,CAACI,IAAI,KAAKD,QAAQ,CAAC;EACxD;EAEA;;;EAGAF,iBAAiBA,CAACD,IAAc,EAAEnB,QAAgB,EAAEC,MAAiC;IACnF;IACA,MAAMa,qBAAqB,GAAGK,IAAI,CAACJ,WAAW,CAACC,IAAI,CAACC,UAAU,IAC5D,CAACA,UAAU,CAACjB,QAAQ,KAAKA,QAAQ,IAAIiB,UAAU,CAACjB,QAAQ,KAAK,GAAG,MAC/DiB,UAAU,CAAChB,MAAM,KAAKA,MAAM,IAAIgB,UAAU,CAAChB,MAAM,KAAK,GAAuB,CAAC,CAChF;IAED,IAAIa,qBAAqB,EAAE;MACzB,OAAO,IAAI;;IAGb;IACA,MAAMU,QAAQ,GAAGL,IAAI,CAACI,IAAoB;IAC1C,MAAME,kBAAkB,GAAG,IAAI,CAAC3B,wBAAwB,CAAC0B,QAAQ,CAAC;IAElE,IAAI,CAACC,kBAAkB,EAAE;MACvB,OAAO,KAAK;;IAGd,OAAOA,kBAAkB,CAACT,IAAI,CAACC,UAAU,IACvC,CAACA,UAAU,CAACjB,QAAQ,KAAKA,QAAQ,IAAIiB,UAAU,CAACjB,QAAQ,KAAK,GAAG,MAC/DiB,UAAU,CAAChB,MAAM,KAAKA,MAAM,IAAIgB,UAAU,CAAChB,MAAM,KAAK,GAAG,CAAC,CAC5D;EACH;EAEA;;;EAGAyB,iBAAiBA,CAACX,WAA8B;IAC9C,OAAOA,WAAW,CAACY,KAAK,CAACV,UAAU,IACjC,IAAI,CAACR,aAAa,CAACQ,UAAU,CAACjB,QAAQ,EAAEiB,UAAU,CAAChB,MAAM,CAAC,CAC3D;EACH;EAEA;;;EAGA2B,gBAAgBA,CAACb,WAA8B;IAC7C,OAAOA,WAAW,CAACC,IAAI,CAACC,UAAU,IAChC,IAAI,CAACR,aAAa,CAACQ,UAAU,CAACjB,QAAQ,EAAEiB,UAAU,CAAChB,MAAM,CAAC,CAC3D;EACH;EAEA;;;EAGA4B,yBAAyBA,CAAA;IACvB,MAAMnB,IAAI,GAAG,IAAI,CAACb,WAAW,CAACc,kBAAkB,EAAE;IAClD,IAAI,CAACD,IAAI,EAAE;MACT,OAAO,EAAE;;IAGX,OAAO,IAAI,CAACoB,kBAAkB,CAACpB,IAAI,CAAC;EACtC;EAEA;;;EAGAoB,kBAAkBA,CAACpB,IAAU;IAC3B,MAAMK,WAAW,GAAwB,EAAE;IAC3C,MAAMgB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACnD,oBAAoB,CAACC,SAAS,CAAC;IAC/D,MAAMmD,OAAO,GAAGF,MAAM,CAACC,MAAM,CAACrD,gBAAgB,CAAC;IAE/CmD,SAAS,CAACI,OAAO,CAACnC,QAAQ,IAAG;MAC3Be,WAAW,CAACf,QAAQ,CAAC,GAAG,EAAE;MAC1BkC,OAAO,CAACC,OAAO,CAAClC,MAAM,IAAG;QACvBc,WAAW,CAACf,QAAQ,CAAC,CAACC,MAAM,CAAC,GAAG,IAAI,CAACW,iBAAiB,CAACF,IAAI,EAAEV,QAAQ,EAAEC,MAAM,CAAC;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOc,WAAW;EACpB;EAEA;;;EAGAqB,cAAcA,CAACC,KAAa;IAC1B;IACA,MAAMC,gBAAgB,GAAsC;MAC1D,YAAY,EAAE,CACZ;QAAEtC,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACC,SAAS;QAAEiB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACtF;MACD,YAAY,EAAE,CACZ;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACE,SAAS;QAAEgB,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACtF;MACD,WAAW,EAAE,CACX;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACG,QAAQ;QAAEe,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACrF;MACD,aAAa,EAAE,CACb;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACI,UAAU;QAAEc,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACvF;MACD,YAAY,EAAE,CACZ;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACK,SAAS;QAAEa,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACtF;MACD,YAAY,EAAE,CACZ;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACM,SAAS;QAAEY,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACtF;MACD,SAAS,EAAE,CACT;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACO,MAAM;QAAEW,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACnF;MACD,UAAU,EAAE,CACV;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACQ,OAAO;QAAEU,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CACpF;MACD,QAAQ,EAAE,CACR;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACS,KAAK;QAAES,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE,CAClF;MACD,WAAW,EAAE,CACX;QAAEH,QAAQ,EAAElB,oBAAoB,CAACC,SAAS,CAACW,QAAQ;QAAEO,MAAM,EAAErB,gBAAgB,CAACuB;MAAI,CAAE;KAEvF;IAED,MAAMoC,mBAAmB,GAAGD,gBAAgB,CAACD,KAAK,CAAC;IACnD,IAAI,CAACE,mBAAmB,EAAE;MACxB,OAAO,IAAI,CAAC,CAAC;;IAGf,OAAO,IAAI,CAACb,iBAAiB,CAACa,mBAAmB,CAAC;EACpD;EAEA;;;EAGAC,mBAAmBA,CAACxC,QAAgB,EAAEC,MAAc,EAAEoC,KAAc;IAClE,MAAM3B,IAAI,GAAG,IAAI,CAACb,WAAW,CAACc,kBAAkB,EAAE;IAClD,MAAM8B,WAAW,GAAG,yBAAyBxC,MAAM,OAAOD,QAAQ,GAAGqC,KAAK,GAAG,YAAYA,KAAK,GAAG,GAAG,EAAE,EAAE;IAExGK,OAAO,CAACC,IAAI,CAAC,kBAAkBF,WAAW,EAAE,EAAE;MAC5C/B,IAAI,EAAEA,IAAI,EAAEkC,QAAQ;MACpB5C,QAAQ;MACRC,MAAM;MACNoC;KACD,CAAC;IAEF;IACA;EACF;EAEA;;;EAGAQ,wBAAwBA,CAAC7C,QAAgB,EAAEC,MAAwB;IACjE,MAAM6C,kBAAkB,GAAG;MACzB,CAAClE,gBAAgB,CAACyB,MAAM,GAAG,QAAQ;MACnC,CAACzB,gBAAgB,CAACuB,IAAI,GAAG,MAAM;MAC/B,CAACvB,gBAAgB,CAACwB,MAAM,GAAG,MAAM;MACjC,CAACxB,gBAAgB,CAACmE,MAAM,GAAG,QAAQ;MACnC,CAACnE,gBAAgB,CAAC0B,MAAM,GAAG;KAC5B;IAED,MAAM0C,oBAAoB,GAAG;MAC3B,CAAClE,oBAAoB,CAACC,SAAS,CAACC,SAAS,GAAG,WAAW;MACvD,CAACF,oBAAoB,CAACC,SAAS,CAACE,SAAS,GAAG,WAAW;MACvD,CAACH,oBAAoB,CAACC,SAAS,CAACG,QAAQ,GAAG,UAAU;MACrD,CAACJ,oBAAoB,CAACC,SAAS,CAACI,UAAU,GAAG,YAAY;MACzD,CAACL,oBAAoB,CAACC,SAAS,CAACK,SAAS,GAAG,WAAW;MACvD,CAACN,oBAAoB,CAACC,SAAS,CAACM,SAAS,GAAG,WAAW;MACvD,CAACP,oBAAoB,CAACC,SAAS,CAACO,MAAM,GAAG,QAAQ;MACjD,CAACR,oBAAoB,CAACC,SAAS,CAACQ,OAAO,GAAG,SAAS;MACnD,CAACT,oBAAoB,CAACC,SAAS,CAACS,KAAK,GAAG,OAAO;MAC/C,CAACV,oBAAoB,CAACC,SAAS,CAACU,KAAK,GAAG,OAAO;MAC/C,CAACX,oBAAoB,CAACC,SAAS,CAACW,QAAQ,GAAG,UAAU;MACrD,CAACZ,oBAAoB,CAACC,SAAS,CAACY,UAAU,GAAG;KAC9C;IAED,MAAMsD,UAAU,GAAGH,kBAAkB,CAAC7C,MAA0B,CAAC,IAAIA,MAAM;IAC3E,MAAMiD,YAAY,GAAGF,oBAAoB,CAAChD,QAA6C,CAAC,IAAIA,QAAQ;IAEpG,OAAO,GAAGiD,UAAU,IAAIC,YAAY,EAAE;EACxC;;;uBA3UWpE,oBAAoB,EAAAqE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAApBxE,oBAAoB;MAAAyE,OAAA,EAApBzE,oBAAoB,CAAA0E,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}