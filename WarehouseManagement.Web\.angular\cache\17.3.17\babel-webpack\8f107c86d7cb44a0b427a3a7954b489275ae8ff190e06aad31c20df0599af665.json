{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/language.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/api\";\nconst _c0 = a0 => ({\n  time: a0\n});\nconst _c1 = a0 => ({\n  attempts: a0\n});\nfunction LoginComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"p-message\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", ctx_r0.languageService.translate(\"auth.login.lockout.active\", i0.ɵɵpureFunction1(1, _c0, ctx_r0.remainingLockoutTime)));\n  }\n}\nfunction LoginComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"username\"), \" \");\n  }\n}\nfunction LoginComponent_small_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"p-message\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", ctx_r0.languageService.translate(\"auth.login.attemptsRemaining\", i0.ɵɵpureFunction1(1, _c1, ctx_r0.remainingAttempts)));\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, languageService, router, route, messageService) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.languageService = languageService;\n      this.router = router;\n      this.route = route;\n      this.messageService = messageService;\n      this.loading = false;\n      this.returnUrl = '/dashboard';\n      this.showPassword = false;\n      this.loginAttempts = 0;\n      this.maxLoginAttempts = 5;\n      this.lockoutTime = 15; // minutes\n      this.isLockedOut = false;\n      this.destroy$ = new Subject();\n      this.loginForm = this.createLoginForm();\n    }\n    ngOnInit() {\n      // Get return URL from route parameters or default to dashboard\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n      // Check if user is already authenticated\n      this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(authState => {\n        if (authState.isAuthenticated) {\n          this.router.navigate([this.returnUrl]);\n        }\n        if (authState.error) {\n          this.handleLoginError(authState.error);\n        }\n        this.loading = authState.loading;\n      });\n      // Check for lockout status\n      this.checkLockoutStatus();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createLoginForm() {\n      return this.formBuilder.group({\n        username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50)]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        rememberMe: [false]\n      });\n    }\n    onSubmit() {\n      if (this.loginForm.invalid || this.loading || this.isLockedOut) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.loading = true;\n      const loginRequest = {\n        username: this.loginForm.value.username.trim(),\n        password: this.loginForm.value.password,\n        rememberMe: this.loginForm.value.rememberMe\n      };\n      this.authService.login(loginRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.loginAttempts = 0;\n          this.clearLockoutStatus();\n          this.messageService.add({\n            severity: 'success',\n            summary: this.languageService.translate('auth.login.success'),\n            detail: this.languageService.translate('auth.login.welcomeBack', {\n              name: response.user.firstName\n            })\n          });\n          // Navigate to return URL or dashboard\n          this.router.navigate([this.returnUrl]);\n        },\n        error: error => {\n          this.handleLoginError(error);\n        }\n      });\n    }\n    handleLoginError(error) {\n      this.loading = false;\n      this.loginAttempts++;\n      let errorMessage = this.languageService.translate('auth.login.error.generic');\n      if (error?.status === 401) {\n        errorMessage = this.languageService.translate('auth.login.error.invalidCredentials');\n      } else if (error?.status === 423) {\n        errorMessage = this.languageService.translate('auth.login.error.accountLocked');\n        this.handleAccountLockout();\n      } else if (error?.status === 429) {\n        errorMessage = this.languageService.translate('auth.login.error.tooManyAttempts');\n        this.handleRateLimitExceeded();\n      } else if (error?.message) {\n        errorMessage = error.message;\n      }\n      this.messageService.add({\n        severity: 'error',\n        summary: this.languageService.translate('auth.login.error.title'),\n        detail: errorMessage\n      });\n      // Check if we should lock out the user\n      if (this.loginAttempts >= this.maxLoginAttempts) {\n        this.handleAccountLockout();\n      }\n      // Clear password field on error\n      this.loginForm.patchValue({\n        password: ''\n      });\n    }\n    handleAccountLockout() {\n      this.isLockedOut = true;\n      this.lockoutEndTime = new Date(Date.now() + this.lockoutTime * 60 * 1000);\n      // Store lockout info in localStorage\n      localStorage.setItem('lockoutEndTime', this.lockoutEndTime.toISOString());\n      localStorage.setItem('loginAttempts', this.loginAttempts.toString());\n      this.messageService.add({\n        severity: 'warn',\n        summary: this.languageService.translate('auth.login.lockout.title'),\n        detail: this.languageService.translate('auth.login.lockout.message', {\n          minutes: this.lockoutTime\n        }),\n        life: 10000\n      });\n      // Start countdown timer\n      this.startLockoutTimer();\n    }\n    handleRateLimitExceeded() {\n      this.messageService.add({\n        severity: 'warn',\n        summary: this.languageService.translate('auth.login.rateLimit.title'),\n        detail: this.languageService.translate('auth.login.rateLimit.message'),\n        life: 8000\n      });\n    }\n    checkLockoutStatus() {\n      const lockoutEndTimeStr = localStorage.getItem('lockoutEndTime');\n      const storedAttempts = localStorage.getItem('loginAttempts');\n      if (lockoutEndTimeStr && storedAttempts) {\n        const lockoutEndTime = new Date(lockoutEndTimeStr);\n        const now = new Date();\n        if (now < lockoutEndTime) {\n          this.isLockedOut = true;\n          this.lockoutEndTime = lockoutEndTime;\n          this.loginAttempts = parseInt(storedAttempts, 10);\n          this.startLockoutTimer();\n        } else {\n          this.clearLockoutStatus();\n        }\n      }\n    }\n    startLockoutTimer() {\n      if (!this.lockoutEndTime) return;\n      const checkLockout = () => {\n        const now = new Date();\n        if (this.lockoutEndTime && now >= this.lockoutEndTime) {\n          this.clearLockoutStatus();\n          this.messageService.add({\n            severity: 'info',\n            summary: this.languageService.translate('auth.login.lockout.expired'),\n            detail: this.languageService.translate('auth.login.lockout.canTryAgain')\n          });\n        } else {\n          setTimeout(checkLockout, 1000);\n        }\n      };\n      setTimeout(checkLockout, 1000);\n    }\n    clearLockoutStatus() {\n      this.isLockedOut = false;\n      this.lockoutEndTime = undefined;\n      this.loginAttempts = 0;\n      localStorage.removeItem('lockoutEndTime');\n      localStorage.removeItem('loginAttempts');\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    togglePasswordVisibility() {\n      this.showPassword = !this.showPassword;\n    }\n    switchLanguage() {\n      this.languageService.toggleLanguage();\n    }\n    // Getter methods for template\n    get username() {\n      return this.loginForm.get('username');\n    }\n    get password() {\n      return this.loginForm.get('password');\n    }\n    get rememberMe() {\n      return this.loginForm.get('rememberMe');\n    }\n    get remainingLockoutTime() {\n      if (!this.lockoutEndTime) return '';\n      const now = new Date();\n      const remaining = Math.max(0, this.lockoutEndTime.getTime() - now.getTime());\n      const minutes = Math.floor(remaining / 60000);\n      const seconds = Math.floor(remaining % 60000 / 1000);\n      return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n    }\n    get remainingAttempts() {\n      return Math.max(0, this.maxLoginAttempts - this.loginAttempts);\n    }\n    // Form validation helpers\n    isFieldInvalid(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldError(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      if (!field || !field.errors) return '';\n      const errors = field.errors;\n      if (errors['required']) {\n        return this.languageService.translate(`auth.login.validation.${fieldName}.required`);\n      }\n      if (errors['minlength']) {\n        return this.languageService.translate(`auth.login.validation.${fieldName}.minLength`, {\n          min: errors['minlength'].requiredLength\n        });\n      }\n      if (errors['maxlength']) {\n        return this.languageService.translate(`auth.login.validation.${fieldName}.maxLength`, {\n          max: errors['maxlength'].requiredLength\n        });\n      }\n      return this.languageService.translate('auth.login.validation.invalid');\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.LanguageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 64,\n        vars: 37,\n        consts: [[1, \"login-container\"], [1, \"login-wrapper\"], [1, \"language-switcher\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-text\", \"p-button-sm\", 3, \"click\", \"label\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"pi\", \"pi-box\", \"text-4xl\", \"text-primary\"], [1, \"login-title\"], [1, \"login-subtitle\"], [\"class\", \"lockout-warning\", 4, \"ngIf\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"field\"], [\"for\", \"username\", 1, \"field-label\"], [1, \"required-asterisk\"], [1, \"p-inputgroup\"], [1, \"p-inputgroup-addon\"], [1, \"pi\", \"pi-user\"], [\"id\", \"username\", \"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"username\", \"autocomplete\", \"username\", 3, \"placeholder\", \"disabled\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"field-label\"], [1, \"pi\", \"pi-lock\"], [\"id\", \"password\", \"pInputText\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\", \"placeholder\", \"disabled\"], [1, \"p-inputgroup-addon\", \"cursor-pointer\", 3, \"click\"], [1, \"field-checkbox\"], [\"formControlName\", \"rememberMe\", \"inputId\", \"rememberMe\", 3, \"binary\", \"disabled\"], [\"for\", \"rememberMe\", 1, \"checkbox-label\"], [\"class\", \"attempts-warning\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", 1, \"login-button\", \"w-full\", 3, \"label\", \"loading\", \"disabled\"], [1, \"forgot-password\"], [\"href\", \"#\", 1, \"forgot-password-link\", 3, \"click\"], [1, \"login-footer\"], [1, \"footer-text\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"footer-separator\"], [1, \"security-notice\"], [\"severity\", \"info\", 3, \"text\"], [1, \"login-background\"], [1, \"background-pattern\"], [\"position\", \"top-right\"], [1, \"lockout-warning\"], [\"severity\", \"warn\", 3, \"text\"], [1, \"p-error\"], [1, \"attempts-warning\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_3_listener() {\n              return ctx.switchLanguage();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n            i0.ɵɵelement(7, \"i\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"h1\", 8);\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"p\", 9);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, LoginComponent_div_12_Template, 2, 3, \"div\", 10);\n            i0.ɵɵelementStart(13, \"form\", 11);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_13_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementStart(17, \"span\", 14);\n            i0.ɵɵtext(18, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 15)(20, \"span\", 16);\n            i0.ɵɵelement(21, \"i\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, LoginComponent_small_23_Template, 2, 1, \"small\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 20);\n            i0.ɵɵtext(26);\n            i0.ɵɵelementStart(27, \"span\", 14);\n            i0.ɵɵtext(28, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 15)(30, \"span\", 16);\n            i0.ɵɵelement(31, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(32, \"input\", 22);\n            i0.ɵɵelementStart(33, \"span\", 23);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_span_click_33_listener() {\n              return ctx.togglePasswordVisibility();\n            });\n            i0.ɵɵelement(34, \"i\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(35, LoginComponent_small_35_Template, 2, 1, \"small\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\", 24);\n            i0.ɵɵelement(37, \"p-checkbox\", 25);\n            i0.ɵɵelementStart(38, \"label\", 26);\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(40, LoginComponent_div_40_Template, 2, 3, \"div\", 27);\n            i0.ɵɵelement(41, \"button\", 28);\n            i0.ɵɵelementStart(42, \"div\", 29)(43, \"a\", 30);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_43_listener($event) {\n              return $event.preventDefault();\n            });\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 31)(46, \"p\", 32);\n            i0.ɵɵtext(47);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 33)(49, \"a\", 34);\n            i0.ɵɵtext(50);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"span\", 35);\n            i0.ɵɵtext(52, \"\\u2022\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"a\", 34);\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"span\", 35);\n            i0.ɵɵtext(56, \"\\u2022\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"a\", 34);\n            i0.ɵɵtext(58);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(59, \"div\", 36);\n            i0.ɵɵelement(60, \"p-message\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(61, \"div\", 38);\n            i0.ɵɵelement(62, \"div\", 39);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(63, \"p-toast\", 40);\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"rtl\", ctx.languageService.isArabic());\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"label\", ctx.languageService.isArabic() ? \"English\" : \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.title\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.subtitle\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLockedOut);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.username\"), \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"ng-invalid\", ctx.isFieldInvalid(\"username\"));\n            i0.ɵɵproperty(\"placeholder\", ctx.languageService.translate(\"auth.login.usernamePlaceholder\"))(\"disabled\", ctx.loading || ctx.isLockedOut);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"username\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.password\"), \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"ng-invalid\", ctx.isFieldInvalid(\"password\"));\n            i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"placeholder\", ctx.languageService.translate(\"auth.login.passwordPlaceholder\"))(\"disabled\", ctx.loading || ctx.isLockedOut);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassMap(ctx.showPassword ? \"pi pi-eye-slash\" : \"pi pi-eye\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"password\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"binary\", true)(\"disabled\", ctx.loading || ctx.isLockedOut);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.rememberMe\"), \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loginAttempts > 0 && !ctx.isLockedOut);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"label\", ctx.languageService.translate(\"auth.login.signIn\"))(\"loading\", ctx.loading)(\"disabled\", ctx.loginForm.invalid || ctx.isLockedOut);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.forgotPassword\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.languageService.translate(\"auth.login.footer.copyright\"), \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.privacy\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.terms\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(ctx.languageService.translate(\"auth.login.footer.support\"));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"text\", ctx.languageService.translate(\"auth.login.securityNotice\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassMap(ctx.languageService.isArabic() ? \"rtl-toast\" : \"\");\n          }\n        },\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;position:relative;background:linear-gradient(135deg,#667eea,#764ba2);padding:2rem}.login-container.rtl[_ngcontent-%COMP%]{direction:rtl}.login-wrapper[_ngcontent-%COMP%]{position:relative;z-index:2;width:100%;max-width:400px}.language-switcher[_ngcontent-%COMP%]{position:absolute;top:-60px;right:0;z-index:3}.rtl[_ngcontent-%COMP%]   .language-switcher[_ngcontent-%COMP%]{right:auto;left:0}.language-switcher[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#ffffff1a;border:1px solid rgba(255,255,255,.2);color:#fff;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:8px;transition:all .3s ease}.language-switcher[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#fff3;transform:translateY(-2px)}.login-card[_ngcontent-%COMP%]{background:#fffffff2;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-radius:20px;padding:3rem 2.5rem;box-shadow:0 20px 40px #0000001a,0 0 0 1px #fff3;border:1px solid rgba(255,255,255,.2);position:relative;overflow:hidden}.login-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,#667eea,#764ba2)}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2.5rem}.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{margin-bottom:1rem}.login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:var(--text-color);margin:0 0 .5rem;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.login-header[_ngcontent-%COMP%]   .login-subtitle[_ngcontent-%COMP%]{color:var(--text-color-secondary);margin:0;font-size:1rem}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]{margin-bottom:1.5rem}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:600;color:var(--text-color);font-size:.9rem}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   .required-asterisk[_ngcontent-%COMP%]{color:#ef4444;margin-left:.25rem}.rtl[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   .required-asterisk[_ngcontent-%COMP%]{margin-left:0;margin-right:.25rem}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon[_ngcontent-%COMP%]{background:var(--surface-100);border-color:var(--surface-300);color:var(--text-color-secondary);min-width:3rem;justify-content:center}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon.cursor-pointer[_ngcontent-%COMP%]{cursor:pointer;transition:all .2s ease}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   .p-inputgroup-addon.cursor-pointer[_ngcontent-%COMP%]:hover{background:var(--surface-200);color:var(--primary-color)}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{border-color:var(--surface-300);transition:all .2s ease}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border-color:var(--primary-color);box-shadow:0 0 0 .2rem rgba(var(--primary-color-rgb),.2)}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input.ng-invalid.ng-touched[_ngcontent-%COMP%]{border-color:#ef4444}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-inputgroup[_ngcontent-%COMP%]   input.ng-invalid.ng-touched[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #ef444433}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .p-error[_ngcontent-%COMP%]{display:block;margin-top:.25rem;font-size:.8rem}.login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:1.5rem}.login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]{margin-left:.5rem;font-size:.9rem;color:var(--text-color-secondary);cursor:pointer}.rtl[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   .field-checkbox[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]{margin-left:0;margin-right:.5rem}.login-form[_ngcontent-%COMP%]   .attempts-warning[_ngcontent-%COMP%]{margin-bottom:1.5rem}.login-form[_ngcontent-%COMP%]   .attempts-warning[_ngcontent-%COMP%]     .p-message{margin:0}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]{height:3rem;font-size:1rem;font-weight:600;background:linear-gradient(135deg,#667eea,#764ba2);border:none;border-radius:12px;transition:all .3s ease;position:relative;overflow:hidden}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:not(:disabled):hover{transform:translateY(-2px);box-shadow:0 10px 25px #667eea4d}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s ease}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:not(:disabled):hover:before{left:100%}.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem}.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]{color:var(--primary-color);text-decoration:none;font-size:.9rem;transition:all .2s ease}.login-form[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]   .forgot-password-link[_ngcontent-%COMP%]:hover{text-decoration:underline;color:var(--primary-color-text)}.lockout-warning[_ngcontent-%COMP%]{margin-bottom:1.5rem}.lockout-warning[_ngcontent-%COMP%]     .p-message{margin:0}.login-footer[_ngcontent-%COMP%]{margin-top:2rem;text-align:center;padding-top:1.5rem;border-top:1px solid var(--surface-200)}.login-footer[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%]{color:var(--text-color-secondary);font-size:.8rem;margin:0 0 .5rem}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:.5rem;flex-wrap:wrap}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]{color:var(--text-color-secondary);text-decoration:none;font-size:.8rem;transition:color .2s ease}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]:hover{color:var(--primary-color)}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-separator[_ngcontent-%COMP%]{color:var(--text-color-secondary);font-size:.8rem}.security-notice[_ngcontent-%COMP%]{margin-top:1.5rem}.security-notice[_ngcontent-%COMP%]     .p-message{background:#ffffffe6;border:1px solid rgba(255,255,255,.2);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:12px}.login-background[_ngcontent-%COMP%]{position:absolute;inset:0;z-index:1;overflow:hidden}.login-background[_ngcontent-%COMP%]   .background-pattern[_ngcontent-%COMP%]{position:absolute;inset:0;background-image:radial-gradient(circle at 25% 25%,rgba(255,255,255,.1) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(255,255,255,.1) 0%,transparent 50%);animation:_ngcontent-%COMP%_float 20s ease-in-out infinite}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0) rotate(0)}50%{transform:translateY(-20px) rotate(180deg)}}@media (max-width: 768px){.login-container[_ngcontent-%COMP%]{padding:1rem}.login-card[_ngcontent-%COMP%]{padding:2rem 1.5rem}.login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%]{font-size:1.5rem}.language-switcher[_ngcontent-%COMP%]{position:static;margin-bottom:1rem;text-align:center}}@media (max-width: 480px){.login-card[_ngcontent-%COMP%]{padding:1.5rem 1rem}.login-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]{margin-bottom:1rem}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]{height:2.5rem}}.dark-theme[_nghost-%COMP%]   .login-card[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .login-card[_ngcontent-%COMP%]{background:#1e1e1ef2;border-color:#ffffff1a}.dark-theme[_nghost-%COMP%]   .login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%], .dark-theme   [_nghost-%COMP%]   .login-header[_ngcontent-%COMP%]   .login-title[_ngcontent-%COMP%]{color:#fff}@media (prefers-contrast: high){.login-card[_ngcontent-%COMP%]{background:#fff;border:2px solid black}.login-button[_ngcontent-%COMP%]{background:#000;color:#fff}}  .rtl-toast .p-toast-message{direction:rtl;text-align:right}  .rtl-toast .p-toast-message .p-toast-message-content{flex-direction:row-reverse}  .rtl-toast .p-toast-message .p-toast-message-text{text-align:right}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}