{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/authorization.service\";\nimport * as i2 from \"../services/auth.service\";\nexport let HasPermissionDirective = /*#__PURE__*/(() => {\n  class HasPermissionDirective {\n    set appHasPermission(permission) {\n      this.checkPermission(permission);\n    }\n    constructor(templateRef, viewContainer, authorizationService, authService) {\n      this.templateRef = templateRef;\n      this.viewContainer = viewContainer;\n      this.authorizationService = authorizationService;\n      this.authService = authService;\n      this.destroy$ = new Subject();\n      this.hasView = false;\n      this.appHasPermissionRequireAll = true;\n    }\n    ngOnInit() {\n      // Listen for authentication state changes\n      this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.updateView();\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    checkPermission(permission) {\n      this.updateView();\n    }\n    updateView() {\n      const hasPermission = this.evaluatePermission();\n      if (hasPermission && !this.hasView) {\n        this.viewContainer.createEmbeddedView(this.templateRef);\n        this.hasView = true;\n      } else if (!hasPermission && this.hasView) {\n        this.viewContainer.clear();\n        this.hasView = false;\n      }\n    }\n    evaluatePermission() {\n      // If user is not authenticated, deny access\n      if (!this.authService.isAuthenticated()) {\n        return false;\n      }\n      // Handle different input types\n      const permissionInput = this.getPermissionInput();\n      if (typeof permissionInput === 'string') {\n        // Simple string permission check (resource:action format)\n        return this.checkStringPermission(permissionInput);\n      }\n      if (Array.isArray(permissionInput)) {\n        // Multiple permissions\n        return this.checkMultiplePermissions(permissionInput);\n      }\n      if (typeof permissionInput === 'object') {\n        // Single permission object\n        return this.checkSinglePermission(permissionInput);\n      }\n      return false;\n    }\n    getPermissionInput() {\n      // Priority: direct input > resource/action combination\n      if (this.appHasPermissionResource && this.appHasPermissionAction) {\n        return {\n          resource: this.appHasPermissionResource,\n          action: this.appHasPermissionAction\n        };\n      }\n      // This will be set by the @Input setter\n      return '';\n    }\n    checkStringPermission(permission) {\n      // Handle different string formats\n      if (permission.includes(':')) {\n        // Format: \"resource:action\"\n        const [resource, action] = permission.split(':');\n        return this.authorizationService.hasPermission(resource, action);\n      }\n      if (permission.includes('.')) {\n        // Format: \"resource.action\"\n        const [resource, action] = permission.split('.');\n        return this.authorizationService.hasPermission(resource, action);\n      }\n      // Assume it's a role name\n      return this.authorizationService.hasRole(permission);\n    }\n    checkSinglePermission(permission) {\n      return this.authorizationService.hasPermission(permission.resource, permission.action);\n    }\n    checkMultiplePermissions(permissions) {\n      if (this.appHasPermissionRequireAll) {\n        // User must have ALL permissions (AND logic)\n        return this.authorizationService.hasAllPermissions(permissions);\n      } else {\n        // User must have ANY permission (OR logic)\n        return this.authorizationService.hasAnyPermission(permissions);\n      }\n    }\n    static {\n      this.ɵfac = function HasPermissionDirective_Factory(t) {\n        return new (t || HasPermissionDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.AuthorizationService), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n        type: HasPermissionDirective,\n        selectors: [[\"\", \"appHasPermission\", \"\"]],\n        inputs: {\n          appHasPermission: \"appHasPermission\",\n          appHasPermissionResource: \"appHasPermissionResource\",\n          appHasPermissionAction: \"appHasPermissionAction\",\n          appHasPermissionRequireAll: \"appHasPermissionRequireAll\"\n        }\n      });\n    }\n  }\n  return HasPermissionDirective;\n})();\nexport let HasRoleDirective = /*#__PURE__*/(() => {\n  class HasRoleDirective {\n    set appHasRole(roles) {\n      this.checkRole(roles);\n    }\n    constructor(templateRef, viewContainer, authorizationService, authService) {\n      this.templateRef = templateRef;\n      this.viewContainer = viewContainer;\n      this.authorizationService = authorizationService;\n      this.authService = authService;\n      this.destroy$ = new Subject();\n      this.hasView = false;\n      this.appHasRoleRequireAll = false;\n    }\n    ngOnInit() {\n      // Listen for authentication state changes\n      this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.updateView();\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    checkRole(roles) {\n      this.updateView();\n    }\n    updateView() {\n      const hasRole = this.evaluateRole();\n      if (hasRole && !this.hasView) {\n        this.viewContainer.createEmbeddedView(this.templateRef);\n        this.hasView = true;\n      } else if (!hasRole && this.hasView) {\n        this.viewContainer.clear();\n        this.hasView = false;\n      }\n    }\n    evaluateRole() {\n      // If user is not authenticated, deny access\n      if (!this.authService.isAuthenticated()) {\n        return false;\n      }\n      const roleInput = this.getRoleInput();\n      if (typeof roleInput === 'string') {\n        return this.authorizationService.hasRole(roleInput);\n      }\n      if (Array.isArray(roleInput)) {\n        if (this.appHasRoleRequireAll) {\n          // User must have ALL roles\n          return roleInput.every(role => this.authorizationService.hasRole(role));\n        } else {\n          // User must have ANY role\n          return roleInput.some(role => this.authorizationService.hasRole(role));\n        }\n      }\n      return false;\n    }\n    getRoleInput() {\n      // This will be set by the @Input setter\n      return '';\n    }\n    static {\n      this.ɵfac = function HasRoleDirective_Factory(t) {\n        return new (t || HasRoleDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.AuthorizationService), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n        type: HasRoleDirective,\n        selectors: [[\"\", \"appHasRole\", \"\"]],\n        inputs: {\n          appHasRole: \"appHasRole\",\n          appHasRoleRequireAll: \"appHasRoleRequireAll\"\n        }\n      });\n    }\n  }\n  return HasRoleDirective;\n})();\nexport let IsAuthenticatedDirective = /*#__PURE__*/(() => {\n  class IsAuthenticatedDirective {\n    set appIsAuthenticated(show) {\n      this.showWhenAuthenticated = show;\n      this.updateView();\n    }\n    constructor(templateRef, viewContainer, authService) {\n      this.templateRef = templateRef;\n      this.viewContainer = viewContainer;\n      this.authService = authService;\n      this.destroy$ = new Subject();\n      this.hasView = false;\n      this.showWhenAuthenticated = true;\n    }\n    ngOnInit() {\n      // Listen for authentication state changes\n      this.authService.authState$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.updateView();\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    updateView() {\n      const isAuthenticated = this.authService.isAuthenticated();\n      const shouldShow = this.showWhenAuthenticated ? isAuthenticated : !isAuthenticated;\n      if (shouldShow && !this.hasView) {\n        this.viewContainer.createEmbeddedView(this.templateRef);\n        this.hasView = true;\n      } else if (!shouldShow && this.hasView) {\n        this.viewContainer.clear();\n        this.hasView = false;\n      }\n    }\n    static {\n      this.ɵfac = function IsAuthenticatedDirective_Factory(t) {\n        return new (t || IsAuthenticatedDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n        type: IsAuthenticatedDirective,\n        selectors: [[\"\", \"appIsAuthenticated\", \"\"]],\n        inputs: {\n          appIsAuthenticated: \"appIsAuthenticated\"\n        }\n      });\n    }\n  }\n  return IsAuthenticatedDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}