{"ast": null, "code": "import { PermissionAction, UserRoleType } from '../models/auth.models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nexport let AuthorizationService = /*#__PURE__*/(() => {\n  class AuthorizationService {\n    // Define resource constants\n    static {\n      this.RESOURCES = {\n        DASHBOARD: 'dashboard',\n        INVENTORY: 'inventory',\n        PRODUCTS: 'products',\n        CATEGORIES: 'categories',\n        SUPPLIERS: 'suppliers',\n        CUSTOMERS: 'customers',\n        ORDERS: 'orders',\n        REPORTS: 'reports',\n        USERS: 'users',\n        ROLES: 'roles',\n        SETTINGS: 'settings',\n        AUDIT_LOGS: 'audit_logs'\n      };\n    }\n    constructor(authService) {\n      this.authService = authService;\n      // Define default role permissions\n      this.DEFAULT_ROLE_PERMISSIONS = {\n        [UserRoleType.ADMIN]: [\n        // Admin has full access to everything\n        {\n          resource: '*',\n          action: '*'\n        }],\n        [UserRoleType.MANAGER]: [\n        // Dashboard access\n        {\n          resource: AuthorizationService.RESOURCES.DASHBOARD,\n          action: PermissionAction.READ\n        },\n        // Inventory management\n        {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.UPDATE\n        }, {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.CREATE\n        },\n        // Product management\n        {\n          resource: AuthorizationService.RESOURCES.PRODUCTS,\n          action: PermissionAction.MANAGE\n        },\n        // Category management\n        {\n          resource: AuthorizationService.RESOURCES.CATEGORIES,\n          action: PermissionAction.MANAGE\n        },\n        // Supplier management\n        {\n          resource: AuthorizationService.RESOURCES.SUPPLIERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.SUPPLIERS,\n          action: PermissionAction.UPDATE\n        },\n        // Customer management\n        {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.UPDATE\n        },\n        // Order management\n        {\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.MANAGE\n        },\n        // Reports access\n        {\n          resource: AuthorizationService.RESOURCES.REPORTS,\n          action: PermissionAction.READ\n        },\n        // User management (limited)\n        {\n          resource: AuthorizationService.RESOURCES.USERS,\n          action: PermissionAction.READ\n        },\n        // Settings access (limited)\n        {\n          resource: AuthorizationService.RESOURCES.SETTINGS,\n          action: PermissionAction.READ\n        }],\n        [UserRoleType.EMPLOYEE]: [\n        // Dashboard access\n        {\n          resource: AuthorizationService.RESOURCES.DASHBOARD,\n          action: PermissionAction.READ\n        },\n        // Inventory access\n        {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.UPDATE\n        },\n        // Product access\n        {\n          resource: AuthorizationService.RESOURCES.PRODUCTS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.PRODUCTS,\n          action: PermissionAction.UPDATE\n        },\n        // Category access\n        {\n          resource: AuthorizationService.RESOURCES.CATEGORIES,\n          action: PermissionAction.READ\n        },\n        // Supplier access\n        {\n          resource: AuthorizationService.RESOURCES.SUPPLIERS,\n          action: PermissionAction.READ\n        },\n        // Customer access\n        {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.CREATE\n        }, {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.UPDATE\n        },\n        // Order access\n        {\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.CREATE\n        }, {\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.UPDATE\n        }],\n        [UserRoleType.VIEWER]: [\n        // Dashboard access\n        {\n          resource: AuthorizationService.RESOURCES.DASHBOARD,\n          action: PermissionAction.READ\n        },\n        // Read-only access to most resources\n        {\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.PRODUCTS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.CATEGORIES,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.SUPPLIERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.READ\n        }, {\n          resource: AuthorizationService.RESOURCES.REPORTS,\n          action: PermissionAction.READ\n        }]\n      };\n    }\n    /**\n     * Check if current user has permission for a specific resource and action\n     */\n    hasPermission(resource, action) {\n      const user = this.authService.getCurrentUserSync();\n      if (!user) {\n        return false;\n      }\n      return this.userHasPermission(user, resource, action);\n    }\n    /**\n     * Check if user has permission for a specific resource and action\n     */\n    userHasPermission(user, resource, action) {\n      // Admin role has all permissions\n      if (this.userHasRole(user, UserRoleType.ADMIN)) {\n        return true;\n      }\n      // Check explicit permissions first\n      const hasExplicitPermission = user.permissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n      if (hasExplicitPermission) {\n        return true;\n      }\n      // Check role-based permissions\n      return user.roles.some(role => this.roleHasPermission(role, resource, action));\n    }\n    /**\n     * Check if current user has specific role\n     */\n    hasRole(roleName) {\n      const user = this.authService.getCurrentUserSync();\n      if (!user) {\n        return false;\n      }\n      return this.userHasRole(user, roleName);\n    }\n    /**\n     * Check if user has specific role\n     */\n    userHasRole(user, roleName) {\n      return user.roles.some(role => role.name === roleName);\n    }\n    /**\n     * Check if role has permission for a specific resource and action\n     */\n    roleHasPermission(role, resource, action) {\n      // Check explicit role permissions\n      const hasExplicitPermission = role.permissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n      if (hasExplicitPermission) {\n        return true;\n      }\n      // Check default role permissions\n      const roleType = role.name;\n      const defaultPermissions = this.DEFAULT_ROLE_PERMISSIONS[roleType];\n      if (!defaultPermissions) {\n        return false;\n      }\n      return defaultPermissions.some(permission => (permission.resource === resource || permission.resource === '*') && (permission.action === action || permission.action === '*'));\n    }\n    /**\n     * Check multiple permissions (AND logic)\n     */\n    hasAllPermissions(permissions) {\n      return permissions.every(permission => this.hasPermission(permission.resource, permission.action));\n    }\n    /**\n     * Check multiple permissions (OR logic)\n     */\n    hasAnyPermission(permissions) {\n      return permissions.some(permission => this.hasPermission(permission.resource, permission.action));\n    }\n    /**\n     * Get all permissions for current user\n     */\n    getCurrentUserPermissions() {\n      const user = this.authService.getCurrentUserSync();\n      if (!user) {\n        return {};\n      }\n      return this.getUserPermissions(user);\n    }\n    /**\n     * Get all permissions for a specific user\n     */\n    getUserPermissions(user) {\n      const permissions = {};\n      const resources = Object.values(AuthorizationService.RESOURCES);\n      const actions = Object.values(PermissionAction);\n      resources.forEach(resource => {\n        permissions[resource] = {};\n        actions.forEach(action => {\n          permissions[resource][action] = this.userHasPermission(user, resource, action);\n        });\n      });\n      return permissions;\n    }\n    /**\n     * Check if current user can access a specific route\n     */\n    canAccessRoute(route) {\n      // Map routes to required permissions\n      const routePermissions = {\n        '/dashboard': [{\n          resource: AuthorizationService.RESOURCES.DASHBOARD,\n          action: PermissionAction.READ\n        }],\n        '/inventory': [{\n          resource: AuthorizationService.RESOURCES.INVENTORY,\n          action: PermissionAction.READ\n        }],\n        '/products': [{\n          resource: AuthorizationService.RESOURCES.PRODUCTS,\n          action: PermissionAction.READ\n        }],\n        '/categories': [{\n          resource: AuthorizationService.RESOURCES.CATEGORIES,\n          action: PermissionAction.READ\n        }],\n        '/suppliers': [{\n          resource: AuthorizationService.RESOURCES.SUPPLIERS,\n          action: PermissionAction.READ\n        }],\n        '/customers': [{\n          resource: AuthorizationService.RESOURCES.CUSTOMERS,\n          action: PermissionAction.READ\n        }],\n        '/orders': [{\n          resource: AuthorizationService.RESOURCES.ORDERS,\n          action: PermissionAction.READ\n        }],\n        '/reports': [{\n          resource: AuthorizationService.RESOURCES.REPORTS,\n          action: PermissionAction.READ\n        }],\n        '/users': [{\n          resource: AuthorizationService.RESOURCES.USERS,\n          action: PermissionAction.READ\n        }],\n        '/settings': [{\n          resource: AuthorizationService.RESOURCES.SETTINGS,\n          action: PermissionAction.READ\n        }]\n      };\n      const requiredPermissions = routePermissions[route];\n      if (!requiredPermissions) {\n        return true; // Allow access to routes without specific permissions\n      }\n      return this.hasAllPermissions(requiredPermissions);\n    }\n    /**\n     * Log permission denied event\n     */\n    logPermissionDenied(resource, action, route) {\n      const user = this.authService.getCurrentUserSync();\n      const description = `Permission denied for ${action} on ${resource}${route ? ` (route: ${route})` : ''}`;\n      console.warn(`Authorization: ${description}`, {\n        user: user?.username,\n        resource,\n        action,\n        route\n      });\n      // This would typically send to a logging service\n      // For now, we'll just log to console\n    }\n    /**\n     * Get user-friendly permission description\n     */\n    getPermissionDescription(resource, action) {\n      const actionDescriptions = {\n        [PermissionAction.CREATE]: 'create',\n        [PermissionAction.READ]: 'view',\n        [PermissionAction.UPDATE]: 'edit',\n        [PermissionAction.DELETE]: 'delete',\n        [PermissionAction.MANAGE]: 'manage'\n      };\n      const resourceDescriptions = {\n        [AuthorizationService.RESOURCES.DASHBOARD]: 'Dashboard',\n        [AuthorizationService.RESOURCES.INVENTORY]: 'Inventory',\n        [AuthorizationService.RESOURCES.PRODUCTS]: 'Products',\n        [AuthorizationService.RESOURCES.CATEGORIES]: 'Categories',\n        [AuthorizationService.RESOURCES.SUPPLIERS]: 'Suppliers',\n        [AuthorizationService.RESOURCES.CUSTOMERS]: 'Customers',\n        [AuthorizationService.RESOURCES.ORDERS]: 'Orders',\n        [AuthorizationService.RESOURCES.REPORTS]: 'Reports',\n        [AuthorizationService.RESOURCES.USERS]: 'Users',\n        [AuthorizationService.RESOURCES.ROLES]: 'Roles',\n        [AuthorizationService.RESOURCES.SETTINGS]: 'Settings',\n        [AuthorizationService.RESOURCES.AUDIT_LOGS]: 'Audit Logs'\n      };\n      const actionDesc = actionDescriptions[action] || action;\n      const resourceDesc = resourceDescriptions[resource] || resource;\n      return `${actionDesc} ${resourceDesc}`;\n    }\n    static {\n      this.ɵfac = function AuthorizationService_Factory(t) {\n        return new (t || AuthorizationService)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthorizationService,\n        factory: AuthorizationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthorizationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}