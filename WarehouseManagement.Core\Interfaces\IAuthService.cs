using System.ComponentModel.DataAnnotations;
using WarehouseManagement.Core.Common;
using WarehouseManagement.Core.Entities;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.Core.Interfaces;

public interface IAuthService
{
    Task<Result<LoginResponse>> LoginAsync(LoginRequest request, string ipAddress, string userAgent);
    Task<Result<RefreshTokenResponse>> RefreshTokenAsync(string refreshToken, string ipAddress);
    Task<Result> LogoutAsync(int userId, string ipAddress);
    Task<Result<UserDto>> GetCurrentUserAsync(int userId);
    Task<Result> ValidateSessionAsync(int userId);
    Task<Result> ChangePasswordAsync(int userId, ChangePasswordRequest request);
    Task<Result> ResetPasswordAsync(ResetPasswordRequest request);
    Task<Result> ConfirmResetPasswordAsync(ConfirmResetPasswordRequest request);

    Task<Result<string>> GetUserPasswordHashAsync(string password);
}

public interface IUserService
{
    Task<Result<UserDto>> GetUserByIdAsync(int id);
    Task<Result<UserDto>> GetUserByUsernameAsync(string username);
    Task<Result<UserDto>> GetUserByEmailAsync(string email);
    Task<Result<UserDto>> CreateUserAsync(CreateUserRequest request);
    Task<Result<UserDto>> UpdateUserAsync(int id, UpdateUserRequest request);
    Task<Result> DeleteUserAsync(int id);
    Task<Result<PagedResult<UserDto>>> GetUsersAsync(int page, int pageSize, string? search = null);
    Task<Result> AssignRoleAsync(int userId, int roleId);
    Task<Result> RemoveRoleAsync(int userId, int roleId);
    Task<Result> GrantPermissionAsync(int userId, int permissionId);
    Task<Result> RevokePermissionAsync(int userId, int permissionId);
    Task<Result> LockUserAsync(int userId, DateTime? lockoutEnd = null);
    Task<Result> UnlockUserAsync(int userId);
}

public interface IRoleService
{
    Task<Result<RoleDto>> GetRoleByIdAsync(int id);
    Task<Result<RoleDto>> GetRoleByNameAsync(string name);
    Task<Result<RoleDto>> CreateRoleAsync(CreateRoleRequest request);
    Task<Result<RoleDto>> UpdateRoleAsync(int id, UpdateRoleRequest request);
    Task<Result> DeleteRoleAsync(int id);
    Task<Result<List<RoleDto>>> GetRolesAsync();
    Task<Result> AssignPermissionAsync(int roleId, int permissionId);
    Task<Result> RemovePermissionAsync(int roleId, int permissionId);
}

public interface IPermissionService
{
    Task<Result<PermissionDto>> GetPermissionByIdAsync(int id);
    Task<Result<PermissionDto>> GetPermissionByNameAsync(string name);
    Task<Result<PermissionDto>> CreatePermissionAsync(CreatePermissionRequest request);
    Task<Result<PermissionDto>> UpdatePermissionAsync(int id, UpdatePermissionRequest request);
    Task<Result> DeletePermissionAsync(int id);
    Task<Result<List<PermissionDto>>> GetPermissionsAsync();
    Task<Result<List<PermissionDto>>> GetPermissionsByResourceAsync(string resource);
}

public interface ISecurityService
{
    Task<Result> LogSecurityEventAsync(SecurityEventType eventType, string description,
        int? userId = null, string? ipAddress = null, string? userAgent = null,
        SecurityEventSeverity severity = SecurityEventSeverity.Low, string? additionalData = null);

    Task<Result> LogLoginAttemptAsync(string username, bool isSuccessful, string ipAddress,
        string? userAgent = null, string? failureReason = null, int? userId = null);
    
    Task<Result<bool>> IsRateLimitedAsync(string action, string identifier, int maxAttempts, TimeSpan window);
    Task<Result<RateLimitInfo>> GetRateLimitInfoAsync(string action, string identifier);
    Task<Result> RecordRateLimitAttemptAsync(string action, string identifier);
    
    Task<Result<CsrfTokenResponse>> GenerateCsrfTokenAsync();
    Task<Result<bool>> ValidateCsrfTokenAsync(string token);
    
    Task<Result<SecurityConfigDto>> GetSecurityConfigAsync();
    Task<Result> UpdateSecurityConfigAsync(SecurityConfigDto config);
    
    Task<Result<PagedResult<SecurityEventDto>>> GetSecurityEventsAsync(int page, int pageSize, 
        SecurityEventType? eventType = null, SecurityEventSeverity? severity = null, 
        DateTime? fromDate = null, DateTime? toDate = null);
    
    Task<Result<PagedResult<LoginAttemptDto>>> GetLoginAttemptsAsync(int page, int pageSize, 
        bool? isSuccessful = null, DateTime? fromDate = null, DateTime? toDate = null);
}

public interface IJwtService
{
    string GenerateAccessToken(User user, List<string> roles, List<string> permissions);
    string GenerateRefreshToken();
    bool ValidateToken(string token);
    int? GetUserIdFromToken(string token);
    string? GetClaimFromToken(string token, string claimType);
    DateTime GetTokenExpiration(string token);
}

public interface IPasswordService
{
    string HashPassword(string password, out string salt);
    bool VerifyPassword(string password, string hash, string salt);
    bool ValidatePasswordStrength(string password, out List<string> errors);
    string GenerateRandomPassword(int length = 12);
    string GeneratePasswordResetToken();
}

// Additional DTOs for the interfaces
public class CreateUserRequest
{
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StrongPassword]
    public string Password { get; set; } = string.Empty;

    public List<int> RoleIds { get; set; } = new();
    public List<int> PermissionIds { get; set; } = new();
}

public class UpdateUserRequest
{
    [StringLength(100)]
    public string? FirstName { get; set; }

    [StringLength(100)]
    public string? LastName { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    public bool? IsActive { get; set; }
}

public class CreateRoleRequest
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string DisplayName { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public List<int> PermissionIds { get; set; } = new();
}

public class UpdateRoleRequest
{
    [StringLength(100)]
    public string? DisplayName { get; set; }

    [StringLength(500)]
    public string? Description { get; set; }

    public bool? IsActive { get; set; }
}

public class CreatePermissionRequest
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Resource { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string Action { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }
}

public class UpdatePermissionRequest
{
    [StringLength(500)]
    public string? Description { get; set; }
}
