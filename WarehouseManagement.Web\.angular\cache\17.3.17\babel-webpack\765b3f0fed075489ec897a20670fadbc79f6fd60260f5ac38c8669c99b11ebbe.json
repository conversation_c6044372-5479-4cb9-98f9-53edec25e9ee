{"ast": null, "code": "// Authentication and Authorization Models\nexport var PermissionAction;\n(function (PermissionAction) {\n  PermissionAction[\"CREATE\"] = \"create\";\n  PermissionAction[\"READ\"] = \"read\";\n  PermissionAction[\"UPDATE\"] = \"update\";\n  PermissionAction[\"DELETE\"] = \"delete\";\n  PermissionAction[\"MANAGE\"] = \"manage\";\n})(PermissionAction || (PermissionAction = {}));\nexport var UserRoleType;\n(function (UserRoleType) {\n  UserRoleType[\"ADMIN\"] = \"admin\";\n  UserRoleType[\"MANAGER\"] = \"manager\";\n  UserRoleType[\"EMPLOYEE\"] = \"employee\";\n  UserRoleType[\"VIEWER\"] = \"viewer\";\n})(UserRoleType || (UserRoleType = {}));\nexport var SecurityEventType;\n(function (SecurityEventType) {\n  SecurityEventType[\"LOGIN_SUCCESS\"] = \"login_success\";\n  SecurityEventType[\"LOGIN_FAILURE\"] = \"login_failure\";\n  SecurityEventType[\"LOGOUT\"] = \"logout\";\n  SecurityEventType[\"TOKEN_REFRESH\"] = \"token_refresh\";\n  SecurityEventType[\"PASSWORD_CHANGE\"] = \"password_change\";\n  SecurityEventType[\"ACCOUNT_LOCKED\"] = \"account_locked\";\n  SecurityEventType[\"SUSPICIOUS_ACTIVITY\"] = \"suspicious_activity\";\n  SecurityEventType[\"PERMISSION_DENIED\"] = \"permission_denied\";\n})(SecurityEventType || (SecurityEventType = {}));\nexport var SecurityEventSeverity;\n(function (SecurityEventSeverity) {\n  SecurityEventSeverity[\"LOW\"] = \"low\";\n  SecurityEventSeverity[\"MEDIUM\"] = \"medium\";\n  SecurityEventSeverity[\"HIGH\"] = \"high\";\n  SecurityEventSeverity[\"CRITICAL\"] = \"critical\";\n})(SecurityEventSeverity || (SecurityEventSeverity = {}));", "map": {"version": 3, "names": ["PermissionAction", "UserRoleType", "SecurityEventType", "SecurityEventSeverity"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\models\\auth.models.ts"], "sourcesContent": ["// Authentication and Authorization Models\n\nexport interface User {\n  id: string;\n  username: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  roles: UserRole[];\n  permissions: Permission[];\n  isActive: boolean;\n  lastLoginAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface UserRole {\n  id: string;\n  name: string;\n  displayName: string;\n  description: string;\n  permissions: Permission[];\n  isActive: boolean;\n}\n\nexport interface Permission {\n  id: string;\n  name: string;\n  resource: string;\n  action: PermissionAction;\n  description: string;\n}\n\nexport enum PermissionAction {\n  CREATE = 'create',\n  READ = 'read',\n  UPDATE = 'update',\n  DELETE = 'delete',\n  MANAGE = 'manage'\n}\n\nexport enum UserRoleType {\n  ADMIN = 'admin',\n  MANAGER = 'manager',\n  EMPLOYEE = 'employee',\n  VIEWER = 'viewer'\n}\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n  rememberMe?: boolean;\n}\n\nexport interface LoginResponse {\n  user: User;\n  accessToken: string;\n  refreshToken: string;\n  expiresIn: number;\n  tokenType: string;\n}\n\nexport interface RefreshTokenRequest {\n  refreshToken: string;\n}\n\nexport interface RefreshTokenResponse {\n  accessToken: string;\n  refreshToken: string;\n  expiresIn: number;\n}\n\nexport interface AuthState {\n  isAuthenticated: boolean;\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n}\n\nexport interface JwtPayload {\n  sub: string; // user id\n  username: string;\n  email: string;\n  roles: string[];\n  permissions: string[];\n  iat: number; // issued at\n  exp: number; // expiration time\n  jti: string; // JWT ID\n}\n\nexport interface SecurityConfig {\n  maxLoginAttempts: number;\n  lockoutDuration: number; // in minutes\n  tokenExpirationTime: number; // in minutes\n  refreshTokenExpirationTime: number; // in days\n  passwordMinLength: number;\n  passwordRequireSpecialChar: boolean;\n  passwordRequireNumber: boolean;\n  passwordRequireUppercase: boolean;\n}\n\nexport interface LoginAttempt {\n  ipAddress: string;\n  username: string;\n  timestamp: Date;\n  success: boolean;\n  userAgent: string;\n}\n\nexport interface SecurityEvent {\n  id: string;\n  userId?: string;\n  eventType: SecurityEventType;\n  description: string;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: Date;\n  severity: SecurityEventSeverity;\n}\n\nexport enum SecurityEventType {\n  LOGIN_SUCCESS = 'login_success',\n  LOGIN_FAILURE = 'login_failure',\n  LOGOUT = 'logout',\n  TOKEN_REFRESH = 'token_refresh',\n  PASSWORD_CHANGE = 'password_change',\n  ACCOUNT_LOCKED = 'account_locked',\n  SUSPICIOUS_ACTIVITY = 'suspicious_activity',\n  PERMISSION_DENIED = 'permission_denied'\n}\n\nexport enum SecurityEventSeverity {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  CRITICAL = 'critical'\n}\n\nexport interface CsrfToken {\n  token: string;\n  expiresAt: Date;\n}\n\nexport interface RateLimitInfo {\n  limit: number;\n  remaining: number;\n  resetTime: Date;\n}\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  errors?: string[];\n  rateLimitInfo?: RateLimitInfo;\n}\n"], "mappings": "AAAA;AAiCA,WAAYA,gBAMX;AAND,WAAYA,gBAAgB;EAC1BA,gBAAA,qBAAiB;EACjBA,gBAAA,iBAAa;EACbA,gBAAA,qBAAiB;EACjBA,gBAAA,qBAAiB;EACjBA,gBAAA,qBAAiB;AACnB,CAAC,EANWA,gBAAgB,KAAhBA,gBAAgB;AAQ5B,WAAYC,YAKX;AALD,WAAYA,YAAY;EACtBA,YAAA,mBAAe;EACfA,YAAA,uBAAmB;EACnBA,YAAA,yBAAqB;EACrBA,YAAA,qBAAiB;AACnB,CAAC,EALWA,YAAY,KAAZA,YAAY;AA+ExB,WAAYC,iBASX;AATD,WAAYA,iBAAiB;EAC3BA,iBAAA,mCAA+B;EAC/BA,iBAAA,mCAA+B;EAC/BA,iBAAA,qBAAiB;EACjBA,iBAAA,mCAA+B;EAC/BA,iBAAA,uCAAmC;EACnCA,iBAAA,qCAAiC;EACjCA,iBAAA,+CAA2C;EAC3CA,iBAAA,2CAAuC;AACzC,CAAC,EATWA,iBAAiB,KAAjBA,iBAAiB;AAW7B,WAAYC,qBAKX;AALD,WAAYA,qBAAqB;EAC/BA,qBAAA,eAAW;EACXA,qBAAA,qBAAiB;EACjBA,qBAAA,iBAAa;EACbA,qBAAA,yBAAqB;AACvB,CAAC,EALWA,qBAAqB,KAArBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}