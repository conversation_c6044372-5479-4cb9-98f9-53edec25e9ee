{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, take, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"../services/authorization.service\";\nimport * as i3 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(authService, authorizationService, router) {\n      this.authService = authService;\n      this.authorizationService = authorizationService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      return this.checkAuthentication(state.url);\n    }\n    canActivateChild(childRoute, state) {\n      return this.checkAuthentication(state.url);\n    }\n    canLoad(route, segments) {\n      const url = segments.map(segment => segment.path).join('/');\n      return this.checkAuthentication(`/${url}`);\n    }\n    checkAuthentication(url) {\n      return this.authService.authState$.pipe(take(1), map(authState => {\n        if (authState.loading) {\n          // Still loading, wait for authentication to complete\n          return false;\n        }\n        if (!authState.isAuthenticated) {\n          // Not authenticated, redirect to login\n          this.router.navigate(['/login'], {\n            queryParams: {\n              returnUrl: url\n            }\n          });\n          return false;\n        }\n        // Check if user has permission to access this route\n        if (!this.authorizationService.canAccessRoute(url)) {\n          // User doesn't have permission, redirect to unauthorized page\n          this.router.navigate(['/unauthorized']);\n          this.authorizationService.logPermissionDenied('route', 'access', url);\n          return false;\n        }\n        return true;\n      }), catchError(() => {\n        // Error occurred, redirect to login\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: url\n          }\n        });\n        return of(false);\n      }));\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(t) {\n        return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthGuard;\n})();\nexport let NoAuthGuard = /*#__PURE__*/(() => {\n  class NoAuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate() {\n      return this.authService.authState$.pipe(take(1), map(authState => {\n        if (authState.isAuthenticated) {\n          // User is already authenticated, redirect to dashboard\n          this.router.navigate(['/dashboard']);\n          return false;\n        }\n        return true;\n      }));\n    }\n    static {\n      this.ɵfac = function NoAuthGuard_Factory(t) {\n        return new (t || NoAuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i3.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NoAuthGuard,\n        factory: NoAuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NoAuthGuard;\n})();\nexport let RoleGuard = /*#__PURE__*/(() => {\n  class RoleGuard {\n    constructor(authService, authorizationService, router) {\n      this.authService = authService;\n      this.authorizationService = authorizationService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      return this.checkRole(route, state.url);\n    }\n    canActivateChild(childRoute, state) {\n      return this.checkRole(childRoute, state.url);\n    }\n    checkRole(route, url) {\n      const requiredRoles = route.data['roles'];\n      const requiredPermissions = route.data['permissions'];\n      return this.authService.authState$.pipe(take(1), map(authState => {\n        if (!authState.isAuthenticated) {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              returnUrl: url\n            }\n          });\n          return false;\n        }\n        // Check required roles\n        if (requiredRoles && requiredRoles.length > 0) {\n          const hasRequiredRole = requiredRoles.some(role => this.authorizationService.hasRole(role));\n          if (!hasRequiredRole) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied('role', requiredRoles.join(','), url);\n            return false;\n          }\n        }\n        // Check required permissions\n        if (requiredPermissions && requiredPermissions.length > 0) {\n          const hasRequiredPermissions = requiredPermissions.every(permission => this.authorizationService.hasPermission(permission.resource, permission.action));\n          if (!hasRequiredPermissions) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied('permission', requiredPermissions.map(p => `${p.action}:${p.resource}`).join(','), url);\n            return false;\n          }\n        }\n        return true;\n      }));\n    }\n    static {\n      this.ɵfac = function RoleGuard_Factory(t) {\n        return new (t || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RoleGuard,\n        factory: RoleGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RoleGuard;\n})();\nexport let PermissionGuard = /*#__PURE__*/(() => {\n  class PermissionGuard {\n    constructor(authService, authorizationService, router) {\n      this.authService = authService;\n      this.authorizationService = authorizationService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      return this.checkPermission(route, state.url);\n    }\n    canActivateChild(childRoute, state) {\n      return this.checkPermission(childRoute, state.url);\n    }\n    checkPermission(route, url) {\n      const resource = route.data['resource'];\n      const action = route.data['action'];\n      const permissions = route.data['permissions'];\n      return this.authService.authState$.pipe(take(1), map(authState => {\n        if (!authState.isAuthenticated) {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              returnUrl: url\n            }\n          });\n          return false;\n        }\n        // Check single permission\n        if (resource && action) {\n          if (!this.authorizationService.hasPermission(resource, action)) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied(resource, action, url);\n            return false;\n          }\n        }\n        // Check multiple permissions\n        if (permissions && permissions.length > 0) {\n          const hasAllPermissions = permissions.every(permission => this.authorizationService.hasPermission(permission.resource, permission.action));\n          if (!hasAllPermissions) {\n            this.router.navigate(['/unauthorized']);\n            this.authorizationService.logPermissionDenied('multiple', permissions.map(p => `${p.action}:${p.resource}`).join(','), url);\n            return false;\n          }\n        }\n        return true;\n      }));\n    }\n    static {\n      this.ɵfac = function PermissionGuard_Factory(t) {\n        return new (t || PermissionGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.AuthorizationService), i0.ɵɵinject(i3.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PermissionGuard,\n        factory: PermissionGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PermissionGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}