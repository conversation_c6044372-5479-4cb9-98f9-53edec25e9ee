{"ast": null, "code": "import { BehaviorSubject, throwError, of } from 'rxjs';\nimport { map, catchError, tap, finalize } from 'rxjs/operators';\nimport { SecurityEventType, SecurityEventSeverity } from '../models/auth.models';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl;\n    this.TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiration\n    this.authStateSubject = new BehaviorSubject({\n      isAuthenticated: false,\n      user: null,\n      loading: false,\n      error: null\n    });\n    this.authState$ = this.authStateSubject.asObservable();\n    this.initializeAuth();\n  }\n  /**\n   * Initialize authentication state on service creation\n   */\n  initializeAuth() {\n    this.updateAuthState({\n      loading: true\n    });\n    // Check if user is already authenticated\n    this.validateCurrentSession().subscribe({\n      next: isValid => {\n        if (isValid) {\n          this.getCurrentUser().subscribe({\n            next: user => {\n              this.updateAuthState({\n                isAuthenticated: true,\n                user,\n                loading: false,\n                error: null\n              });\n              this.scheduleTokenRefresh();\n            },\n            error: () => {\n              this.clearAuthState();\n            }\n          });\n        } else {\n          this.clearAuthState();\n        }\n      },\n      error: () => {\n        this.clearAuthState();\n      }\n    });\n  }\n  /**\n   * Login user with credentials\n   */\n  login(credentials) {\n    this.updateAuthState({\n      loading: true,\n      error: null\n    });\n    return this.http.post(`${this.API_URL}/auth/login`, credentials, {\n      withCredentials: true // Important for httpOnly cookies\n    }).pipe(map(response => {\n      if (!response.success || !response.data) {\n        throw new Error(response.message || 'Login failed');\n      }\n      return response.data;\n    }), tap(loginResponse => {\n      this.updateAuthState({\n        isAuthenticated: true,\n        user: loginResponse.user,\n        loading: false,\n        error: null\n      });\n      this.scheduleTokenRefresh();\n      this.logSecurityEvent(SecurityEventType.LOGIN_SUCCESS, 'User logged in successfully');\n    }), catchError(error => {\n      this.updateAuthState({\n        isAuthenticated: false,\n        user: null,\n        loading: false,\n        error: this.getErrorMessage(error)\n      });\n      this.logSecurityEvent(SecurityEventType.LOGIN_FAILURE, `Login failed: ${this.getErrorMessage(error)}`, SecurityEventSeverity.MEDIUM);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Logout user and clear session\n   */\n  logout() {\n    return this.http.post(`${this.API_URL}/auth/logout`, {}, {\n      withCredentials: true\n    }).pipe(tap(() => {\n      this.logSecurityEvent(SecurityEventType.LOGOUT, 'User logged out');\n    }), map(() => void 0),\n    // Convert to void\n    catchError(error => {\n      console.error('Logout error:', error);\n      // Even if logout fails on server, clear local state\n      this.clearAuthState();\n      this.router.navigate(['/login']);\n      return of(void 0);\n    }), finalize(() => {\n      this.clearAuthState();\n      this.router.navigate(['/login']);\n    }));\n  }\n  /**\n   * Refresh authentication token\n   */\n  refreshToken() {\n    return this.http.post(`${this.API_URL}/auth/refresh`, {}, {\n      withCredentials: true\n    }).pipe(map(response => {\n      if (!response.success || !response.data) {\n        throw new Error('Token refresh failed');\n      }\n      return response.data;\n    }), tap(() => {\n      this.scheduleTokenRefresh();\n      this.logSecurityEvent(SecurityEventType.TOKEN_REFRESH, 'Token refreshed successfully');\n    }), catchError(error => {\n      console.error('Token refresh failed:', error);\n      this.clearAuthState();\n      this.router.navigate(['/login']);\n      return throwError(() => error);\n    }));\n  }\n  /**\n   * Get current user information\n   */\n  getCurrentUser() {\n    return this.http.get(`${this.API_URL}/auth/me`, {\n      withCredentials: true\n    }).pipe(map(response => {\n      if (!response.success || !response.data) {\n        throw new Error('Failed to get user information');\n      }\n      return response.data;\n    }));\n  }\n  /**\n   * Validate current session\n   */\n  validateCurrentSession() {\n    return this.http.get(`${this.API_URL}/auth/validate`, {\n      withCredentials: true\n    }).pipe(map(response => response.success && response.data?.valid === true), catchError(() => {\n      return throwError(() => false);\n    }));\n  }\n  /**\n   * Check if user has specific permission\n   */\n  hasPermission(resource, action) {\n    const user = this.authStateSubject.value.user;\n    if (!user) return false;\n    // Admin has all permissions\n    if (user.roles.some(role => role.name === 'admin')) {\n      return true;\n    }\n    // Check specific permissions\n    return user.permissions.some(permission => permission.resource === resource && permission.action === action);\n  }\n  /**\n   * Check if user has specific role\n   */\n  hasRole(roleName) {\n    const user = this.authStateSubject.value.user;\n    return user?.roles.some(role => role.name === roleName) || false;\n  }\n  /**\n   * Get current authentication state\n   */\n  getAuthState() {\n    return this.authStateSubject.value;\n  }\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return this.authStateSubject.value.isAuthenticated;\n  }\n  /**\n   * Get current user\n   */\n  getCurrentUserSync() {\n    return this.authStateSubject.value.user;\n  }\n  /**\n   * Schedule automatic token refresh\n   */\n  scheduleTokenRefresh() {\n    if (this.refreshTokenTimer) {\n      clearTimeout(this.refreshTokenTimer);\n    }\n    // Schedule refresh 5 minutes before token expiration\n    this.refreshTokenTimer = setTimeout(() => {\n      this.refreshToken().subscribe({\n        error: error => {\n          console.error('Automatic token refresh failed:', error);\n        }\n      });\n    }, this.TOKEN_REFRESH_THRESHOLD);\n  }\n  /**\n   * Update authentication state\n   */\n  updateAuthState(updates) {\n    const currentState = this.authStateSubject.value;\n    this.authStateSubject.next({\n      ...currentState,\n      ...updates\n    });\n  }\n  /**\n   * Clear authentication state\n   */\n  clearAuthState() {\n    if (this.refreshTokenTimer) {\n      clearTimeout(this.refreshTokenTimer);\n    }\n    this.authStateSubject.next({\n      isAuthenticated: false,\n      user: null,\n      loading: false,\n      error: null\n    });\n  }\n  /**\n   * Log security events\n   */\n  logSecurityEvent(eventType, description, severity = SecurityEventSeverity.LOW) {\n    const event = {\n      eventType,\n      description,\n      severity,\n      timestamp: new Date()\n    };\n    // Send to backend for logging\n    this.http.post(`${this.API_URL}/security/events`, event, {\n      withCredentials: true\n    }).subscribe({\n      error: error => console.error('Failed to log security event:', error)\n    });\n  }\n  /**\n   * Extract error message from HTTP error\n   */\n  getErrorMessage(error) {\n    if (error?.error?.message) {\n      return error.error.message;\n    }\n    if (error?.message) {\n      return error.message;\n    }\n    return 'An unexpected error occurred';\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "throwError", "of", "map", "catchError", "tap", "finalize", "SecurityEventType", "SecurityEventSeverity", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "TOKEN_REFRESH_THRESHOLD", "authStateSubject", "isAuthenticated", "user", "loading", "error", "authState$", "asObservable", "initializeAuth", "updateAuthState", "validateCurrentSession", "subscribe", "next", "<PERSON><PERSON><PERSON><PERSON>", "getCurrentUser", "scheduleTokenRefresh", "clearAuthState", "login", "credentials", "post", "withCredentials", "pipe", "response", "success", "data", "Error", "message", "loginResponse", "logSecurityEvent", "LOGIN_SUCCESS", "getErrorMessage", "LOGIN_FAILURE", "MEDIUM", "logout", "LOGOUT", "console", "navigate", "refreshToken", "TOKEN_REFRESH", "get", "valid", "hasPermission", "resource", "action", "value", "roles", "some", "role", "name", "permissions", "permission", "hasRole", "<PERSON><PERSON><PERSON>", "getAuthState", "getCurrentUserSync", "refreshTokenTimer", "clearTimeout", "setTimeout", "updates", "currentState", "eventType", "description", "severity", "LOW", "event", "timestamp", "Date", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, timer, of } from 'rxjs';\nimport { map, catchError, tap, switchMap, finalize } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { \n  User, \n  LoginRequest, \n  LoginResponse, \n  RefreshTokenResponse, \n  AuthState, \n  JwtPayload,\n  ApiResponse,\n  SecurityEvent,\n  SecurityEventType,\n  SecurityEventSeverity\n} from '../models/auth.models';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl;\n  private readonly TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiration\n  \n  private authStateSubject = new BehaviorSubject<AuthState>({\n    isAuthenticated: false,\n    user: null,\n    loading: false,\n    error: null\n  });\n\n  public authState$ = this.authStateSubject.asObservable();\n  private refreshTokenTimer?: any;\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    this.initializeAuth();\n  }\n\n  /**\n   * Initialize authentication state on service creation\n   */\n  private initializeAuth(): void {\n    this.updateAuthState({ loading: true });\n    \n    // Check if user is already authenticated\n    this.validateCurrentSession().subscribe({\n      next: (isValid) => {\n        if (isValid) {\n          this.getCurrentUser().subscribe({\n            next: (user) => {\n              this.updateAuthState({\n                isAuthenticated: true,\n                user,\n                loading: false,\n                error: null\n              });\n              this.scheduleTokenRefresh();\n            },\n            error: () => {\n              this.clearAuthState();\n            }\n          });\n        } else {\n          this.clearAuthState();\n        }\n      },\n      error: () => {\n        this.clearAuthState();\n      }\n    });\n  }\n\n  /**\n   * Login user with credentials\n   */\n  login(credentials: LoginRequest): Observable<LoginResponse> {\n    this.updateAuthState({ loading: true, error: null });\n\n    return this.http.post<ApiResponse<LoginResponse>>(`${this.API_URL}/auth/login`, credentials, {\n      withCredentials: true // Important for httpOnly cookies\n    }).pipe(\n      map(response => {\n        if (!response.success || !response.data) {\n          throw new Error(response.message || 'Login failed');\n        }\n        return response.data;\n      }),\n      tap(loginResponse => {\n        this.updateAuthState({\n          isAuthenticated: true,\n          user: loginResponse.user,\n          loading: false,\n          error: null\n        });\n        \n        this.scheduleTokenRefresh();\n        this.logSecurityEvent(SecurityEventType.LOGIN_SUCCESS, 'User logged in successfully');\n      }),\n      catchError(error => {\n        this.updateAuthState({\n          isAuthenticated: false,\n          user: null,\n          loading: false,\n          error: this.getErrorMessage(error)\n        });\n        \n        this.logSecurityEvent(\n          SecurityEventType.LOGIN_FAILURE, \n          `Login failed: ${this.getErrorMessage(error)}`,\n          SecurityEventSeverity.MEDIUM\n        );\n        \n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Logout user and clear session\n   */\n  logout(): Observable<void> {\n    return this.http.post<ApiResponse<void>>(`${this.API_URL}/auth/logout`, {}, {\n      withCredentials: true\n    }).pipe(\n      tap(() => {\n        this.logSecurityEvent(SecurityEventType.LOGOUT, 'User logged out');\n      }),\n      map(() => void 0), // Convert to void\n      catchError(error => {\n        console.error('Logout error:', error);\n        // Even if logout fails on server, clear local state\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n        return of(void 0);\n      }),\n      finalize(() => {\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n      })\n    );\n  }\n\n  /**\n   * Refresh authentication token\n   */\n  refreshToken(): Observable<RefreshTokenResponse> {\n    return this.http.post<ApiResponse<RefreshTokenResponse>>(`${this.API_URL}/auth/refresh`, {}, {\n      withCredentials: true\n    }).pipe(\n      map(response => {\n        if (!response.success || !response.data) {\n          throw new Error('Token refresh failed');\n        }\n        return response.data;\n      }),\n      tap(() => {\n        this.scheduleTokenRefresh();\n        this.logSecurityEvent(SecurityEventType.TOKEN_REFRESH, 'Token refreshed successfully');\n      }),\n      catchError(error => {\n        console.error('Token refresh failed:', error);\n        this.clearAuthState();\n        this.router.navigate(['/login']);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  /**\n   * Get current user information\n   */\n  getCurrentUser(): Observable<User> {\n    return this.http.get<ApiResponse<User>>(`${this.API_URL}/auth/me`, {\n      withCredentials: true\n    }).pipe(\n      map(response => {\n        if (!response.success || !response.data) {\n          throw new Error('Failed to get user information');\n        }\n        return response.data;\n      })\n    );\n  }\n\n  /**\n   * Validate current session\n   */\n  validateCurrentSession(): Observable<boolean> {\n    return this.http.get<ApiResponse<{ valid: boolean }>>(`${this.API_URL}/auth/validate`, {\n      withCredentials: true\n    }).pipe(\n      map(response => response.success && response.data?.valid === true),\n      catchError(() => {\n        return throwError(() => false);\n      })\n    );\n  }\n\n  /**\n   * Check if user has specific permission\n   */\n  hasPermission(resource: string, action: string): boolean {\n    const user = this.authStateSubject.value.user;\n    if (!user) return false;\n\n    // Admin has all permissions\n    if (user.roles.some(role => role.name === 'admin')) {\n      return true;\n    }\n\n    // Check specific permissions\n    return user.permissions.some(permission => \n      permission.resource === resource && permission.action === action\n    );\n  }\n\n  /**\n   * Check if user has specific role\n   */\n  hasRole(roleName: string): boolean {\n    const user = this.authStateSubject.value.user;\n    return user?.roles.some(role => role.name === roleName) || false;\n  }\n\n  /**\n   * Get current authentication state\n   */\n  getAuthState(): AuthState {\n    return this.authStateSubject.value;\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.authStateSubject.value.isAuthenticated;\n  }\n\n  /**\n   * Get current user\n   */\n  getCurrentUserSync(): User | null {\n    return this.authStateSubject.value.user;\n  }\n\n  /**\n   * Schedule automatic token refresh\n   */\n  private scheduleTokenRefresh(): void {\n    if (this.refreshTokenTimer) {\n      clearTimeout(this.refreshTokenTimer);\n    }\n\n    // Schedule refresh 5 minutes before token expiration\n    this.refreshTokenTimer = setTimeout(() => {\n      this.refreshToken().subscribe({\n        error: (error) => {\n          console.error('Automatic token refresh failed:', error);\n        }\n      });\n    }, this.TOKEN_REFRESH_THRESHOLD);\n  }\n\n  /**\n   * Update authentication state\n   */\n  private updateAuthState(updates: Partial<AuthState>): void {\n    const currentState = this.authStateSubject.value;\n    this.authStateSubject.next({ ...currentState, ...updates });\n  }\n\n  /**\n   * Clear authentication state\n   */\n  private clearAuthState(): void {\n    if (this.refreshTokenTimer) {\n      clearTimeout(this.refreshTokenTimer);\n    }\n    \n    this.authStateSubject.next({\n      isAuthenticated: false,\n      user: null,\n      loading: false,\n      error: null\n    });\n  }\n\n  /**\n   * Log security events\n   */\n  private logSecurityEvent(\n    eventType: SecurityEventType, \n    description: string, \n    severity: SecurityEventSeverity = SecurityEventSeverity.LOW\n  ): void {\n    const event: Partial<SecurityEvent> = {\n      eventType,\n      description,\n      severity,\n      timestamp: new Date()\n    };\n\n    // Send to backend for logging\n    this.http.post(`${this.API_URL}/security/events`, event, {\n      withCredentials: true\n    }).subscribe({\n      error: (error) => console.error('Failed to log security event:', error)\n    });\n  }\n\n  /**\n   * Extract error message from HTTP error\n   */\n  private getErrorMessage(error: any): string {\n    if (error?.error?.message) {\n      return error.error.message;\n    }\n    if (error?.message) {\n      return error.message;\n    }\n    return 'An unexpected error occurred';\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,UAAU,EAASC,EAAE,QAAQ,MAAM;AACzE,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,EAAaC,QAAQ,QAAQ,gBAAgB;AAE1E,SASEC,iBAAiB,EACjBC,qBAAqB,QAChB,uBAAuB;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,WAAW;EActBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IAfC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IAC5B,KAAAC,uBAAuB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAElD,KAAAC,gBAAgB,GAAG,IAAIjB,eAAe,CAAY;MACxDkB,eAAe,EAAE,KAAK;MACtBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;KACR,CAAC;IAEK,KAAAC,UAAU,GAAG,IAAI,CAACL,gBAAgB,CAACM,YAAY,EAAE;IAOtD,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;EAGQA,cAAcA,CAAA;IACpB,IAAI,CAACC,eAAe,CAAC;MAAEL,OAAO,EAAE;IAAI,CAAE,CAAC;IAEvC;IACA,IAAI,CAACM,sBAAsB,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,OAAO,IAAI;QAChB,IAAIA,OAAO,EAAE;UACX,IAAI,CAACC,cAAc,EAAE,CAACH,SAAS,CAAC;YAC9BC,IAAI,EAAGT,IAAI,IAAI;cACb,IAAI,CAACM,eAAe,CAAC;gBACnBP,eAAe,EAAE,IAAI;gBACrBC,IAAI;gBACJC,OAAO,EAAE,KAAK;gBACdC,KAAK,EAAE;eACR,CAAC;cACF,IAAI,CAACU,oBAAoB,EAAE;YAC7B,CAAC;YACDV,KAAK,EAAEA,CAAA,KAAK;cACV,IAAI,CAACW,cAAc,EAAE;YACvB;WACD,CAAC;SACH,MAAM;UACL,IAAI,CAACA,cAAc,EAAE;;MAEzB,CAAC;MACDX,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACW,cAAc,EAAE;MACvB;KACD,CAAC;EACJ;EAEA;;;EAGAC,KAAKA,CAACC,WAAyB;IAC7B,IAAI,CAACT,eAAe,CAAC;MAAEL,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IAEpD,OAAO,IAAI,CAACT,IAAI,CAACuB,IAAI,CAA6B,GAAG,IAAI,CAACrB,OAAO,aAAa,EAAEoB,WAAW,EAAE;MAC3FE,eAAe,EAAE,IAAI,CAAC;KACvB,CAAC,CAACC,IAAI,CACLlC,GAAG,CAACmC,QAAQ,IAAG;MACb,IAAI,CAACA,QAAQ,CAACC,OAAO,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;QACvC,MAAM,IAAIC,KAAK,CAACH,QAAQ,CAACI,OAAO,IAAI,cAAc,CAAC;;MAErD,OAAOJ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,EACFnC,GAAG,CAACsC,aAAa,IAAG;MAClB,IAAI,CAAClB,eAAe,CAAC;QACnBP,eAAe,EAAE,IAAI;QACrBC,IAAI,EAAEwB,aAAa,CAACxB,IAAI;QACxBC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE;OACR,CAAC;MAEF,IAAI,CAACU,oBAAoB,EAAE;MAC3B,IAAI,CAACa,gBAAgB,CAACrC,iBAAiB,CAACsC,aAAa,EAAE,6BAA6B,CAAC;IACvF,CAAC,CAAC,EACFzC,UAAU,CAACiB,KAAK,IAAG;MACjB,IAAI,CAACI,eAAe,CAAC;QACnBP,eAAe,EAAE,KAAK;QACtBC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,KAAK;QACdC,KAAK,EAAE,IAAI,CAACyB,eAAe,CAACzB,KAAK;OAClC,CAAC;MAEF,IAAI,CAACuB,gBAAgB,CACnBrC,iBAAiB,CAACwC,aAAa,EAC/B,iBAAiB,IAAI,CAACD,eAAe,CAACzB,KAAK,CAAC,EAAE,EAC9Cb,qBAAqB,CAACwC,MAAM,CAC7B;MAED,OAAO/C,UAAU,CAAC,MAAMoB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGA4B,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACrC,IAAI,CAACuB,IAAI,CAAoB,GAAG,IAAI,CAACrB,OAAO,cAAc,EAAE,EAAE,EAAE;MAC1EsB,eAAe,EAAE;KAClB,CAAC,CAACC,IAAI,CACLhC,GAAG,CAAC,MAAK;MACP,IAAI,CAACuC,gBAAgB,CAACrC,iBAAiB,CAAC2C,MAAM,EAAE,iBAAiB,CAAC;IACpE,CAAC,CAAC,EACF/C,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;IAAE;IACnBC,UAAU,CAACiB,KAAK,IAAG;MACjB8B,OAAO,CAAC9B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACA,IAAI,CAACW,cAAc,EAAE;MACrB,IAAI,CAACnB,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,OAAOlD,EAAE,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC,EACFI,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC0B,cAAc,EAAE;MACrB,IAAI,CAACnB,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACzC,IAAI,CAACuB,IAAI,CAAoC,GAAG,IAAI,CAACrB,OAAO,eAAe,EAAE,EAAE,EAAE;MAC3FsB,eAAe,EAAE;KAClB,CAAC,CAACC,IAAI,CACLlC,GAAG,CAACmC,QAAQ,IAAG;MACb,IAAI,CAACA,QAAQ,CAACC,OAAO,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,OAAOH,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,EACFnC,GAAG,CAAC,MAAK;MACP,IAAI,CAAC0B,oBAAoB,EAAE;MAC3B,IAAI,CAACa,gBAAgB,CAACrC,iBAAiB,CAAC+C,aAAa,EAAE,8BAA8B,CAAC;IACxF,CAAC,CAAC,EACFlD,UAAU,CAACiB,KAAK,IAAG;MACjB8B,OAAO,CAAC9B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACW,cAAc,EAAE;MACrB,IAAI,CAACnB,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC,OAAOnD,UAAU,CAAC,MAAMoB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAS,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClB,IAAI,CAAC2C,GAAG,CAAoB,GAAG,IAAI,CAACzC,OAAO,UAAU,EAAE;MACjEsB,eAAe,EAAE;KAClB,CAAC,CAACC,IAAI,CACLlC,GAAG,CAACmC,QAAQ,IAAG;MACb,IAAI,CAACA,QAAQ,CAACC,OAAO,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE;QACvC,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOH,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAd,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACd,IAAI,CAAC2C,GAAG,CAAkC,GAAG,IAAI,CAACzC,OAAO,gBAAgB,EAAE;MACrFsB,eAAe,EAAE;KAClB,CAAC,CAACC,IAAI,CACLlC,GAAG,CAACmC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAEgB,KAAK,KAAK,IAAI,CAAC,EAClEpD,UAAU,CAAC,MAAK;MACd,OAAOH,UAAU,CAAC,MAAM,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAwD,aAAaA,CAACC,QAAgB,EAAEC,MAAc;IAC5C,MAAMxC,IAAI,GAAG,IAAI,CAACF,gBAAgB,CAAC2C,KAAK,CAACzC,IAAI;IAC7C,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IAEvB;IACA,IAAIA,IAAI,CAAC0C,KAAK,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,OAAO,CAAC,EAAE;MAClD,OAAO,IAAI;;IAGb;IACA,OAAO7C,IAAI,CAAC8C,WAAW,CAACH,IAAI,CAACI,UAAU,IACrCA,UAAU,CAACR,QAAQ,KAAKA,QAAQ,IAAIQ,UAAU,CAACP,MAAM,KAAKA,MAAM,CACjE;EACH;EAEA;;;EAGAQ,OAAOA,CAACC,QAAgB;IACtB,MAAMjD,IAAI,GAAG,IAAI,CAACF,gBAAgB,CAAC2C,KAAK,CAACzC,IAAI;IAC7C,OAAOA,IAAI,EAAE0C,KAAK,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKI,QAAQ,CAAC,IAAI,KAAK;EAClE;EAEA;;;EAGAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACpD,gBAAgB,CAAC2C,KAAK;EACpC;EAEA;;;EAGA1C,eAAeA,CAAA;IACb,OAAO,IAAI,CAACD,gBAAgB,CAAC2C,KAAK,CAAC1C,eAAe;EACpD;EAEA;;;EAGAoD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrD,gBAAgB,CAAC2C,KAAK,CAACzC,IAAI;EACzC;EAEA;;;EAGQY,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACwC,iBAAiB,EAAE;MAC1BC,YAAY,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGtC;IACA,IAAI,CAACA,iBAAiB,GAAGE,UAAU,CAAC,MAAK;MACvC,IAAI,CAACpB,YAAY,EAAE,CAAC1B,SAAS,CAAC;QAC5BN,KAAK,EAAGA,KAAK,IAAI;UACf8B,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACzD;OACD,CAAC;IACJ,CAAC,EAAE,IAAI,CAACL,uBAAuB,CAAC;EAClC;EAEA;;;EAGQS,eAAeA,CAACiD,OAA2B;IACjD,MAAMC,YAAY,GAAG,IAAI,CAAC1D,gBAAgB,CAAC2C,KAAK;IAChD,IAAI,CAAC3C,gBAAgB,CAACW,IAAI,CAAC;MAAE,GAAG+C,YAAY;MAAE,GAAGD;IAAO,CAAE,CAAC;EAC7D;EAEA;;;EAGQ1C,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACuC,iBAAiB,EAAE;MAC1BC,YAAY,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGtC,IAAI,CAACtD,gBAAgB,CAACW,IAAI,CAAC;MACzBV,eAAe,EAAE,KAAK;MACtBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;KACR,CAAC;EACJ;EAEA;;;EAGQuB,gBAAgBA,CACtBgC,SAA4B,EAC5BC,WAAmB,EACnBC,QAAA,GAAkCtE,qBAAqB,CAACuE,GAAG;IAE3D,MAAMC,KAAK,GAA2B;MACpCJ,SAAS;MACTC,WAAW;MACXC,QAAQ;MACRG,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED;IACA,IAAI,CAACtE,IAAI,CAACuB,IAAI,CAAC,GAAG,IAAI,CAACrB,OAAO,kBAAkB,EAAEkE,KAAK,EAAE;MACvD5C,eAAe,EAAE;KAClB,CAAC,CAACT,SAAS,CAAC;MACXN,KAAK,EAAGA,KAAK,IAAK8B,OAAO,CAAC9B,KAAK,CAAC,+BAA+B,EAAEA,KAAK;KACvE,CAAC;EACJ;EAEA;;;EAGQyB,eAAeA,CAACzB,KAAU;IAChC,IAAIA,KAAK,EAAEA,KAAK,EAAEqB,OAAO,EAAE;MACzB,OAAOrB,KAAK,CAACA,KAAK,CAACqB,OAAO;;IAE5B,IAAIrB,KAAK,EAAEqB,OAAO,EAAE;MAClB,OAAOrB,KAAK,CAACqB,OAAO;;IAEtB,OAAO,8BAA8B;EACvC;;;uBAhTWhC,WAAW,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAX9E,WAAW;MAAA+E,OAAA,EAAX/E,WAAW,CAAAgF,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}