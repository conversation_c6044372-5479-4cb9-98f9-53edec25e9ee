{"ast": null, "code": "import _asyncToGenerator from \"G:/CodeAgumnet/StoreAugments/src/WarehouseManagement.Web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Subject, interval, takeUntil } from 'rxjs';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ChartModule } from 'primeng/chart';\nimport { CardModule } from 'primeng/card';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { CoreModule } from '../../core/core.module';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../core/services/language.service\";\nimport * as i3 from \"../../core/services/authorization.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../core/directives/has-permission.directive\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"primeng/inputswitch\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/calendar\";\nimport * as i14 from \"primeng/chart\";\nconst _c0 = () => ({\n  \"width\": \"100%\"\n});\nconst _c1 = () => ({\n  label: \"All\",\n  value: \"\"\n});\nconst _c2 = () => ({\n  label: \"Login Success\",\n  value: \"LoginSuccess\"\n});\nconst _c3 = () => ({\n  label: \"Login Failure\",\n  value: \"LoginFailure\"\n});\nconst _c4 = () => ({\n  label: \"Logout\",\n  value: \"Logout\"\n});\nconst _c5 = () => ({\n  label: \"Account Locked\",\n  value: \"AccountLocked\"\n});\nconst _c6 = () => ({\n  label: \"Suspicious Activity\",\n  value: \"SuspiciousActivity\"\n});\nconst _c7 = (a0, a1, a2, a3, a4, a5) => [a0, a1, a2, a3, a4, a5];\nconst _c8 = () => ({\n  label: \"Critical\",\n  value: \"Critical\"\n});\nconst _c9 = () => ({\n  label: \"High\",\n  value: \"High\"\n});\nconst _c10 = () => ({\n  label: \"Medium\",\n  value: \"Medium\"\n});\nconst _c11 = () => ({\n  label: \"Low\",\n  value: \"Low\"\n});\nconst _c12 = (a0, a1, a2, a3, a4) => [a0, a1, a2, a3, a4];\nconst _c13 = () => ({\n  position: \"bottom\"\n});\nconst _c14 = a0 => ({\n  legend: a0\n});\nconst _c15 = a0 => ({\n  plugins: a0\n});\nfunction SecurityMonitoringComponent_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.exportData());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.export\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.timestamp\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.eventType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.severity\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.ipAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.actions\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 54);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵelement(15, \"button\", 55);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const event_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(event_r4.occurredAt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMapInterpolate1(\"pi \", ctx_r1.getEventTypeIcon(event_r4.eventType), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", event_r4.eventType, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"severity-badge \", ctx_r1.getSeverityClass(event_r4.severity), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", event_r4.severity, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r4.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.viewDetails\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.noEvents\"), \" \");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.timestamp\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.username\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.ipAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.failureReason\"));\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 57);\n    i0.ɵɵelement(7, \"i\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attempt_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(attempt_r5.attemptedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"success\", attempt_r5.isSuccessful)(\"failure\", !attempt_r5.isSuccessful);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"pi-check\", attempt_r5.isSuccessful)(\"pi-times\", !attempt_r5.isSuccessful);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", attempt_r5.isSuccessful ? ctx_r1.languageService.translate(\"security.monitoring.success\") : ctx_r1.languageService.translate(\"security.monitoring.failure\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(attempt_r5.failureReason || \"-\");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_ng_template_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.noAttempts\"), \" \");\n  }\n}\nfunction SecurityMonitoringComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h2\", 4);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"p-inputSwitch\", 6);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_inputSwitch_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.autoRefresh, $event) || (ctx_r1.autoRefresh = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function SecurityMonitoringComponent_div_0_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAutoRefresh());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.loadData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SecurityMonitoringComponent_div_0_button_7_Template, 1, 1, \"button\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"div\", 11)(11, \"div\", 12);\n    i0.ɵɵelement(12, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 14);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 15);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 10)(18, \"div\", 11)(19, \"div\", 16);\n    i0.ɵɵelement(20, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 18);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 15);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 10)(26, \"div\", 11)(27, \"div\", 19);\n    i0.ɵɵelement(28, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 15);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 10)(34, \"div\", 11)(35, \"div\", 22);\n    i0.ɵɵelement(36, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 24);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 15);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 10)(42, \"div\", 11)(43, \"div\", 25);\n    i0.ɵɵelement(44, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 27);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 15);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 10)(50, \"div\", 11)(51, \"div\", 28);\n    i0.ɵɵelement(52, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 30);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 15);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 31)(58, \"h5\");\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 32)(61, \"div\", 33)(62, \"label\", 34);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"p-dropdown\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_64_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.eventTypeFilter, $event) || (ctx_r1.eventTypeFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 33)(66, \"label\", 36);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p-dropdown\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_68_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.severityFilter, $event) || (ctx_r1.severityFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(69, \"div\", 33)(70, \"label\", 38);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"p-calendar\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SecurityMonitoringComponent_div_0_Template_p_calendar_ngModelChange_72_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dateRangeFilter.start, $event) || (ctx_r1.dateRangeFilter.start = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 40)(74, \"div\", 41)(75, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_75_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function SecurityMonitoringComponent_div_0_Template_button_click_76_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFilters());\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(77, \"div\", 9)(78, \"div\", 44)(79, \"div\", 45)(80, \"h5\");\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(82, \"p-chart\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 44)(84, \"div\", 45)(85, \"h5\");\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(87, \"p-chart\", 46);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(88, \"p-tabView\", 47);\n    i0.ɵɵlistener(\"onChange\", function SecurityMonitoringComponent_div_0_Template_p_tabView_onChange_88_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTabChange($event));\n    });\n    i0.ɵɵelementStart(89, \"p-tabPanel\", 48)(90, \"p-table\", 49);\n    i0.ɵɵtemplate(91, SecurityMonitoringComponent_div_0_ng_template_91_Template, 13, 6, \"ng-template\", 50)(92, SecurityMonitoringComponent_div_0_ng_template_92_Template, 16, 12, \"ng-template\", 51)(93, SecurityMonitoringComponent_div_0_ng_template_93_Template, 3, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(94, \"p-tabPanel\", 48)(95, \"p-table\", 49);\n    i0.ɵɵtemplate(96, SecurityMonitoringComponent_div_0_ng_template_96_Template, 11, 5, \"ng-template\", 50)(97, SecurityMonitoringComponent_div_0_ng_template_97_Template, 13, 13, \"ng-template\", 51)(98, SecurityMonitoringComponent_div_0_ng_template_98_Template, 3, 1, \"ng-template\", 52);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.title\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.autoRefresh);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.autoRefresh\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"loading\", ctx_r1.loading)(\"pTooltip\", ctx_r1.languageService.translate(\"security.monitoring.refresh\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"appHasRole\", \"admin\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.totalEvents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.totalEvents\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.criticalEvents);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.criticalEvents\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.successfulLogins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.successfulLogins\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.failedLogins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.failedLogins\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.uniqueIPs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.uniqueIPs\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.metrics.lockedAccounts);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.lockedAccounts\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.filters\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.eventType\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(54, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.eventTypeFilter);\n    i0.ɵɵproperty(\"options\", i0.ɵɵpureFunction6(61, _c7, i0.ɵɵpureFunction0(55, _c1), i0.ɵɵpureFunction0(56, _c2), i0.ɵɵpureFunction0(57, _c3), i0.ɵɵpureFunction0(58, _c4), i0.ɵɵpureFunction0(59, _c5), i0.ɵɵpureFunction0(60, _c6)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.severity\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(68, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.severityFilter);\n    i0.ɵɵproperty(\"options\", i0.ɵɵpureFunction5(74, _c12, i0.ɵɵpureFunction0(69, _c1), i0.ɵɵpureFunction0(70, _c8), i0.ɵɵpureFunction0(71, _c9), i0.ɵɵpureFunction0(72, _c10), i0.ɵɵpureFunction0(73, _c11)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.dateRange\"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(80, _c0));\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.dateRangeFilter.start);\n    i0.ɵɵproperty(\"showTime\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.languageService.translate(\"security.monitoring.apply\"));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r1.languageService.translate(\"security.monitoring.clear\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.eventsBySeverity\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r1.eventChartData)(\"options\", i0.ɵɵpureFunction1(84, _c15, i0.ɵɵpureFunction1(82, _c14, i0.ɵɵpureFunction0(81, _c13))));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.loginAttempts\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"data\", ctx_r1.loginChartData)(\"options\", i0.ɵɵpureFunction1(89, _c15, i0.ɵɵpureFunction1(87, _c14, i0.ɵɵpureFunction0(86, _c13))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.languageService.translate(\"security.monitoring.securityEvents\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.securityEvents)(\"paginator\", true)(\"rows\", 20)(\"loading\", ctx_r1.loading)(\"responsive\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.languageService.translate(\"security.monitoring.loginAttempts\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r1.loginAttempts)(\"paginator\", true)(\"rows\", 20)(\"loading\", ctx_r1.loading)(\"responsive\", true);\n  }\n}\nfunction SecurityMonitoringComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"i\", 61);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.languageService.translate(\"security.monitoring.accessDenied\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.languageService.translate(\"security.monitoring.accessDeniedMessage\"), \" \");\n  }\n}\nexport class SecurityMonitoringComponent {\n  constructor(http, languageService, authorizationService) {\n    this.http = http;\n    this.languageService = languageService;\n    this.authorizationService = authorizationService;\n    this.destroy$ = new Subject();\n    this.API_URL = environment.apiUrl;\n    // Data\n    this.securityEvents = [];\n    this.loginAttempts = [];\n    this.metrics = {\n      totalEvents: 0,\n      criticalEvents: 0,\n      failedLogins: 0,\n      successfulLogins: 0,\n      uniqueIPs: 0,\n      lockedAccounts: 0\n    };\n    // UI State\n    this.loading = false;\n    this.selectedTab = 0;\n    this.refreshInterval = 30000; // 30 seconds\n    this.autoRefresh = true;\n    // Filters\n    this.eventTypeFilter = '';\n    this.severityFilter = '';\n    this.dateRangeFilter = {\n      start: new Date(Date.now() - 24 * 60 * 60 * 1000),\n      end: new Date()\n    };\n  }\n  ngOnInit() {\n    this.loadData();\n    this.setupAutoRefresh();\n    this.initializeCharts();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupAutoRefresh() {\n    if (this.autoRefresh) {\n      interval(this.refreshInterval).pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.loadData();\n      });\n    }\n  }\n  loadData() {\n    this.loading = true;\n    Promise.all([this.loadSecurityEvents(), this.loadLoginAttempts(), this.loadMetrics()]).finally(() => {\n      this.loading = false;\n      this.updateCharts();\n    });\n  }\n  loadSecurityEvents() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const params = new URLSearchParams({\n          page: '1',\n          pageSize: '50',\n          ...(_this.eventTypeFilter && {\n            eventType: _this.eventTypeFilter\n          }),\n          ...(_this.severityFilter && {\n            severity: _this.severityFilter\n          }),\n          fromDate: _this.dateRangeFilter.start.toISOString(),\n          toDate: _this.dateRangeFilter.end.toISOString()\n        });\n        const response = yield _this.http.get(`${_this.API_URL}/security/events?${params}`).toPromise();\n        if (response.success) {\n          _this.securityEvents = response.data || [];\n        }\n      } catch (error) {\n        console.error('Error loading security events:', error);\n      }\n    })();\n  }\n  loadLoginAttempts() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const params = new URLSearchParams({\n          page: '1',\n          pageSize: '50',\n          fromDate: _this2.dateRangeFilter.start.toISOString(),\n          toDate: _this2.dateRangeFilter.end.toISOString()\n        });\n        const response = yield _this2.http.get(`${_this2.API_URL}/security/login-attempts?${params}`).toPromise();\n        if (response.success) {\n          _this2.loginAttempts = response.data || [];\n        }\n      } catch (error) {\n        console.error('Error loading login attempts:', error);\n      }\n    })();\n  }\n  loadMetrics() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Calculate metrics from loaded data\n      _this3.metrics = {\n        totalEvents: _this3.securityEvents.length,\n        criticalEvents: _this3.securityEvents.filter(e => e.severity === 'Critical').length,\n        failedLogins: _this3.loginAttempts.filter(a => !a.isSuccessful).length,\n        successfulLogins: _this3.loginAttempts.filter(a => a.isSuccessful).length,\n        uniqueIPs: new Set(_this3.loginAttempts.map(a => a.ipAddress)).size,\n        lockedAccounts: _this3.securityEvents.filter(e => e.eventType === 'AccountLocked').length\n      };\n    })();\n  }\n  initializeCharts() {\n    this.eventChartData = {\n      labels: ['Critical', 'High', 'Medium', 'Low'],\n      datasets: [{\n        data: [0, 0, 0, 0],\n        backgroundColor: ['#ef4444', '#f97316', '#eab308', '#22c55e']\n      }]\n    };\n    this.loginChartData = {\n      labels: ['Successful', 'Failed'],\n      datasets: [{\n        data: [0, 0],\n        backgroundColor: ['#22c55e', '#ef4444']\n      }]\n    };\n  }\n  updateCharts() {\n    // Update event severity chart\n    const severityCounts = {\n      Critical: this.securityEvents.filter(e => e.severity === 'Critical').length,\n      High: this.securityEvents.filter(e => e.severity === 'High').length,\n      Medium: this.securityEvents.filter(e => e.severity === 'Medium').length,\n      Low: this.securityEvents.filter(e => e.severity === 'Low').length\n    };\n    this.eventChartData = {\n      ...this.eventChartData,\n      datasets: [{\n        ...this.eventChartData.datasets[0],\n        data: [severityCounts.Critical, severityCounts.High, severityCounts.Medium, severityCounts.Low]\n      }]\n    };\n    // Update login attempts chart\n    this.loginChartData = {\n      ...this.loginChartData,\n      datasets: [{\n        ...this.loginChartData.datasets[0],\n        data: [this.metrics.successfulLogins, this.metrics.failedLogins]\n      }]\n    };\n  }\n  onTabChange(event) {\n    this.selectedTab = event.index;\n  }\n  onRefreshIntervalChange(interval) {\n    this.refreshInterval = interval * 1000;\n    this.setupAutoRefresh();\n  }\n  toggleAutoRefresh() {\n    this.autoRefresh = !this.autoRefresh;\n    if (this.autoRefresh) {\n      this.setupAutoRefresh();\n    }\n  }\n  applyFilters() {\n    this.loadData();\n  }\n  clearFilters() {\n    this.eventTypeFilter = '';\n    this.severityFilter = '';\n    this.dateRangeFilter = {\n      start: new Date(Date.now() - 24 * 60 * 60 * 1000),\n      end: new Date()\n    };\n    this.loadData();\n  }\n  exportData() {\n    const data = {\n      securityEvents: this.securityEvents,\n      loginAttempts: this.loginAttempts,\n      metrics: this.metrics,\n      exportedAt: new Date().toISOString()\n    };\n    const blob = new Blob([JSON.stringify(data, null, 2)], {\n      type: 'application/json'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n  getSeverityClass(severity) {\n    switch (severity.toLowerCase()) {\n      case 'critical':\n        return 'severity-critical';\n      case 'high':\n        return 'severity-high';\n      case 'medium':\n        return 'severity-medium';\n      case 'low':\n        return 'severity-low';\n      default:\n        return '';\n    }\n  }\n  getEventTypeIcon(eventType) {\n    switch (eventType.toLowerCase()) {\n      case 'loginsuccess':\n        return 'pi-check-circle';\n      case 'loginfailure':\n        return 'pi-times-circle';\n      case 'logout':\n        return 'pi-sign-out';\n      case 'accountlocked':\n        return 'pi-lock';\n      case 'suspiciousactivity':\n        return 'pi-exclamation-triangle';\n      case 'permissiondenied':\n        return 'pi-ban';\n      default:\n        return 'pi-info-circle';\n    }\n  }\n  formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleString(this.languageService.isArabic() ? 'ar-SA' : 'en-US');\n  }\n  // Permission checks\n  canViewSecurityEvents() {\n    return this.authorizationService.hasPermission('audit_logs', 'read');\n  }\n  canExportData() {\n    return this.authorizationService.hasRole('admin');\n  }\n  static {\n    this.ɵfac = function SecurityMonitoringComponent_Factory(t) {\n      return new (t || SecurityMonitoringComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.LanguageService), i0.ɵɵdirectiveInject(i3.AuthorizationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SecurityMonitoringComponent,\n      selectors: [[\"app-security-monitoring\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"security-monitoring\", 4, \"appHasPermission\"], [\"class\", \"text-center p-6\", 4, \"appIsAuthenticated\"], [1, \"security-monitoring\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"m-0\"], [1, \"flex\", \"gap-2\"], [3, \"ngModelChange\", \"onChange\", \"ngModel\", \"pTooltip\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-refresh\", 1, \"p-button-outlined\", 3, \"click\", \"loading\", \"pTooltip\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-download\", \"class\", \"p-button-outlined\", 3, \"pTooltip\", \"click\", 4, \"appHasRole\"], [1, \"grid\", \"mb-4\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [1, \"card\", \"text-center\"], [1, \"text-blue-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-shield\"], [1, \"text-2xl\", \"font-bold\", \"text-blue-600\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"text-red-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-exclamation-triangle\"], [1, \"text-2xl\", \"font-bold\", \"text-red-600\"], [1, \"text-green-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-check-circle\"], [1, \"text-2xl\", \"font-bold\", \"text-green-600\"], [1, \"text-orange-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-times-circle\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-600\"], [1, \"text-purple-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-globe\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-600\"], [1, \"text-yellow-500\", \"text-3xl\", \"mb-2\"], [1, \"pi\", \"pi-lock\"], [1, \"text-2xl\", \"font-bold\", \"text-yellow-600\"], [1, \"card\", \"mb-4\"], [1, \"grid\"], [1, \"col-12\", \"md:col-3\"], [\"for\", \"eventType\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"eventType\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"ngModel\", \"options\"], [\"for\", \"severity\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"severity\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"ngModel\", \"options\"], [\"for\", \"dateRange\", 1, \"block\", \"text-sm\", \"font-medium\", \"mb-2\"], [\"id\", \"dateRange\", 3, \"ngModelChange\", \"ngModel\", \"showTime\"], [1, \"col-12\", \"md:col-3\", \"flex\", \"align-items-end\"], [1, \"flex\", \"gap-2\", \"w-full\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-search\", 1, \"p-button-primary\", \"flex-1\", 3, \"click\", \"label\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times\", 1, \"p-button-outlined\", \"flex-1\", 3, \"click\", \"label\"], [1, \"col-12\", \"md:col-6\"], [1, \"card\"], [\"type\", \"doughnut\", 3, \"data\", \"options\"], [3, \"onChange\"], [3, \"header\"], [3, \"value\", \"paginator\", \"rows\", \"loading\", \"responsive\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-download\", 1, \"p-button-outlined\", 3, \"click\", \"pTooltip\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-eye\", 1, \"p-button-rounded\", \"p-button-text\", 3, \"pTooltip\"], [\"colspan\", \"6\", 1, \"text-center\"], [1, \"status-badge\"], [1, \"pi\"], [\"colspan\", \"5\", 1, \"text-center\"], [1, \"text-center\", \"p-6\"], [1, \"pi\", \"pi-lock\", \"text-6xl\", \"text-gray-400\", \"mb-3\"], [1, \"text-gray-600\"]],\n      template: function SecurityMonitoringComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SecurityMonitoringComponent_div_0_Template, 99, 91, \"div\", 0)(1, SecurityMonitoringComponent_div_1_Template, 6, 2, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"appHasPermission\", \"audit_logs:read\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"appIsAuthenticated\", false);\n        }\n      },\n      dependencies: [CommonModule, FormsModule, i4.NgControlStatus, i4.NgModel, CoreModule, i5.HasPermissionDirective, i5.HasRoleDirective, i5.IsAuthenticatedDirective, ButtonModule, i6.ButtonDirective, i7.PrimeTemplate, InputSwitchModule, i8.InputSwitch, TooltipModule, i9.Tooltip, TableModule, i10.Table, TabViewModule, i11.TabView, i11.TabPanel, DropdownModule, i12.Dropdown, CalendarModule, i13.Calendar, ChartModule, i14.UIChart, CardModule, ProgressSpinnerModule],\n      styles: [\".security-monitoring[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  padding: 1.5rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid #e5e7eb;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 1rem;\\n  color: #374151;\\n  font-weight: 600;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .severity-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 9999px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-critical[_ngcontent-%COMP%] {\\n  background-color: #fee2e2;\\n  color: #dc2626;\\n  border: 1px solid #fecaca;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-high[_ngcontent-%COMP%] {\\n  background-color: #fed7aa;\\n  color: #ea580c;\\n  border: 1px solid #fdba74;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-medium[_ngcontent-%COMP%] {\\n  background-color: #fef3c7;\\n  color: #d97706;\\n  border: 1px solid #fde68a;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .severity-badge.severity-low[_ngcontent-%COMP%] {\\n  background-color: #dcfce7;\\n  color: #16a34a;\\n  border: 1px solid #bbf7d0;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 9999px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .status-badge.success[_ngcontent-%COMP%] {\\n  background-color: #dcfce7;\\n  color: #16a34a;\\n  border: 1px solid #bbf7d0;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .status-badge.failure[_ngcontent-%COMP%] {\\n  background-color: #fee2e2;\\n  color: #dc2626;\\n  border: 1px solid #fecaca;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-3xl[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-2xl[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-header {\\n  background: #f9fafb;\\n  border: 1px solid #e5e7eb;\\n  padding: 1rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-thead > tr > th {\\n  background: #f9fafb;\\n  color: #374151;\\n  font-weight: 600;\\n  border-bottom: 1px solid #e5e7eb;\\n  padding: 0.75rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr > td {\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #f3f4f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr:hover {\\n  background: #f9fafb;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-emptymessage > td {\\n  text-align: center;\\n  padding: 2rem;\\n  color: #6b7280;\\n  font-style: italic;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-paginator {\\n  background: #f9fafb;\\n  border: 1px solid #e5e7eb;\\n  border-top: none;\\n  padding: 0.75rem 1rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page {\\n  color: #374151;\\n  border: 1px solid #d1d5db;\\n  margin: 0 0.125rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {\\n  background: #3b82f6;\\n  border-color: #3b82f6;\\n  color: white;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-paginator .p-paginator-pages .p-paginator-page:hover:not(.p-highlight) {\\n  background: #f3f4f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav {\\n  background: #f9fafb;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  color: #6b7280;\\n  border: none;\\n  padding: 1rem 1.5rem;\\n  font-weight: 500;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link:hover {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  color: #3b82f6;\\n  border-bottom: 2px solid #3b82f6;\\n  background: white;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-panels {\\n  background: white;\\n  padding: 1.5rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-chart canvas {\\n  max-height: 300px;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-dropdown {\\n  border: 1px solid #d1d5db;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border-color: #9ca3af;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-dropdown.p-focus {\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 1px #3b82f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext {\\n  border: 1px solid #d1d5db;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext:hover {\\n  border-color: #9ca3af;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-calendar .p-inputtext:focus {\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 1px #3b82f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-outlined {\\n  border: 1px solid #d1d5db;\\n  color: #374151;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-outlined:hover {\\n  background: #f3f4f6;\\n  border-color: #9ca3af;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-primary {\\n  background: #3b82f6;\\n  border-color: #3b82f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-primary:hover {\\n  background: #2563eb;\\n  border-color: #2563eb;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-rounded {\\n  border-radius: 50%;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-text {\\n  color: #6b7280;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-button.p-button-text:hover {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {\\n  background: #3b82f6;\\n}\\n.security-monitoring   [_nghost-%COMP%]     .p-inputswitch .p-inputswitch-slider {\\n  background: #d1d5db;\\n}\\n@media (max-width: 768px) {\\n  .security-monitoring[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .security-monitoring[_ngcontent-%COMP%]   .grid[_ngcontent-%COMP%]   .col-12[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n  }\\n  .security-monitoring[_ngcontent-%COMP%]   .flex.gap-2[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .security-monitoring[_ngcontent-%COMP%]   .flex.gap-2[_ngcontent-%COMP%]   .flex-1[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .flex[_ngcontent-%COMP%] {\\n  flex-direction: row-reverse;\\n}\\n.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .text-left[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n.security-monitoring[dir=rtl][_ngcontent-%COMP%]   .text-right[_ngcontent-%COMP%] {\\n  text-align: left;\\n}\\n.security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-datatable-thead > tr > th, .security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-datatable-tbody > tr > td {\\n  text-align: right;\\n}\\n.security-monitoring[dir=rtl]   [_nghost-%COMP%]     .p-tabview-nav li {\\n  margin-left: 0;\\n  margin-right: 0.25rem;\\n}\\n@media (prefers-color-scheme: dark) {\\n  .security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n    background: #1f2937;\\n    border-color: #374151;\\n    color: #f9fafb;\\n  }\\n  .security-monitoring[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    color: #f9fafb;\\n  }\\n  .security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-header, .security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-thead > tr > th {\\n    background: #374151;\\n    color: #f9fafb;\\n    border-color: #4b5563;\\n  }\\n  .security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr > td {\\n    border-color: #4b5563;\\n    color: #f9fafb;\\n  }\\n  .security-monitoring   [_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr:hover {\\n    background: #374151;\\n  }\\n  .security-monitoring   [_nghost-%COMP%]     .p-paginator {\\n    background: #374151;\\n    border-color: #4b5563;\\n    color: #f9fafb;\\n  }\\n  .security-monitoring   [_nghost-%COMP%]     .p-tabview .p-tabview-nav {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "Subject", "interval", "takeUntil", "ButtonModule", "InputSwitchModule", "TooltipModule", "TableModule", "TabViewModule", "DropdownModule", "CalendarModule", "ChartModule", "CardModule", "ProgressSpinnerModule", "CoreModule", "environment", "i0", "ɵɵelementStart", "ɵɵlistener", "SecurityMonitoringComponent_div_0_button_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "exportData", "ɵɵelementEnd", "ɵɵproperty", "languageService", "translate", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵelement", "formatDate", "event_r4", "occurredAt", "ɵɵclassMapInterpolate1", "getEventTypeIcon", "eventType", "ɵɵtextInterpolate1", "getSeverityClass", "severity", "description", "ip<PERSON><PERSON><PERSON>", "attempt_r5", "attemptedAt", "username", "ɵɵclassProp", "isSuccessful", "failureReason", "ɵɵtwoWayListener", "SecurityMonitoringComponent_div_0_Template_p_inputSwitch_ngModelChange_5_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "autoRefresh", "SecurityMonitoringComponent_div_0_Template_p_inputSwitch_onChange_5_listener", "toggleAutoRefresh", "SecurityMonitoringComponent_div_0_Template_button_click_6_listener", "loadData", "ɵɵtemplate", "SecurityMonitoringComponent_div_0_button_7_Template", "SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_64_listener", "eventTypeFilter", "SecurityMonitoringComponent_div_0_Template_p_dropdown_ngModelChange_68_listener", "severityFilter", "SecurityMonitoringComponent_div_0_Template_p_calendar_ngModelChange_72_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start", "SecurityMonitoringComponent_div_0_Template_button_click_75_listener", "applyFilters", "SecurityMonitoringComponent_div_0_Template_button_click_76_listener", "clearFilters", "SecurityMonitoringComponent_div_0_Template_p_tabView_onChange_88_listener", "onTabChange", "SecurityMonitoringComponent_div_0_ng_template_91_Template", "SecurityMonitoringComponent_div_0_ng_template_92_Template", "SecurityMonitoringComponent_div_0_ng_template_93_Template", "SecurityMonitoringComponent_div_0_ng_template_96_Template", "SecurityMonitoringComponent_div_0_ng_template_97_Template", "SecurityMonitoringComponent_div_0_ng_template_98_Template", "ɵɵtwoWayProperty", "loading", "metrics", "totalEvents", "criticalEvents", "successfulLogins", "failed<PERSON>ogins", "uniqueIPs", "lockedAccounts", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction6", "_c7", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "ɵɵpureFunction5", "_c12", "_c8", "_c9", "_c10", "_c11", "ɵɵpropertyInterpolate", "eventChartData", "ɵɵpureFunction1", "_c15", "_c14", "_c13", "loginChartData", "securityEvents", "loginAttempts", "SecurityMonitoringComponent", "constructor", "http", "authorizationService", "destroy$", "API_URL", "apiUrl", "selectedTab", "refreshInterval", "Date", "now", "end", "ngOnInit", "setupAutoRefresh", "initializeCharts", "ngOnDestroy", "next", "complete", "pipe", "subscribe", "Promise", "all", "loadSecurityEvents", "loadLoginAttempts", "loadMetrics", "finally", "updateCharts", "_this", "_asyncToGenerator", "params", "URLSearchParams", "page", "pageSize", "fromDate", "toISOString", "toDate", "response", "get", "to<PERSON>romise", "success", "data", "error", "console", "_this2", "_this3", "length", "filter", "e", "a", "Set", "map", "size", "labels", "datasets", "backgroundColor", "severityCounts", "Critical", "High", "Medium", "Low", "event", "index", "onRefreshIntervalChange", "exportedAt", "blob", "Blob", "JSON", "stringify", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "click", "revokeObjectURL", "toLowerCase", "date", "d", "toLocaleString", "isArabic", "canViewSecurityEvents", "hasPermission", "canExportData", "hasRole", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "LanguageService", "i3", "AuthorizationService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SecurityMonitoringComponent_Template", "rf", "ctx", "SecurityMonitoringComponent_div_0_Template", "SecurityMonitoringComponent_div_1_Template", "i4", "NgControlStatus", "NgModel", "i5", "HasPermissionDirective", "HasRoleDirective", "IsAuthenticatedDirective", "i6", "ButtonDirective", "i7", "PrimeTemplate", "i8", "InputSwitch", "i9", "<PERSON><PERSON><PERSON>", "i10", "Table", "i11", "TabView", "TabPanel", "i12", "Dropdown", "i13", "Calendar", "i14", "UIChart", "styles"], "sources": ["G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\security\\security-monitoring.component.ts", "G:\\CodeAgumnet\\StoreAugments\\src\\WarehouseManagement.Web\\src\\app\\features\\security\\security-monitoring.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClient } from '@angular/common/http';\nimport { Subject, interval, takeUntil } from 'rxjs';\n\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { TableModule } from 'primeng/table';\nimport { TabViewModule } from 'primeng/tabview';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { CalendarModule } from 'primeng/calendar';\nimport { ChartModule } from 'primeng/chart';\nimport { CardModule } from 'primeng/card';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\n\n// Core Services and Module\nimport { LanguageService } from '../../core/services/language.service';\nimport { AuthorizationService } from '../../core/services/authorization.service';\nimport { CoreModule } from '../../core/core.module';\nimport { environment } from '../../../environments/environment';\n\ninterface SecurityEvent {\n  id: string;\n  userId?: string;\n  eventType: string;\n  description: string;\n  ipAddress: string;\n  userAgent?: string;\n  severity: string;\n  occurredAt: Date;\n  additionalData?: string;\n}\n\ninterface LoginAttempt {\n  id: string;\n  userId?: string;\n  username: string;\n  ipAddress: string;\n  userAgent?: string;\n  isSuccessful: boolean;\n  failureReason?: string;\n  attemptedAt: Date;\n}\n\ninterface SecurityMetrics {\n  totalEvents: number;\n  criticalEvents: number;\n  failedLogins: number;\n  successfulLogins: number;\n  uniqueIPs: number;\n  lockedAccounts: number;\n}\n\n@Component({\n  selector: 'app-security-monitoring',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    CoreModule,\n    ButtonModule,\n    InputSwitchModule,\n    TooltipModule,\n    TableModule,\n    TabViewModule,\n    DropdownModule,\n    CalendarModule,\n    ChartModule,\n    CardModule,\n    ProgressSpinnerModule\n  ],\n  templateUrl: './security-monitoring.component.html',\n  styleUrls: ['./security-monitoring.component.scss']\n})\nexport class SecurityMonitoringComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  private readonly API_URL = environment.apiUrl;\n\n  // Data\n  securityEvents: SecurityEvent[] = [];\n  loginAttempts: LoginAttempt[] = [];\n  metrics: SecurityMetrics = {\n    totalEvents: 0,\n    criticalEvents: 0,\n    failedLogins: 0,\n    successfulLogins: 0,\n    uniqueIPs: 0,\n    lockedAccounts: 0\n  };\n\n  // UI State\n  loading = false;\n  selectedTab = 0;\n  refreshInterval = 30000; // 30 seconds\n  autoRefresh = true;\n\n  // Filters\n  eventTypeFilter = '';\n  severityFilter = '';\n  dateRangeFilter = {\n    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours\n    end: new Date()\n  };\n\n  // Chart data\n  eventChartData: any;\n  loginChartData: any;\n\n  constructor(\n    private http: HttpClient,\n    public languageService: LanguageService,\n    private authorizationService: AuthorizationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadData();\n    this.setupAutoRefresh();\n    this.initializeCharts();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefresh) {\n      interval(this.refreshInterval)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe(() => {\n          this.loadData();\n        });\n    }\n  }\n\n  loadData(): void {\n    this.loading = true;\n    \n    Promise.all([\n      this.loadSecurityEvents(),\n      this.loadLoginAttempts(),\n      this.loadMetrics()\n    ]).finally(() => {\n      this.loading = false;\n      this.updateCharts();\n    });\n  }\n\n  private async loadSecurityEvents(): Promise<void> {\n    try {\n      const params = new URLSearchParams({\n        page: '1',\n        pageSize: '50',\n        ...(this.eventTypeFilter && { eventType: this.eventTypeFilter }),\n        ...(this.severityFilter && { severity: this.severityFilter }),\n        fromDate: this.dateRangeFilter.start.toISOString(),\n        toDate: this.dateRangeFilter.end.toISOString()\n      });\n\n      const response = await this.http.get<any>(`${this.API_URL}/security/events?${params}`)\n        .toPromise();\n\n      if (response.success) {\n        this.securityEvents = response.data || [];\n      }\n    } catch (error) {\n      console.error('Error loading security events:', error);\n    }\n  }\n\n  private async loadLoginAttempts(): Promise<void> {\n    try {\n      const params = new URLSearchParams({\n        page: '1',\n        pageSize: '50',\n        fromDate: this.dateRangeFilter.start.toISOString(),\n        toDate: this.dateRangeFilter.end.toISOString()\n      });\n\n      const response = await this.http.get<any>(`${this.API_URL}/security/login-attempts?${params}`)\n        .toPromise();\n\n      if (response.success) {\n        this.loginAttempts = response.data || [];\n      }\n    } catch (error) {\n      console.error('Error loading login attempts:', error);\n    }\n  }\n\n  private async loadMetrics(): Promise<void> {\n    // Calculate metrics from loaded data\n    this.metrics = {\n      totalEvents: this.securityEvents.length,\n      criticalEvents: this.securityEvents.filter(e => e.severity === 'Critical').length,\n      failedLogins: this.loginAttempts.filter(a => !a.isSuccessful).length,\n      successfulLogins: this.loginAttempts.filter(a => a.isSuccessful).length,\n      uniqueIPs: new Set(this.loginAttempts.map(a => a.ipAddress)).size,\n      lockedAccounts: this.securityEvents.filter(e => e.eventType === 'AccountLocked').length\n    };\n  }\n\n  private initializeCharts(): void {\n    this.eventChartData = {\n      labels: ['Critical', 'High', 'Medium', 'Low'],\n      datasets: [{\n        data: [0, 0, 0, 0],\n        backgroundColor: ['#ef4444', '#f97316', '#eab308', '#22c55e']\n      }]\n    };\n\n    this.loginChartData = {\n      labels: ['Successful', 'Failed'],\n      datasets: [{\n        data: [0, 0],\n        backgroundColor: ['#22c55e', '#ef4444']\n      }]\n    };\n  }\n\n  private updateCharts(): void {\n    // Update event severity chart\n    const severityCounts = {\n      Critical: this.securityEvents.filter(e => e.severity === 'Critical').length,\n      High: this.securityEvents.filter(e => e.severity === 'High').length,\n      Medium: this.securityEvents.filter(e => e.severity === 'Medium').length,\n      Low: this.securityEvents.filter(e => e.severity === 'Low').length\n    };\n\n    this.eventChartData = {\n      ...this.eventChartData,\n      datasets: [{\n        ...this.eventChartData.datasets[0],\n        data: [severityCounts.Critical, severityCounts.High, severityCounts.Medium, severityCounts.Low]\n      }]\n    };\n\n    // Update login attempts chart\n    this.loginChartData = {\n      ...this.loginChartData,\n      datasets: [{\n        ...this.loginChartData.datasets[0],\n        data: [this.metrics.successfulLogins, this.metrics.failedLogins]\n      }]\n    };\n  }\n\n  onTabChange(event: any): void {\n    this.selectedTab = event.index;\n  }\n\n  onRefreshIntervalChange(interval: number): void {\n    this.refreshInterval = interval * 1000;\n    this.setupAutoRefresh();\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefresh = !this.autoRefresh;\n    if (this.autoRefresh) {\n      this.setupAutoRefresh();\n    }\n  }\n\n  applyFilters(): void {\n    this.loadData();\n  }\n\n  clearFilters(): void {\n    this.eventTypeFilter = '';\n    this.severityFilter = '';\n    this.dateRangeFilter = {\n      start: new Date(Date.now() - 24 * 60 * 60 * 1000),\n      end: new Date()\n    };\n    this.loadData();\n  }\n\n  exportData(): void {\n    const data = {\n      securityEvents: this.securityEvents,\n      loginAttempts: this.loginAttempts,\n      metrics: this.metrics,\n      exportedAt: new Date().toISOString()\n    };\n\n    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n\n  getSeverityClass(severity: string): string {\n    switch (severity.toLowerCase()) {\n      case 'critical': return 'severity-critical';\n      case 'high': return 'severity-high';\n      case 'medium': return 'severity-medium';\n      case 'low': return 'severity-low';\n      default: return '';\n    }\n  }\n\n  getEventTypeIcon(eventType: string): string {\n    switch (eventType.toLowerCase()) {\n      case 'loginsuccess': return 'pi-check-circle';\n      case 'loginfailure': return 'pi-times-circle';\n      case 'logout': return 'pi-sign-out';\n      case 'accountlocked': return 'pi-lock';\n      case 'suspiciousactivity': return 'pi-exclamation-triangle';\n      case 'permissiondenied': return 'pi-ban';\n      default: return 'pi-info-circle';\n    }\n  }\n\n  formatDate(date: Date | string): string {\n    const d = new Date(date);\n    return d.toLocaleString(this.languageService.isArabic() ? 'ar-SA' : 'en-US');\n  }\n\n  // Permission checks\n  canViewSecurityEvents(): boolean {\n    return this.authorizationService.hasPermission('audit_logs', 'read');\n  }\n\n  canExportData(): boolean {\n    return this.authorizationService.hasRole('admin');\n  }\n}\n", "<div class=\"security-monitoring\" *appHasPermission=\"'audit_logs:read'\">\n  <!-- Header -->\n  <div class=\"flex justify-content-between align-items-center mb-4\">\n    <h2 class=\"text-2xl font-bold m-0\">\n      {{ languageService.translate('security.monitoring.title') }}\n    </h2>\n    \n    <div class=\"flex gap-2\">\n      <!-- Auto Refresh Toggle -->\n      <p-inputSwitch \n        [(ngModel)]=\"autoRefresh\" \n        (onChange)=\"toggleAutoRefresh()\"\n        [pTooltip]=\"languageService.translate('security.monitoring.autoRefresh')\">\n      </p-inputSwitch>\n      \n      <!-- Refresh Button -->\n      <button pButton \n              type=\"button\" \n              icon=\"pi pi-refresh\" \n              class=\"p-button-outlined\"\n              [loading]=\"loading\"\n              (click)=\"loadData()\"\n              [pTooltip]=\"languageService.translate('security.monitoring.refresh')\">\n      </button>\n      \n      <!-- Export Button -->\n      <button *appHasRole=\"'admin'\"\n              pButton \n              type=\"button\" \n              icon=\"pi pi-download\" \n              class=\"p-button-outlined\"\n              (click)=\"exportData()\"\n              [pTooltip]=\"languageService.translate('security.monitoring.export')\">\n      </button>\n    </div>\n  </div>\n\n  <!-- Metrics Cards -->\n  <div class=\"grid mb-4\">\n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-blue-500 text-3xl mb-2\">\n          <i class=\"pi pi-shield\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-blue-600\">{{ metrics.totalEvents }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.totalEvents') }}\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-red-500 text-3xl mb-2\">\n          <i class=\"pi pi-exclamation-triangle\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-red-600\">{{ metrics.criticalEvents }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.criticalEvents') }}\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-green-500 text-3xl mb-2\">\n          <i class=\"pi pi-check-circle\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-green-600\">{{ metrics.successfulLogins }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.successfulLogins') }}\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-orange-500 text-3xl mb-2\">\n          <i class=\"pi pi-times-circle\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-orange-600\">{{ metrics.failedLogins }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.failedLogins') }}\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-purple-500 text-3xl mb-2\">\n          <i class=\"pi pi-globe\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-purple-600\">{{ metrics.uniqueIPs }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.uniqueIPs') }}\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6 lg:col-2\">\n      <div class=\"card text-center\">\n        <div class=\"text-yellow-500 text-3xl mb-2\">\n          <i class=\"pi pi-lock\"></i>\n        </div>\n        <div class=\"text-2xl font-bold text-yellow-600\">{{ metrics.lockedAccounts }}</div>\n        <div class=\"text-sm text-gray-600\">\n          {{ languageService.translate('security.monitoring.lockedAccounts') }}\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Filters -->\n  <div class=\"card mb-4\">\n    <h5>{{ languageService.translate('security.monitoring.filters') }}</h5>\n    <div class=\"grid\">\n      <div class=\"col-12 md:col-3\">\n        <label for=\"eventType\" class=\"block text-sm font-medium mb-2\">\n          {{ languageService.translate('security.monitoring.eventType') }}\n        </label>\n        <p-dropdown id=\"eventType\"\n                    [(ngModel)]=\"eventTypeFilter\"\n                    [options]=\"[\n                      {label: 'All', value: ''},\n                      {label: 'Login Success', value: 'LoginSuccess'},\n                      {label: 'Login Failure', value: 'LoginFailure'},\n                      {label: 'Logout', value: 'Logout'},\n                      {label: 'Account Locked', value: 'AccountLocked'},\n                      {label: 'Suspicious Activity', value: 'SuspiciousActivity'}\n                    ]\"\n                    optionLabel=\"label\"\n                    optionValue=\"value\"\n                    [style]=\"{'width': '100%'}\">\n        </p-dropdown>\n      </div>\n      \n      <div class=\"col-12 md:col-3\">\n        <label for=\"severity\" class=\"block text-sm font-medium mb-2\">\n          {{ languageService.translate('security.monitoring.severity') }}\n        </label>\n        <p-dropdown id=\"severity\"\n                    [(ngModel)]=\"severityFilter\"\n                    [options]=\"[\n                      {label: 'All', value: ''},\n                      {label: 'Critical', value: 'Critical'},\n                      {label: 'High', value: 'High'},\n                      {label: 'Medium', value: 'Medium'},\n                      {label: 'Low', value: 'Low'}\n                    ]\"\n                    optionLabel=\"label\"\n                    optionValue=\"value\"\n                    [style]=\"{'width': '100%'}\">\n        </p-dropdown>\n      </div>\n      \n      <div class=\"col-12 md:col-3\">\n        <label for=\"dateRange\" class=\"block text-sm font-medium mb-2\">\n          {{ languageService.translate('security.monitoring.dateRange') }}\n        </label>\n        <p-calendar id=\"dateRange\"\n                    [(ngModel)]=\"dateRangeFilter.start\"\n                    [showTime]=\"true\"\n                    [style]=\"{'width': '100%'}\">\n        </p-calendar>\n      </div>\n      \n      <div class=\"col-12 md:col-3 flex align-items-end\">\n        <div class=\"flex gap-2 w-full\">\n          <button pButton \n                  type=\"button\" \n                  label=\"{{ languageService.translate('security.monitoring.apply') }}\"\n                  icon=\"pi pi-search\"\n                  class=\"p-button-primary flex-1\"\n                  (click)=\"applyFilters()\">\n          </button>\n          <button pButton \n                  type=\"button\" \n                  label=\"{{ languageService.translate('security.monitoring.clear') }}\"\n                  icon=\"pi pi-times\"\n                  class=\"p-button-outlined flex-1\"\n                  (click)=\"clearFilters()\">\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Charts -->\n  <div class=\"grid mb-4\">\n    <div class=\"col-12 md:col-6\">\n      <div class=\"card\">\n        <h5>{{ languageService.translate('security.monitoring.eventsBySeverity') }}</h5>\n        <p-chart type=\"doughnut\" \n                 [data]=\"eventChartData\"\n                 [options]=\"{\n                   plugins: {\n                     legend: { position: 'bottom' }\n                   }\n                 }\">\n        </p-chart>\n      </div>\n    </div>\n    \n    <div class=\"col-12 md:col-6\">\n      <div class=\"card\">\n        <h5>{{ languageService.translate('security.monitoring.loginAttempts') }}</h5>\n        <p-chart type=\"doughnut\" \n                 [data]=\"loginChartData\"\n                 [options]=\"{\n                   plugins: {\n                     legend: { position: 'bottom' }\n                   }\n                 }\">\n        </p-chart>\n      </div>\n    </div>\n  </div>\n\n  <!-- Tabs -->\n  <p-tabView (onChange)=\"onTabChange($event)\">\n    <!-- Security Events Tab -->\n    <p-tabPanel header=\"{{ languageService.translate('security.monitoring.securityEvents') }}\">\n      <p-table [value]=\"securityEvents\" \n               [paginator]=\"true\" \n               [rows]=\"20\"\n               [loading]=\"loading\"\n               [responsive]=\"true\">\n        <ng-template pTemplate=\"header\">\n          <tr>\n            <th>{{ languageService.translate('security.monitoring.timestamp') }}</th>\n            <th>{{ languageService.translate('security.monitoring.eventType') }}</th>\n            <th>{{ languageService.translate('security.monitoring.severity') }}</th>\n            <th>{{ languageService.translate('security.monitoring.description') }}</th>\n            <th>{{ languageService.translate('security.monitoring.ipAddress') }}</th>\n            <th>{{ languageService.translate('security.monitoring.actions') }}</th>\n          </tr>\n        </ng-template>\n        \n        <ng-template pTemplate=\"body\" let-event>\n          <tr>\n            <td>{{ formatDate(event.occurredAt) }}</td>\n            <td>\n              <div class=\"flex align-items-center gap-2\">\n                <i class=\"pi {{ getEventTypeIcon(event.eventType) }}\"></i>\n                {{ event.eventType }}\n              </div>\n            </td>\n            <td>\n              <span class=\"severity-badge {{ getSeverityClass(event.severity) }}\">\n                {{ event.severity }}\n              </span>\n            </td>\n            <td>{{ event.description }}</td>\n            <td>{{ event.ipAddress }}</td>\n            <td>\n              <button pButton \n                      type=\"button\" \n                      icon=\"pi pi-eye\" \n                      class=\"p-button-rounded p-button-text\"\n                      [pTooltip]=\"languageService.translate('security.monitoring.viewDetails')\">\n              </button>\n            </td>\n          </tr>\n        </ng-template>\n        \n        <ng-template pTemplate=\"emptymessage\">\n          <tr>\n            <td colspan=\"6\" class=\"text-center\">\n              {{ languageService.translate('security.monitoring.noEvents') }}\n            </td>\n          </tr>\n        </ng-template>\n      </p-table>\n    </p-tabPanel>\n\n    <!-- Login Attempts Tab -->\n    <p-tabPanel header=\"{{ languageService.translate('security.monitoring.loginAttempts') }}\">\n      <p-table [value]=\"loginAttempts\" \n               [paginator]=\"true\" \n               [rows]=\"20\"\n               [loading]=\"loading\"\n               [responsive]=\"true\">\n        <ng-template pTemplate=\"header\">\n          <tr>\n            <th>{{ languageService.translate('security.monitoring.timestamp') }}</th>\n            <th>{{ languageService.translate('security.monitoring.username') }}</th>\n            <th>{{ languageService.translate('security.monitoring.status') }}</th>\n            <th>{{ languageService.translate('security.monitoring.ipAddress') }}</th>\n            <th>{{ languageService.translate('security.monitoring.failureReason') }}</th>\n          </tr>\n        </ng-template>\n        \n        <ng-template pTemplate=\"body\" let-attempt>\n          <tr>\n            <td>{{ formatDate(attempt.attemptedAt) }}</td>\n            <td>{{ attempt.username }}</td>\n            <td>\n              <span class=\"status-badge\" \n                    [class.success]=\"attempt.isSuccessful\" \n                    [class.failure]=\"!attempt.isSuccessful\">\n                <i class=\"pi\" \n                   [class.pi-check]=\"attempt.isSuccessful\" \n                   [class.pi-times]=\"!attempt.isSuccessful\"></i>\n                {{ attempt.isSuccessful ? \n                   languageService.translate('security.monitoring.success') : \n                   languageService.translate('security.monitoring.failure') }}\n              </span>\n            </td>\n            <td>{{ attempt.ipAddress }}</td>\n            <td>{{ attempt.failureReason || '-' }}</td>\n          </tr>\n        </ng-template>\n        \n        <ng-template pTemplate=\"emptymessage\">\n          <tr>\n            <td colspan=\"5\" class=\"text-center\">\n              {{ languageService.translate('security.monitoring.noAttempts') }}\n            </td>\n          </tr>\n        </ng-template>\n      </p-table>\n    </p-tabPanel>\n  </p-tabView>\n</div>\n\n<!-- Access Denied Message -->\n<div *appIsAuthenticated=\"false\" class=\"text-center p-6\">\n  <i class=\"pi pi-lock text-6xl text-gray-400 mb-3\"></i>\n  <h3>{{ languageService.translate('security.monitoring.accessDenied') }}</h3>\n  <p class=\"text-gray-600\">\n    {{ languageService.translate('security.monitoring.accessDeniedMessage') }}\n  </p>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,MAAM;AAEnD;AACA,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,qBAAqB,QAAQ,yBAAyB;AAK/D,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICIzDC,EAAA,CAAAC,cAAA,iBAM6E;IADrED,EAAA,CAAAE,UAAA,mBAAAC,4EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAE9BT,EAAA,CAAAU,YAAA,EAAS;;;;IADDV,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAM,eAAA,CAAAC,SAAA,+BAAoE;;;;;IAqMtEb,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAc,MAAA,GAAgE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACzEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAgE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACzEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAA+D;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACxEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAkE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAC3EV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,IAAgE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACzEV,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,IAA8D;IACpEd,EADoE,CAAAU,YAAA,EAAK,EACpE;;;;IANCV,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAgE;IAChEb,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAgE;IAChEb,EAAA,CAAAe,SAAA,GAA+D;IAA/Df,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,iCAA+D;IAC/Db,EAAA,CAAAe,SAAA,GAAkE;IAAlEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,oCAAkE;IAClEb,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAgE;IAChEb,EAAA,CAAAe,SAAA,GAA8D;IAA9Df,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,gCAA8D;;;;;IAMlEb,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAc,MAAA,GAAkC;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAEzCV,EADF,CAAAC,cAAA,SAAI,cACyC;IACzCD,EAAA,CAAAiB,SAAA,QAA0D;IAC1DjB,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAU,YAAA,EAAM,EACH;IAEHV,EADF,CAAAC,cAAA,SAAI,WACkE;IAClED,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAU,YAAA,EAAO,EACJ;IACLV,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,IAAuB;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAChCV,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,IAAqB;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAC9BV,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAiB,SAAA,kBAKS;IAEbjB,EADE,CAAAU,YAAA,EAAK,EACF;;;;;IAtBCV,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAY,UAAA,CAAAC,QAAA,CAAAC,UAAA,EAAkC;IAG/BpB,EAAA,CAAAe,SAAA,GAAkD;IAAlDf,EAAA,CAAAqB,sBAAA,QAAAf,MAAA,CAAAgB,gBAAA,CAAAH,QAAA,CAAAI,SAAA,MAAkD;IACrDvB,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAL,QAAA,CAAAI,SAAA,MACF;IAGMvB,EAAA,CAAAe,SAAA,GAA6D;IAA7Df,EAAA,CAAAqB,sBAAA,oBAAAf,MAAA,CAAAmB,gBAAA,CAAAN,QAAA,CAAAO,QAAA,MAA6D;IACjE1B,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAL,QAAA,CAAAO,QAAA,MACF;IAEE1B,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgB,iBAAA,CAAAG,QAAA,CAAAQ,WAAA,CAAuB;IACvB3B,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAgB,iBAAA,CAAAG,QAAA,CAAAS,SAAA,CAAqB;IAMf5B,EAAA,CAAAe,SAAA,GAAyE;IAAzEf,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAM,eAAA,CAAAC,SAAA,oCAAyE;;;;;IAQnFb,EADF,CAAAC,cAAA,SAAI,aACkC;IAClCD,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAU,YAAA,EAAK,EACF;;;;IAFDV,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,sCACF;;;;;IAeAb,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAc,MAAA,GAAgE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACzEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAA+D;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACxEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAA6D;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACtEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAgE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACzEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,IAAoE;IAC1Ed,EAD0E,CAAAU,YAAA,EAAK,EAC1E;;;;IALCV,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAgE;IAChEb,EAAA,CAAAe,SAAA,GAA+D;IAA/Df,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,iCAA+D;IAC/Db,EAAA,CAAAe,SAAA,GAA6D;IAA7Df,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,+BAA6D;IAC7Db,EAAA,CAAAe,SAAA,GAAgE;IAAhEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAgE;IAChEb,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,sCAAoE;;;;;IAMxEb,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAc,MAAA,GAAqC;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAC9CV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAsB;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAE7BV,EADF,CAAAC,cAAA,SAAI,eAG4C;IAC5CD,EAAA,CAAAiB,SAAA,YAEgD;IAChDjB,EAAA,CAAAc,MAAA,GAGF;IACFd,EADE,CAAAU,YAAA,EAAO,EACJ;IACLV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,IAAuB;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAChCV,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAc,MAAA,IAAkC;IACxCd,EADwC,CAAAU,YAAA,EAAK,EACxC;;;;;IAhBCV,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAY,UAAA,CAAAW,UAAA,CAAAC,WAAA,EAAqC;IACrC9B,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAgB,iBAAA,CAAAa,UAAA,CAAAE,QAAA,CAAsB;IAGlB/B,EAAA,CAAAe,SAAA,GAAsC;IACtCf,EADA,CAAAgC,WAAA,YAAAH,UAAA,CAAAI,YAAA,CAAsC,aAAAJ,UAAA,CAAAI,YAAA,CACC;IAExCjC,EAAA,CAAAe,SAAA,EAAuC;IACvCf,EADA,CAAAgC,WAAA,aAAAH,UAAA,CAAAI,YAAA,CAAuC,cAAAJ,UAAA,CAAAI,YAAA,CACC;IAC3CjC,EAAA,CAAAe,SAAA,EAGF;IAHEf,EAAA,CAAAwB,kBAAA,MAAAK,UAAA,CAAAI,YAAA,GAAA3B,MAAA,CAAAM,eAAA,CAAAC,SAAA,kCAAAP,MAAA,CAAAM,eAAA,CAAAC,SAAA,qCAGF;IAEEb,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgB,iBAAA,CAAAa,UAAA,CAAAD,SAAA,CAAuB;IACvB5B,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAgB,iBAAA,CAAAa,UAAA,CAAAK,aAAA,QAAkC;;;;;IAMtClC,EADF,CAAAC,cAAA,SAAI,aACkC;IAClCD,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAU,YAAA,EAAK,EACF;;;;IAFDV,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,wCACF;;;;;;IA1TRb,EAHJ,CAAAC,cAAA,aAAuE,aAEH,YAC7B;IACjCD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAIHV,EAFF,CAAAC,cAAA,aAAwB,uBAKsD;IAF1ED,EAAA,CAAAmC,gBAAA,2BAAAC,kFAAAC,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuC,kBAAA,CAAAjC,MAAA,CAAAkC,WAAA,EAAAH,MAAA,MAAA/B,MAAA,CAAAkC,WAAA,GAAAH,MAAA;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAA6B,MAAA;IAAA,EAAyB;IACzBrC,EAAA,CAAAE,UAAA,sBAAAuC,6EAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAoC,iBAAA,EAAmB;IAAA,EAAC;IAElC1C,EAAA,CAAAU,YAAA,EAAgB;IAGhBV,EAAA,CAAAC,cAAA,gBAM8E;IADtED,EAAA,CAAAE,UAAA,mBAAAyC,mEAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsC,QAAA,EAAU;IAAA,EAAC;IAE5B5C,EAAA,CAAAU,YAAA,EAAS;IAGTV,EAAA,CAAA6C,UAAA,IAAAC,mDAAA,oBAM6E;IAGjF9C,EADE,CAAAU,YAAA,EAAM,EACF;IAMAV,EAHN,CAAAC,cAAA,aAAuB,cACiB,eACN,eACa;IACvCD,EAAA,CAAAiB,SAAA,aAA4B;IAC9BjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAc,MAAA,IAAyB;IAAAd,EAAA,CAAAU,YAAA,EAAM;IAC7EV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAAsC,eACN,eACY;IACtCD,EAAA,CAAAiB,SAAA,aAA0C;IAC5CjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAc,MAAA,IAA4B;IAAAd,EAAA,CAAAU,YAAA,EAAM;IAC/EV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAAsC,eACN,eACc;IACxCD,EAAA,CAAAiB,SAAA,aAAkC;IACpCjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAc,MAAA,IAA8B;IAAAd,EAAA,CAAAU,YAAA,EAAM;IACnFV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAAsC,eACN,eACe;IACzCD,EAAA,CAAAiB,SAAA,aAAkC;IACpCjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAc,MAAA,IAA0B;IAAAd,EAAA,CAAAU,YAAA,EAAM;IAChFV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAAsC,eACN,eACe;IACzCD,EAAA,CAAAiB,SAAA,aAA2B;IAC7BjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAc,MAAA,IAAuB;IAAAd,EAAA,CAAAU,YAAA,EAAM;IAC7EV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAEJd,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAAsC,eACN,eACe;IACzCD,EAAA,CAAAiB,SAAA,aAA0B;IAC5BjB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAc,MAAA,IAA4B;IAAAd,EAAA,CAAAU,YAAA,EAAM;IAClFV,EAAA,CAAAC,cAAA,eAAmC;IACjCD,EAAA,CAAAc,MAAA,IACF;IAGNd,EAHM,CAAAU,YAAA,EAAM,EACF,EACF,EACF;IAIJV,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAc,MAAA,IAA8D;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAGnEV,EAFJ,CAAAC,cAAA,eAAkB,eACa,iBACmC;IAC5DD,EAAA,CAAAc,MAAA,IACF;IAAAd,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAC,cAAA,sBAYwC;IAX5BD,EAAA,CAAAmC,gBAAA,2BAAAY,gFAAAV,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuC,kBAAA,CAAAjC,MAAA,CAAA0C,eAAA,EAAAX,MAAA,MAAA/B,MAAA,CAAA0C,eAAA,GAAAX,MAAA;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAA6B,MAAA;IAAA,EAA6B;IAa3CrC,EADE,CAAAU,YAAA,EAAa,EACT;IAGJV,EADF,CAAAC,cAAA,eAA6B,iBACkC;IAC3DD,EAAA,CAAAc,MAAA,IACF;IAAAd,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAC,cAAA,sBAWwC;IAV5BD,EAAA,CAAAmC,gBAAA,2BAAAc,gFAAAZ,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuC,kBAAA,CAAAjC,MAAA,CAAA4C,cAAA,EAAAb,MAAA,MAAA/B,MAAA,CAAA4C,cAAA,GAAAb,MAAA;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAA6B,MAAA;IAAA,EAA4B;IAY1CrC,EADE,CAAAU,YAAA,EAAa,EACT;IAGJV,EADF,CAAAC,cAAA,eAA6B,iBACmC;IAC5DD,EAAA,CAAAc,MAAA,IACF;IAAAd,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAC,cAAA,sBAGwC;IAF5BD,EAAA,CAAAmC,gBAAA,2BAAAgB,gFAAAd,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuC,kBAAA,CAAAjC,MAAA,CAAA8C,eAAA,CAAAC,KAAA,EAAAhB,MAAA,MAAA/B,MAAA,CAAA8C,eAAA,CAAAC,KAAA,GAAAhB,MAAA;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAA6B,MAAA;IAAA,EAAmC;IAIjDrC,EADE,CAAAU,YAAA,EAAa,EACT;IAIFV,EAFJ,CAAAC,cAAA,eAAkD,eACjB,kBAMI;IAAzBD,EAAA,CAAAE,UAAA,mBAAAoD,oEAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,YAAA,EAAc;IAAA,EAAC;IAChCvD,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,kBAKiC;IAAzBD,EAAA,CAAAE,UAAA,mBAAAsD,oEAAA;MAAAxD,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmD,YAAA,EAAc;IAAA,EAAC;IAKxCzD,EAJQ,CAAAU,YAAA,EAAS,EACL,EACF,EACF,EACF;IAMAV,EAHN,CAAAC,cAAA,cAAuB,eACQ,eACT,UACZ;IAAAD,EAAA,CAAAc,MAAA,IAAuE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAChFV,EAAA,CAAAiB,SAAA,mBAOU;IAEdjB,EADE,CAAAU,YAAA,EAAM,EACF;IAIFV,EAFJ,CAAAC,cAAA,eAA6B,eACT,UACZ;IAAAD,EAAA,CAAAc,MAAA,IAAoE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAC7EV,EAAA,CAAAiB,SAAA,mBAOU;IAGhBjB,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAGNV,EAAA,CAAAC,cAAA,qBAA4C;IAAjCD,EAAA,CAAAE,UAAA,sBAAAwD,0EAAArB,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAqD,WAAA,CAAAtB,MAAA,CAAmB;IAAA,EAAC;IAGvCrC,EADF,CAAAC,cAAA,sBAA2F,mBAK5D;IAuC3BD,EAtCA,CAAA6C,UAAA,KAAAe,yDAAA,2BAAgC,KAAAC,yDAAA,4BAWQ,KAAAC,yDAAA,0BA2BF;IAQ1C9D,EADE,CAAAU,YAAA,EAAU,EACC;IAIXV,EADF,CAAAC,cAAA,sBAA0F,mBAK3D;IAgC3BD,EA/BA,CAAA6C,UAAA,KAAAkB,yDAAA,2BAAgC,KAAAC,yDAAA,4BAUU,KAAAC,yDAAA,0BAqBJ;IAU9CjE,EAHM,CAAAU,YAAA,EAAU,EACC,EACH,EACR;;;;IA/TAV,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,mCACF;IAKIb,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAkE,gBAAA,YAAA5D,MAAA,CAAAkC,WAAA,CAAyB;IAEzBxC,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAM,eAAA,CAAAC,SAAA,oCAAyE;IAQnEb,EAAA,CAAAe,SAAA,EAAmB;IAEnBf,EAFA,CAAAW,UAAA,YAAAL,MAAA,CAAA6D,OAAA,CAAmB,aAAA7D,MAAA,CAAAM,eAAA,CAAAC,SAAA,gCAEkD;IAIpEb,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAW,UAAA,uBAAmB;IAkBoBX,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAC,WAAA,CAAyB;IAErErE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,yCACF;IAS6Cb,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAE,cAAA,CAA4B;IAEvEtE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,4CACF;IAS+Cb,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAG,gBAAA,CAA8B;IAE3EvE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,8CACF;IASgDb,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAI,YAAA,CAA0B;IAExExE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,0CACF;IASgDb,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAK,SAAA,CAAuB;IAErEzE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,uCACF;IASgDb,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAA8D,OAAA,CAAAM,cAAA,CAA4B;IAE1E1E,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,4CACF;IAOAb,EAAA,CAAAe,SAAA,GAA8D;IAA9Df,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,gCAA8D;IAI5Db,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,uCACF;IAaYb,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAA2E,UAAA,CAAA3E,EAAA,CAAA4E,eAAA,KAAAC,GAAA,EAA2B;IAX3B7E,EAAA,CAAAkE,gBAAA,YAAA5D,MAAA,CAAA0C,eAAA,CAA6B;IAC7BhD,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAA8E,eAAA,KAAAC,GAAA,EAAA/E,EAAA,CAAA4E,eAAA,KAAAI,GAAA,GAAAhF,EAAA,CAAA4E,eAAA,KAAAK,GAAA,GAAAjF,EAAA,CAAA4E,eAAA,KAAAM,GAAA,GAAAlF,EAAA,CAAA4E,eAAA,KAAAO,GAAA,GAAAnF,EAAA,CAAA4E,eAAA,KAAAQ,GAAA,GAAApF,EAAA,CAAA4E,eAAA,KAAAS,GAAA,GAOE;IASZrF,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,sCACF;IAYYb,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAA2E,UAAA,CAAA3E,EAAA,CAAA4E,eAAA,KAAAC,GAAA,EAA2B;IAV3B7E,EAAA,CAAAkE,gBAAA,YAAA5D,MAAA,CAAA4C,cAAA,CAA4B;IAC5BlD,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAsF,eAAA,KAAAC,IAAA,EAAAvF,EAAA,CAAA4E,eAAA,KAAAI,GAAA,GAAAhF,EAAA,CAAA4E,eAAA,KAAAY,GAAA,GAAAxF,EAAA,CAAA4E,eAAA,KAAAa,GAAA,GAAAzF,EAAA,CAAA4E,eAAA,KAAAc,IAAA,GAAA1F,EAAA,CAAA4E,eAAA,KAAAe,IAAA,GAME;IASZ3F,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,uCACF;IAIYb,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAA2E,UAAA,CAAA3E,EAAA,CAAA4E,eAAA,KAAAC,GAAA,EAA2B;IAF3B7E,EAAA,CAAAkE,gBAAA,YAAA5D,MAAA,CAAA8C,eAAA,CAAAC,KAAA,CAAmC;IACnCrD,EAAA,CAAAW,UAAA,kBAAiB;IASnBX,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAA4F,qBAAA,UAAAtF,MAAA,CAAAM,eAAA,CAAAC,SAAA,8BAAoE;IAOpEb,EAAA,CAAAe,SAAA,EAAoE;IAApEf,EAAA,CAAA4F,qBAAA,UAAAtF,MAAA,CAAAM,eAAA,CAAAC,SAAA,8BAAoE;IAc1Eb,EAAA,CAAAe,SAAA,GAAuE;IAAvEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,yCAAuE;IAElEb,EAAA,CAAAe,SAAA,EAAuB;IACvBf,EADA,CAAAW,UAAA,SAAAL,MAAA,CAAAuF,cAAA,CAAuB,YAAA7F,EAAA,CAAA8F,eAAA,KAAAC,IAAA,EAAA/F,EAAA,CAAA8F,eAAA,KAAAE,IAAA,EAAAhG,EAAA,CAAA4E,eAAA,KAAAqB,IAAA,IAKrB;IAOPjG,EAAA,CAAAe,SAAA,GAAoE;IAApEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,sCAAoE;IAE/Db,EAAA,CAAAe,SAAA,EAAuB;IACvBf,EADA,CAAAW,UAAA,SAAAL,MAAA,CAAA4F,cAAA,CAAuB,YAAAlG,EAAA,CAAA8F,eAAA,KAAAC,IAAA,EAAA/F,EAAA,CAAA8F,eAAA,KAAAE,IAAA,EAAAhG,EAAA,CAAA4E,eAAA,KAAAqB,IAAA,IAKrB;IASHjG,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAA4F,qBAAA,WAAAtF,MAAA,CAAAM,eAAA,CAAAC,SAAA,uCAA8E;IAC/Eb,EAAA,CAAAe,SAAA,EAAwB;IAIxBf,EAJA,CAAAW,UAAA,UAAAL,MAAA,CAAA6F,cAAA,CAAwB,mBACN,YACP,YAAA7F,MAAA,CAAA6D,OAAA,CACQ,oBACA;IAkDlBnE,EAAA,CAAAe,SAAA,GAA6E;IAA7Ef,EAAA,CAAA4F,qBAAA,WAAAtF,MAAA,CAAAM,eAAA,CAAAC,SAAA,sCAA6E;IAC9Eb,EAAA,CAAAe,SAAA,EAAuB;IAIvBf,EAJA,CAAAW,UAAA,UAAAL,MAAA,CAAA8F,aAAA,CAAuB,mBACL,YACP,YAAA9F,MAAA,CAAA6D,OAAA,CACQ,oBACA;;;;;IA6ClCnE,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAiB,SAAA,YAAsD;IACtDjB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAmE;IAAAd,EAAA,CAAAU,YAAA,EAAK;IAC5EV,EAAA,CAAAC,cAAA,YAAyB;IACvBD,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAU,YAAA,EAAI,EACA;;;;IAJAV,EAAA,CAAAe,SAAA,GAAmE;IAAnEf,EAAA,CAAAgB,iBAAA,CAAAV,MAAA,CAAAM,eAAA,CAAAC,SAAA,qCAAmE;IAErEb,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,eAAA,CAAAC,SAAA,iDACF;;;AD9PF,OAAM,MAAOwF,2BAA2B;EAkCtCC,YACUC,IAAgB,EACjB3F,eAAgC,EAC/B4F,oBAA0C;IAF1C,KAAAD,IAAI,GAAJA,IAAI;IACL,KAAA3F,eAAe,GAAfA,eAAe;IACd,KAAA4F,oBAAoB,GAApBA,oBAAoB;IApCtB,KAAAC,QAAQ,GAAG,IAAIxH,OAAO,EAAQ;IACrB,KAAAyH,OAAO,GAAG3G,WAAW,CAAC4G,MAAM;IAE7C;IACA,KAAAR,cAAc,GAAoB,EAAE;IACpC,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAhC,OAAO,GAAoB;MACzBC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBE,YAAY,EAAE,CAAC;MACfD,gBAAgB,EAAE,CAAC;MACnBE,SAAS,EAAE,CAAC;MACZC,cAAc,EAAE;KACjB;IAED;IACA,KAAAP,OAAO,GAAG,KAAK;IACf,KAAAyC,WAAW,GAAG,CAAC;IACf,KAAAC,eAAe,GAAG,KAAK,CAAC,CAAC;IACzB,KAAArE,WAAW,GAAG,IAAI;IAElB;IACA,KAAAQ,eAAe,GAAG,EAAE;IACpB,KAAAE,cAAc,GAAG,EAAE;IACnB,KAAAE,eAAe,GAAG;MAChBC,KAAK,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACjDC,GAAG,EAAE,IAAIF,IAAI;KACd;EAUE;EAEHG,QAAQA,CAAA;IACN,IAAI,CAACrE,QAAQ,EAAE;IACf,IAAI,CAACsE,gBAAgB,EAAE;IACvB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,QAAQ,CAACY,IAAI,EAAE;IACpB,IAAI,CAACZ,QAAQ,CAACa,QAAQ,EAAE;EAC1B;EAEQJ,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAC1E,WAAW,EAAE;MACpBtD,QAAQ,CAAC,IAAI,CAAC2H,eAAe,CAAC,CAC3BU,IAAI,CAACpI,SAAS,CAAC,IAAI,CAACsH,QAAQ,CAAC,CAAC,CAC9Be,SAAS,CAAC,MAAK;QACd,IAAI,CAAC5E,QAAQ,EAAE;MACjB,CAAC,CAAC;;EAER;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACuB,OAAO,GAAG,IAAI;IAEnBsD,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,kBAAkB,EAAE,EACzB,IAAI,CAACC,iBAAiB,EAAE,EACxB,IAAI,CAACC,WAAW,EAAE,CACnB,CAAC,CAACC,OAAO,CAAC,MAAK;MACd,IAAI,CAAC3D,OAAO,GAAG,KAAK;MACpB,IAAI,CAAC4D,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEcJ,kBAAkBA,CAAA;IAAA,IAAAK,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;UACjCC,IAAI,EAAE,GAAG;UACTC,QAAQ,EAAE,IAAI;UACd,IAAIL,KAAI,CAAChF,eAAe,IAAI;YAAEzB,SAAS,EAAEyG,KAAI,CAAChF;UAAe,CAAE,CAAC;UAChE,IAAIgF,KAAI,CAAC9E,cAAc,IAAI;YAAExB,QAAQ,EAAEsG,KAAI,CAAC9E;UAAc,CAAE,CAAC;UAC7DoF,QAAQ,EAAEN,KAAI,CAAC5E,eAAe,CAACC,KAAK,CAACkF,WAAW,EAAE;UAClDC,MAAM,EAAER,KAAI,CAAC5E,eAAe,CAAC4D,GAAG,CAACuB,WAAW;SAC7C,CAAC;QAEF,MAAME,QAAQ,SAAST,KAAI,CAACzB,IAAI,CAACmC,GAAG,CAAM,GAAGV,KAAI,CAACtB,OAAO,oBAAoBwB,MAAM,EAAE,CAAC,CACnFS,SAAS,EAAE;QAEd,IAAIF,QAAQ,CAACG,OAAO,EAAE;UACpBZ,KAAI,CAAC7B,cAAc,GAAGsC,QAAQ,CAACI,IAAI,IAAI,EAAE;;OAE5C,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IACvD;EACH;EAEclB,iBAAiBA,CAAA;IAAA,IAAAoB,MAAA;IAAA,OAAAf,iBAAA;MAC7B,IAAI;QACF,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;UACjCC,IAAI,EAAE,GAAG;UACTC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAEU,MAAI,CAAC5F,eAAe,CAACC,KAAK,CAACkF,WAAW,EAAE;UAClDC,MAAM,EAAEQ,MAAI,CAAC5F,eAAe,CAAC4D,GAAG,CAACuB,WAAW;SAC7C,CAAC;QAEF,MAAME,QAAQ,SAASO,MAAI,CAACzC,IAAI,CAACmC,GAAG,CAAM,GAAGM,MAAI,CAACtC,OAAO,4BAA4BwB,MAAM,EAAE,CAAC,CAC3FS,SAAS,EAAE;QAEd,IAAIF,QAAQ,CAACG,OAAO,EAAE;UACpBI,MAAI,CAAC5C,aAAa,GAAGqC,QAAQ,CAACI,IAAI,IAAI,EAAE;;OAE3C,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;IACtD;EACH;EAEcjB,WAAWA,CAAA;IAAA,IAAAoB,MAAA;IAAA,OAAAhB,iBAAA;MACvB;MACAgB,MAAI,CAAC7E,OAAO,GAAG;QACbC,WAAW,EAAE4E,MAAI,CAAC9C,cAAc,CAAC+C,MAAM;QACvC5E,cAAc,EAAE2E,MAAI,CAAC9C,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,QAAQ,KAAK,UAAU,CAAC,CAACwH,MAAM;QACjF1E,YAAY,EAAEyE,MAAI,CAAC7C,aAAa,CAAC+C,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAACpH,YAAY,CAAC,CAACiH,MAAM;QACpE3E,gBAAgB,EAAE0E,MAAI,CAAC7C,aAAa,CAAC+C,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACpH,YAAY,CAAC,CAACiH,MAAM;QACvEzE,SAAS,EAAE,IAAI6E,GAAG,CAACL,MAAI,CAAC7C,aAAa,CAACmD,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACzH,SAAS,CAAC,CAAC,CAAC4H,IAAI;QACjE9E,cAAc,EAAEuE,MAAI,CAAC9C,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7H,SAAS,KAAK,eAAe,CAAC,CAAC2H;OAClF;IAAC;EACJ;EAEQ/B,gBAAgBA,CAAA;IACtB,IAAI,CAACtB,cAAc,GAAG;MACpB4D,MAAM,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;MAC7CC,QAAQ,EAAE,CAAC;QACTb,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAClBc,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;OAC7D;KACF;IAED,IAAI,CAACzD,cAAc,GAAG;MACpBuD,MAAM,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;MAChCC,QAAQ,EAAE,CAAC;QACTb,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACZc,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS;OACvC;KACF;EACH;EAEQ5B,YAAYA,CAAA;IAClB;IACA,MAAM6B,cAAc,GAAG;MACrBC,QAAQ,EAAE,IAAI,CAAC1D,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,QAAQ,KAAK,UAAU,CAAC,CAACwH,MAAM;MAC3EY,IAAI,EAAE,IAAI,CAAC3D,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,QAAQ,KAAK,MAAM,CAAC,CAACwH,MAAM;MACnEa,MAAM,EAAE,IAAI,CAAC5D,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,QAAQ,KAAK,QAAQ,CAAC,CAACwH,MAAM;MACvEc,GAAG,EAAE,IAAI,CAAC7D,cAAc,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1H,QAAQ,KAAK,KAAK,CAAC,CAACwH;KAC5D;IAED,IAAI,CAACrD,cAAc,GAAG;MACpB,GAAG,IAAI,CAACA,cAAc;MACtB6D,QAAQ,EAAE,CAAC;QACT,GAAG,IAAI,CAAC7D,cAAc,CAAC6D,QAAQ,CAAC,CAAC,CAAC;QAClCb,IAAI,EAAE,CAACe,cAAc,CAACC,QAAQ,EAAED,cAAc,CAACE,IAAI,EAAEF,cAAc,CAACG,MAAM,EAAEH,cAAc,CAACI,GAAG;OAC/F;KACF;IAED;IACA,IAAI,CAAC9D,cAAc,GAAG;MACpB,GAAG,IAAI,CAACA,cAAc;MACtBwD,QAAQ,EAAE,CAAC;QACT,GAAG,IAAI,CAACxD,cAAc,CAACwD,QAAQ,CAAC,CAAC,CAAC;QAClCb,IAAI,EAAE,CAAC,IAAI,CAACzE,OAAO,CAACG,gBAAgB,EAAE,IAAI,CAACH,OAAO,CAACI,YAAY;OAChE;KACF;EACH;EAEAb,WAAWA,CAACsG,KAAU;IACpB,IAAI,CAACrD,WAAW,GAAGqD,KAAK,CAACC,KAAK;EAChC;EAEAC,uBAAuBA,CAACjL,QAAgB;IACtC,IAAI,CAAC2H,eAAe,GAAG3H,QAAQ,GAAG,IAAI;IACtC,IAAI,CAACgI,gBAAgB,EAAE;EACzB;EAEAxE,iBAAiBA,CAAA;IACf,IAAI,CAACF,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC,IAAI,IAAI,CAACA,WAAW,EAAE;MACpB,IAAI,CAAC0E,gBAAgB,EAAE;;EAE3B;EAEA3D,YAAYA,CAAA;IACV,IAAI,CAACX,QAAQ,EAAE;EACjB;EAEAa,YAAYA,CAAA;IACV,IAAI,CAACT,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,cAAc,GAAG,EAAE;IACxB,IAAI,CAACE,eAAe,GAAG;MACrBC,KAAK,EAAE,IAAIyD,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACjDC,GAAG,EAAE,IAAIF,IAAI;KACd;IACD,IAAI,CAAClE,QAAQ,EAAE;EACjB;EAEAnC,UAAUA,CAAA;IACR,MAAMoI,IAAI,GAAG;MACX1C,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjChC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBgG,UAAU,EAAE,IAAItD,IAAI,EAAE,CAACyB,WAAW;KACnC;IAED,MAAM8B,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAACC,SAAS,CAAC3B,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MAAE4B,IAAI,EAAE;IAAkB,CAAE,CAAC;IACpF,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IAC5C,MAAMS,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;IACfI,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAIpE,IAAI,EAAE,CAACyB,WAAW,EAAE,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAChFL,IAAI,CAACM,KAAK,EAAE;IACZT,MAAM,CAACC,GAAG,CAACS,eAAe,CAACX,GAAG,CAAC;EACjC;EAEAjJ,gBAAgBA,CAACC,QAAgB;IAC/B,QAAQA,QAAQ,CAAC4J,WAAW,EAAE;MAC5B,KAAK,UAAU;QAAE,OAAO,mBAAmB;MAC3C,KAAK,MAAM;QAAE,OAAO,eAAe;MACnC,KAAK,QAAQ;QAAE,OAAO,iBAAiB;MACvC,KAAK,KAAK;QAAE,OAAO,cAAc;MACjC;QAAS,OAAO,EAAE;;EAEtB;EAEAhK,gBAAgBA,CAACC,SAAiB;IAChC,QAAQA,SAAS,CAAC+J,WAAW,EAAE;MAC7B,KAAK,cAAc;QAAE,OAAO,iBAAiB;MAC7C,KAAK,cAAc;QAAE,OAAO,iBAAiB;MAC7C,KAAK,QAAQ;QAAE,OAAO,aAAa;MACnC,KAAK,eAAe;QAAE,OAAO,SAAS;MACtC,KAAK,oBAAoB;QAAE,OAAO,yBAAyB;MAC3D,KAAK,kBAAkB;QAAE,OAAO,QAAQ;MACxC;QAAS,OAAO,gBAAgB;;EAEpC;EAEApK,UAAUA,CAACqK,IAAmB;IAC5B,MAAMC,CAAC,GAAG,IAAI1E,IAAI,CAACyE,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC7K,eAAe,CAAC8K,QAAQ,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC;EAC9E;EAEA;EACAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACnF,oBAAoB,CAACoF,aAAa,CAAC,YAAY,EAAE,MAAM,CAAC;EACtE;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACrF,oBAAoB,CAACsF,OAAO,CAAC,OAAO,CAAC;EACnD;;;uBA9PWzF,2BAA2B,EAAArG,EAAA,CAAA+L,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAjM,EAAA,CAAA+L,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAnM,EAAA,CAAA+L,iBAAA,CAAAK,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA3BhG,2BAA2B;MAAAiG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxM,EAAA,CAAAyM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCyPxC/M,EAtUA,CAAA6C,UAAA,IAAAoK,0CAAA,mBAAuE,IAAAC,0CAAA,iBAsUd;;;UAtUvBlN,EAAA,CAAAW,UAAA,uCAAmC;UAsU/DX,EAAA,CAAAe,SAAA,EAAyB;UAAzBf,EAAA,CAAAW,UAAA,6BAAyB;;;qBD1Q3B5B,YAAY,EACZC,WAAW,EAAAmO,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACXvN,UAAU,EAAAwN,EAAA,CAAAC,sBAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,wBAAA,EACVrO,YAAY,EAAAsO,EAAA,CAAAC,eAAA,EAAAC,EAAA,CAAAC,aAAA,EACZxO,iBAAiB,EAAAyO,EAAA,CAAAC,WAAA,EACjBzO,aAAa,EAAA0O,EAAA,CAAAC,OAAA,EACb1O,WAAW,EAAA2O,GAAA,CAAAC,KAAA,EACX3O,aAAa,EAAA4O,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,QAAA,EACb7O,cAAc,EAAA8O,GAAA,CAAAC,QAAA,EACd9O,cAAc,EAAA+O,GAAA,CAAAC,QAAA,EACd/O,WAAW,EAAAgP,GAAA,CAAAC,OAAA,EACXhP,UAAU,EACVC,qBAAqB;MAAAgP,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}