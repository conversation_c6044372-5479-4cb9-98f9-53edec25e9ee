using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;
using WarehouseManagement.Core.Entities;

namespace WarehouseManagement.Infrastructure.Data.Seeders;

public static class AuthDataSeeder
{
    public static async Task SeedAsync(WarehouseDbContext context, ILogger logger)
    {
        try
        {
            logger.LogInformation("Starting authentication data seeding...");

            // Seed Permissions
            await SeedPermissionsAsync(context, logger);

            // Seed Roles
            await SeedRolesAsync(context, logger);

            // Save changes after seeding basic data
            await context.SaveChangesAsync();

            // Seed Admin User
            await SeedAdminUserAsync(context, logger);

            // Save changes after seeding user
            await context.SaveChangesAsync();

            // Assign Permissions to Roles
            await SeedRolePermissionsAsync(context, logger);

            // Assign Roles to Admin User
            await SeedUserRolesAsync(context, logger);

            await context.SaveChangesAsync();
            logger.LogInformation("Authentication data seeding completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while seeding authentication data");
            throw;
        }
    }

    private static async Task SeedPermissionsAsync(WarehouseDbContext context, ILogger logger)
    {
        if (await context.Permissions.AnyAsync())
        {
            logger.LogInformation("Permissions already exist, skipping seeding");
            return;
        }

        var permissions = new List<Permission>
        {
            // User Management
            new() { Name = "users.create", Resource = "users", Action = "create", Description = "Create new users" },
            new() { Name = "users.read", Resource = "users", Action = "read", Description = "View users" },
            new() { Name = "users.update", Resource = "users", Action = "update", Description = "Update user information" },
            new() { Name = "users.delete", Resource = "users", Action = "delete", Description = "Delete users" },
            new() { Name = "users.manage", Resource = "users", Action = "manage", Description = "Full user management" },

            // Role Management
            new() { Name = "roles.create", Resource = "roles", Action = "create", Description = "Create new roles" },
            new() { Name = "roles.read", Resource = "roles", Action = "read", Description = "View roles" },
            new() { Name = "roles.update", Resource = "roles", Action = "update", Description = "Update role information" },
            new() { Name = "roles.delete", Resource = "roles", Action = "delete", Description = "Delete roles" },
            new() { Name = "roles.manage", Resource = "roles", Action = "manage", Description = "Full role management" },

            // Permission Management
            new() { Name = "permissions.create", Resource = "permissions", Action = "create", Description = "Create new permissions" },
            new() { Name = "permissions.read", Resource = "permissions", Action = "read", Description = "View permissions" },
            new() { Name = "permissions.update", Resource = "permissions", Action = "update", Description = "Update permission information" },
            new() { Name = "permissions.delete", Resource = "permissions", Action = "delete", Description = "Delete permissions" },
            new() { Name = "permissions.manage", Resource = "permissions", Action = "manage", Description = "Full permission management" },

            // Warehouse Management
            new() { Name = "warehouses.create", Resource = "warehouses", Action = "create", Description = "Create new warehouses" },
            new() { Name = "warehouses.read", Resource = "warehouses", Action = "read", Description = "View warehouses" },
            new() { Name = "warehouses.update", Resource = "warehouses", Action = "update", Description = "Update warehouse information" },
            new() { Name = "warehouses.delete", Resource = "warehouses", Action = "delete", Description = "Delete warehouses" },
            new() { Name = "warehouses.manage", Resource = "warehouses", Action = "manage", Description = "Full warehouse management" },

            // Inventory Management
            new() { Name = "inventory.create", Resource = "inventory", Action = "create", Description = "Create inventory items" },
            new() { Name = "inventory.read", Resource = "inventory", Action = "read", Description = "View inventory" },
            new() { Name = "inventory.update", Resource = "inventory", Action = "update", Description = "Update inventory" },
            new() { Name = "inventory.delete", Resource = "inventory", Action = "delete", Description = "Delete inventory items" },
            new() { Name = "inventory.manage", Resource = "inventory", Action = "manage", Description = "Full inventory management" },

            // Reports
            new() { Name = "reports.read", Resource = "reports", Action = "read", Description = "View reports" },
            new() { Name = "reports.create", Resource = "reports", Action = "create", Description = "Generate reports" },
            new() { Name = "reports.manage", Resource = "reports", Action = "manage", Description = "Full report management" },

            // Security
            new() { Name = "security.read", Resource = "security", Action = "read", Description = "View security events" },
            new() { Name = "security.manage", Resource = "security", Action = "manage", Description = "Manage security settings" },

            // Dashboard
            new() { Name = "dashboard.read", Resource = "dashboard", Action = "read", Description = "View dashboard" },

            // System Settings
            new() { Name = "settings.read", Resource = "settings", Action = "read", Description = "View system settings" },
            new() { Name = "settings.update", Resource = "settings", Action = "update", Description = "Update system settings" },
            new() { Name = "settings.manage", Resource = "settings", Action = "manage", Description = "Full settings management" }
        };

        await context.Permissions.AddRangeAsync(permissions);
        logger.LogInformation($"Seeded {permissions.Count} permissions");
    }

    private static async Task SeedRolesAsync(WarehouseDbContext context, ILogger logger)
    {
        if (await context.Roles.AnyAsync())
        {
            logger.LogInformation("Roles already exist, skipping seeding");
            return;
        }

        var roles = new List<Role>
        {
            new()
            {
                Name = "admin",
                DisplayName = "System Administrator",
                Description = "Full system access with all permissions",
                IsActive = true
            },
            new()
            {
                Name = "manager",
                DisplayName = "Warehouse Manager",
                Description = "Warehouse management with limited administrative access",
                IsActive = true
            },
            new()
            {
                Name = "employee",
                DisplayName = "Warehouse Employee",
                Description = "Basic warehouse operations access",
                IsActive = true
            },
            new()
            {
                Name = "viewer",
                DisplayName = "Read-Only User",
                Description = "View-only access to warehouse data",
                IsActive = true
            }
        };

        await context.Roles.AddRangeAsync(roles);
        logger.LogInformation($"Seeded {roles.Count} roles");
    }

    private static async Task SeedAdminUserAsync(WarehouseDbContext context, ILogger logger)
    {
        if (await context.Users.AnyAsync(u => u.Username == "admin"))
        {
            logger.LogInformation("Admin user already exists, skipping seeding");
            return;
        }

        // Generate salt and hash password
        var salt = GenerateSalt();
        var passwordHash = HashPassword("Admin@123", salt);

        var adminUser = new User
        {
            Username = "admin",
            Email = "<EMAIL>",
            FirstName = "System",
            LastName = "Administrator",
            PasswordHash = passwordHash,
            Salt = salt,
            IsActive = true,
            IsEmailConfirmed = true,
            PasswordChangedAt = DateTime.UtcNow,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await context.Users.AddAsync(adminUser);
        logger.LogInformation("Seeded admin user with username: admin, password: Admin@123");
    }

    private static async Task SeedRolePermissionsAsync(WarehouseDbContext context, ILogger logger)
    {
        if (await context.RolePermissions.AnyAsync())
        {
            logger.LogInformation("Role permissions already exist, skipping seeding");
            return;
        }

        // Get all roles and permissions
        var adminRole = await context.Roles.FirstAsync(r => r.Name == "admin");
        var managerRole = await context.Roles.FirstAsync(r => r.Name == "manager");
        var employeeRole = await context.Roles.FirstAsync(r => r.Name == "employee");
        var viewerRole = await context.Roles.FirstAsync(r => r.Name == "viewer");

        var allPermissions = await context.Permissions.ToListAsync();

        var rolePermissions = new List<RolePermission>();

        // Admin gets all permissions
        foreach (var permission in allPermissions)
        {
            rolePermissions.Add(new RolePermission
            {
                RoleId = adminRole.Id,
                PermissionId = permission.Id,
                GrantedAt = DateTime.UtcNow
            });
        }

        // Manager gets most permissions except user/role/permission management
        var managerPermissions = allPermissions.Where(p =>
            !p.Resource.Equals("users", StringComparison.OrdinalIgnoreCase) &&
            !p.Resource.Equals("roles", StringComparison.OrdinalIgnoreCase) &&
            !p.Resource.Equals("permissions", StringComparison.OrdinalIgnoreCase) &&
            !p.Name.Contains("manage", StringComparison.OrdinalIgnoreCase)).ToList();

        foreach (var permission in managerPermissions)
        {
            rolePermissions.Add(new RolePermission
            {
                RoleId = managerRole.Id,
                PermissionId = permission.Id,
                GrantedAt = DateTime.UtcNow
            });
        }

        // Employee gets basic operational permissions
        var employeePermissions = allPermissions.Where(p =>
            p.Action.Equals("read", StringComparison.OrdinalIgnoreCase) ||
            p.Action.Equals("create", StringComparison.OrdinalIgnoreCase) ||
            p.Action.Equals("update", StringComparison.OrdinalIgnoreCase)).ToList();

        foreach (var permission in employeePermissions)
        {
            rolePermissions.Add(new RolePermission
            {
                RoleId = employeeRole.Id,
                PermissionId = permission.Id,
                GrantedAt = DateTime.UtcNow
            });
        }

        // Viewer gets only read permissions
        var viewerPermissions = allPermissions.Where(p =>
            p.Action.Equals("read", StringComparison.OrdinalIgnoreCase)).ToList();

        foreach (var permission in viewerPermissions)
        {
            rolePermissions.Add(new RolePermission
            {
                RoleId = viewerRole.Id,
                PermissionId = permission.Id,
                GrantedAt = DateTime.UtcNow
            });
        }

        await context.RolePermissions.AddRangeAsync(rolePermissions);
        logger.LogInformation($"Seeded {rolePermissions.Count} role permissions");
    }

    private static async Task SeedUserRolesAsync(WarehouseDbContext context, ILogger logger)
    {
        if (await context.UserRoles.AnyAsync())
        {
            logger.LogInformation("User roles already exist, skipping seeding");
            return;
        }

        var adminUser = await context.Users.FirstAsync(u => u.Username == "admin");
        var adminRole = await context.Roles.FirstAsync(r => r.Name == "admin");

        var userRole = new UserRole
        {
            UserId = adminUser.Id,
            RoleId = adminRole.Id,
            AssignedAt = DateTime.UtcNow
        };

        await context.UserRoles.AddAsync(userRole);
        logger.LogInformation("Assigned admin role to admin user");
    }

    private static string GenerateSalt()
    {
        var saltBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(saltBytes);
        return Convert.ToBase64String(saltBytes);
    }

    private static string HashPassword(string password, string salt)
    {
        using var sha256 = SHA256.Create();
        var saltedPassword = password + salt;
        var saltedPasswordBytes = Encoding.UTF8.GetBytes(saltedPassword);
        var hashBytes = sha256.ComputeHash(saltedPasswordBytes);
        return Convert.ToBase64String(hashBytes);
    }

   

}
