using WarehouseManagement.Core.Common;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.Core.Interfaces;

public interface IItemAppService
{
    Task<Result<ItemDto>> GetItemByIdAsync(int id);
    Task<Result<PagedResult<ItemDto>>> GetItemsAsync(int page, int pageSize, string? search = null, int? categoryId = null);
    Task<Result<ItemDto>> CreateItemAsync(CreateItemRequest request);
    Task<Result<ItemDto>> UpdateItemAsync(int id, UpdateItemRequest request);
    Task<Result> DeleteItemAsync(int id);
    Task<Result<bool>> IsItemCodeUniqueAsync(string code, int? excludeId = null);
    Task<Result<bool>> IsBarcodeUniqueAsync(string barcode, int? excludeId = null);
    Task<Result<List<ItemDto>>> GetItemsByCategoryAsync(int categoryId);
    Task<Result<List<ItemDto>>> SearchItemsAsync(string searchTerm);
}
