{"ast": null, "code": "export const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)\n}, {\n  path: 'security',\n  loadComponent: () => import('./features/security/security-monitoring.component').then(m => m.SecurityMonitoringComponent)\n}, {\n  path: 'login',\n  loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)\n},\n// TODO: Add other feature routes after implementing them\n{\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}