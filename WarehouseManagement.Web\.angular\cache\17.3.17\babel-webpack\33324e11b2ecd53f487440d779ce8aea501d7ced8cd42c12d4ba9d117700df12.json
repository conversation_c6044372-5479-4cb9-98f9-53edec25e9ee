{"ast": null, "code": "// Authentication and Authorization Models\nexport var PermissionAction = /*#__PURE__*/function (PermissionAction) {\n  PermissionAction[\"CREATE\"] = \"create\";\n  PermissionAction[\"READ\"] = \"read\";\n  PermissionAction[\"UPDATE\"] = \"update\";\n  PermissionAction[\"DELETE\"] = \"delete\";\n  PermissionAction[\"MANAGE\"] = \"manage\";\n  return PermissionAction;\n}(PermissionAction || {});\nexport var UserRoleType = /*#__PURE__*/function (UserRoleType) {\n  UserRoleType[\"ADMIN\"] = \"admin\";\n  UserRoleType[\"MANAGER\"] = \"manager\";\n  UserRoleType[\"EMPLOYEE\"] = \"employee\";\n  UserRoleType[\"VIEWER\"] = \"viewer\";\n  return UserRoleType;\n}(UserRoleType || {});\nexport var SecurityEventType = /*#__PURE__*/function (SecurityEventType) {\n  SecurityEventType[\"LOGIN_SUCCESS\"] = \"login_success\";\n  SecurityEventType[\"LOGIN_FAILURE\"] = \"login_failure\";\n  SecurityEventType[\"LOGOUT\"] = \"logout\";\n  SecurityEventType[\"TOKEN_REFRESH\"] = \"token_refresh\";\n  SecurityEventType[\"PASSWORD_CHANGE\"] = \"password_change\";\n  SecurityEventType[\"ACCOUNT_LOCKED\"] = \"account_locked\";\n  SecurityEventType[\"SUSPICIOUS_ACTIVITY\"] = \"suspicious_activity\";\n  SecurityEventType[\"PERMISSION_DENIED\"] = \"permission_denied\";\n  return SecurityEventType;\n}(SecurityEventType || {});\nexport var SecurityEventSeverity = /*#__PURE__*/function (SecurityEventSeverity) {\n  SecurityEventSeverity[\"LOW\"] = \"low\";\n  SecurityEventSeverity[\"MEDIUM\"] = \"medium\";\n  SecurityEventSeverity[\"HIGH\"] = \"high\";\n  SecurityEventSeverity[\"CRITICAL\"] = \"critical\";\n  return SecurityEventSeverity;\n}(SecurityEventSeverity || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}