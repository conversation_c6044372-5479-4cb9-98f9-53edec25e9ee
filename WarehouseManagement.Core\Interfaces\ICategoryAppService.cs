using WarehouseManagement.Core.Common;
using WarehouseManagement.Core.Models;

namespace WarehouseManagement.Core.Interfaces;

public interface ICategoryAppService
{
    Task<Result<CategoryDto>> GetCategoryByIdAsync(int id);
    Task<Result<List<CategoryDto>>> GetAllCategoriesAsync();
    Task<Result<CategoryDto>> CreateCategoryAsync(CreateCategoryRequest request);
    Task<Result<CategoryDto>> UpdateCategoryAsync(int id, UpdateCategoryRequest request);
    Task<Result> DeleteCategoryAsync(int id);
    Task<Result<List<CategoryDto>>> GetCategoriesByParentAsync(int? parentId);
    Task<Result<bool>> IsCategoryNameUniqueAsync(string name, int? excludeId = null);
}
