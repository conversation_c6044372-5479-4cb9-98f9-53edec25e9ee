{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ExclamationTriangleIcon } from 'primeng/icons/exclamationtriangle';\nimport { InfoCircleIcon } from 'primeng/icons/infocircle';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n\n/**\n * Message groups a collection of contents in tabs.\n * @group Components\n */\nfunction UIMessage_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_InfoCircleIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"InfoCircleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_TimesCircleIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesCircleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_ExclamationTriangleIcon_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ExclamationTriangleIcon\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-inline-message-icon\");\n  }\n}\nfunction UIMessage_div_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.text, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction UIMessage_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, UIMessage_div_5_span_1_Template, 1, 1, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.escape);\n  }\n}\nfunction UIMessage_ng_template_6_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.text);\n  }\n}\nfunction UIMessage_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UIMessage_ng_template_6_span_0_Template, 2, 1, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.escape);\n  }\n}\nlet UIMessage = /*#__PURE__*/(() => {\n  class UIMessage {\n    /**\n     * Severity level of the message.\n     * @group Props\n     */\n    severity;\n    /**\n     * Text content.\n     * @group Props\n     */\n    text;\n    /**\n     * Whether displaying messages would be escaped or not.\n     * @group Props\n     */\n    escape = true;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    get icon() {\n      if (this.severity) {\n        return this.severity;\n      } else {\n        return 'info';\n      }\n    }\n    get containerClass() {\n      return {\n        [`p-inline-message-${this.severity}`]: this.severity,\n        'p-inline-message-icon-only': this.text == null\n      };\n    }\n    static ɵfac = function UIMessage_Factory(t) {\n      return new (t || UIMessage)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: UIMessage,\n      selectors: [[\"p-message\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        severity: \"severity\",\n        text: \"text\",\n        escape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"escape\", \"escape\", booleanAttribute],\n        style: \"style\",\n        styleClass: \"styleClass\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      decls: 8,\n      vars: 10,\n      consts: [[\"escapeOut\", \"\"], [\"aria-live\", \"polite\", 1, \"p-inline-message\", \"p-component\", \"p-inline-message\", 3, \"ngStyle\", \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"styleClass\"], [\"class\", \"p-inline-message-text\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"p-inline-message-text\", 3, \"innerHTML\"], [\"class\", \"p-inline-message-text\", 4, \"ngIf\"], [1, \"p-inline-message-text\"]],\n      template: function UIMessage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, UIMessage_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 2)(2, UIMessage_InfoCircleIcon_2_Template, 1, 1, \"InfoCircleIcon\", 2)(3, UIMessage_TimesCircleIcon_3_Template, 1, 1, \"TimesCircleIcon\", 2)(4, UIMessage_ExclamationTriangleIcon_4_Template, 1, 1, \"ExclamationTriangleIcon\", 2)(5, UIMessage_div_5_Template, 2, 1, \"div\", 3)(6, UIMessage_ng_template_6_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const escapeOut_r2 = i0.ɵɵreference(7);\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon === \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon === \"info\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon === \"error\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon === \"warn\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.escape)(\"ngIfElse\", escapeOut_r2);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgStyle, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon],\n      styles: [\"@layer primeng{.p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return UIMessage;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MessageModule = /*#__PURE__*/(() => {\n  class MessageModule {\n    static ɵfac = function MessageModule_Factory(t) {\n      return new (t || MessageModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MessageModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, CheckIcon, InfoCircleIcon, TimesCircleIcon, ExclamationTriangleIcon]\n    });\n  }\n  return MessageModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MessageModule, UIMessage };\n//# sourceMappingURL=primeng-message.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}