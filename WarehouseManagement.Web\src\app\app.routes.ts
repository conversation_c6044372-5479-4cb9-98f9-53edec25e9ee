import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },{
      path: 'security',
      loadComponent: () => import('./features/security/security-monitoring.component').then(m => m.SecurityMonitoringComponent)
  },
  // TODO: Add other feature routes after implementing them
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
