import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },{
      path: 'security',
      loadComponent: () => import('./features/security/security-monitoring.component').then(m => m.SecurityMonitoringComponent)
  },
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  // TODO: Add other feature routes after implementing them
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
