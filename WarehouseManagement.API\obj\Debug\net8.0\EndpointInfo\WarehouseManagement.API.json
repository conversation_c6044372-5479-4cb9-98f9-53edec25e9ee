{"openapi": "3.0.1", "info": {"title": "Warehouse Management API", "description": "A comprehensive warehouse management system API with JWT authentication", "contact": {"name": "Warehouse Management Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/validate": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/confirm-reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfirmResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ConfirmResetPasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Categories": {"get": {"tags": ["Categories"], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Categories/{id}": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Categories/tree": {"get": {"tags": ["Categories"], "responses": {"200": {"description": "Success"}}}}, "/api/Items": {"get": {"tags": ["Items"], "parameters": [{"name": "SearchTerm", "in": "query", "style": "form", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "WarehouseId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "Type", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/ItemType"}}, {"name": "IsActive", "in": "query", "style": "form", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Items"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateItemDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Items/{id}": {"get": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateItemDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateItemDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateItemDto"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Items/by-code/{code}": {"get": {"tags": ["Items"], "parameters": [{"name": "code", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Items/by-category/{categoryId}": {"get": {"tags": ["Items"], "parameters": [{"name": "categoryId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Items/by-warehouse/{warehouseId}": {"get": {"tags": ["Items"], "parameters": [{"name": "warehouseId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Items/{id}/alternatives": {"get": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "post": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddAlternativeItemRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddAlternativeItemRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddAlternativeItemRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Items/{id}/alternatives/{alternativeItemId}": {"delete": {"tags": ["Items"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}, {"name": "alternativeItemId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Security/config": {"get": {"tags": ["Security"], "responses": {"200": {"description": "Success"}}}, "put": {"tags": ["Security"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SecurityConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SecurityConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SecurityConfigDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Security/csrf-token": {"get": {"tags": ["Security"], "responses": {"200": {"description": "Success"}}}}, "/api/Security/events": {"post": {"tags": ["Security"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogSecurityEventRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LogSecurityEventRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LogSecurityEventRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Security"], "parameters": [{"name": "page", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "eventType", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/SecurityEventType"}}, {"name": "severity", "in": "query", "style": "form", "schema": {"$ref": "#/components/schemas/SecurityEventSeverity"}}, {"name": "fromDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Security/login-attempts": {"post": {"tags": ["Security"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogLoginAttemptRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LogLoginAttemptRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LogLoginAttemptRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "get": {"tags": ["Security"], "parameters": [{"name": "page", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "isSuccessful", "in": "query", "style": "form", "schema": {"type": "boolean"}}, {"name": "fromDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "style": "form", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Security/rate-limit/{action}/{identifier}": {"get": {"tags": ["Security"], "parameters": [{"name": "action", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}, {"name": "identifier", "in": "path", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AddAlternativeItemRequest": {"type": "object", "properties": {"alternativeItemId": {"type": "integer", "format": "int32"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordRequest": {"required": ["confirmPassword", "currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"maxLength": 100, "minLength": 8, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ConfirmResetPasswordRequest": {"required": ["confirmPassword", "newPassword", "token"], "type": "object", "properties": {"token": {"minLength": 1, "type": "string"}, "newPassword": {"maxLength": 100, "minLength": 8, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateCategoryDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentCategoryId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateItemDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "unitOfMeasurementId": {"type": "integer", "format": "int32"}, "warehouseId": {"type": "integer", "format": "int32"}, "storageLocation": {"type": "string", "nullable": true}, "openingBalance": {"type": "number", "format": "double"}, "minimumOrderLimit": {"type": "number", "format": "double"}, "maximumOrderLimit": {"type": "number", "format": "double"}, "unitCost": {"type": "number", "format": "double"}, "sellingPrice": {"type": "number", "format": "double"}, "type": {"$ref": "#/components/schemas/ItemType"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "ItemType": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "LogLoginAttemptRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "isSuccessful": {"type": "boolean"}, "failureReason": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "LogSecurityEventRequest": {"type": "object", "properties": {"eventType": {"$ref": "#/components/schemas/SecurityEventType"}, "description": {"type": "string", "nullable": true}, "severity": {"$ref": "#/components/schemas/SecurityEventSeverity"}, "additionalData": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"required": ["password", "username"], "type": "object", "properties": {"username": {"maxLength": 50, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 0, "type": "string"}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "PasswordPolicyDto": {"type": "object", "properties": {"minLength": {"type": "integer", "format": "int32"}, "requireUppercase": {"type": "boolean"}, "requireLowercase": {"type": "boolean"}, "requireNumbers": {"type": "boolean"}, "requireSpecialChars": {"type": "boolean"}, "preventCommonPasswords": {"type": "boolean"}}, "additionalProperties": false}, "RateLimitConfigDto": {"type": "object", "properties": {"maxAttempts": {"type": "integer", "format": "int32"}, "windowMinutes": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ResetPasswordRequest": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}}, "additionalProperties": false}, "SecurityConfigDto": {"type": "object", "properties": {"maxLoginAttempts": {"type": "integer", "format": "int32"}, "lockoutDurationMinutes": {"type": "integer", "format": "int32"}, "tokenExpirationMinutes": {"type": "integer", "format": "int32"}, "refreshTokenExpirationDays": {"type": "integer", "format": "int32"}, "passwordPolicy": {"$ref": "#/components/schemas/PasswordPolicyDto"}, "rateLimits": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/RateLimitConfigDto"}, "nullable": true}}, "additionalProperties": false}, "SecurityEventSeverity": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "SecurityEventType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "UpdateCategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentCategoryId": {"type": "integer", "format": "int32", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateItemDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "unitOfMeasurementId": {"type": "integer", "format": "int32"}, "warehouseId": {"type": "integer", "format": "int32"}, "storageLocation": {"type": "string", "nullable": true}, "minimumOrderLimit": {"type": "number", "format": "double"}, "maximumOrderLimit": {"type": "number", "format": "double"}, "unitCost": {"type": "number", "format": "double"}, "sellingPrice": {"type": "number", "format": "double"}, "type": {"$ref": "#/components/schemas/ItemType"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Enter 'Bearer' [space] and then your token in the text input below.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}